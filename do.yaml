alerts:
- rule: DEPLOYMENT_FAILED
- rule: DOMAIN_FAILED
features:
- buildpack-stack=ubuntu-22
ingress:
  rules:
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /api
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /health
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /admin
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /static
  - component:
      name: goali-backend
      preserve_path_prefix: true
    match:
      path:
        prefix: /ws
  - component:
      name: goali-frontend
    match:
      path:
        prefix: /

name: monkfish-app
region: lon
services:
- name: goali-backend
  environment_slug: python
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DEFAULT_LLM_MODEL_NAME
    scope: RUN_AND_BUILD_TIME
    value: mistral-small-latest
  - key: DEFAULT_LLM_TEMPERATURE
    scope: RUN_AND_BUILD_TIME
    value: "0.7"
  - key: DJANGO_ADMIN_PASSWORD
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: STATIC_ROOT
    scope: RUN_AND_BUILD_TIME
    value: /app/staticfiles
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  github:
    branch: main
    deploy_on_push: true
    repo: elgui/goali
  http_port: 8080
  instance_count: 2
  instance_size_slug: apps-s-1vcpu-1gb
  run_command: python manage.py collectstatic --noinput && python manage.py migrate --noinput && python manage.py shell -c "from django.contrib.auth import get_user_model; import os; User = get_user_model(); User.objects.create_superuser('admin', '<EMAIL>', os.environ.get('DJANGO_ADMIN_PASSWORD', 'defaultpassword')) if not User.objects.filter(username='admin').exists() else None" && python manage.py create_anonymous_user && python manage.py start_production
  source_dir: backend

workers:
# Celery worker for background task processing
- name: celery-worker
  github:
    repo: elgui/goali
    branch: main
    deploy_on_push: true
  source_dir: backend
  environment_slug: python
  run_command: python manage.py fix_migration_state && python manage.py migrate --noinput && python manage.py cmd_register_tools && python manage.py cmd_tool_connect --reset && celery -A config worker --loglevel=debug --concurrency=1
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:fOT1spL8aMqulKRDB3mqegtnDenVz9bs:1Ycxz/cXHRl3YV30PfVWqpgx9gEbEA44MoBgXn58nQazUSjYBYzIg7hAg/VuWShwvtTgQps1JFKPEbm6WcghiFjQmU4=]
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:ozWac/j5PFsbweHDnazsxKtUIVL7RCgQ:ZDf9uMMDmsWM79OfG5z9Rfjmafl5y2osvHA7Y9Njhjy9nJ2kWsMtWblkFa+9W8Tz]
  - key: DEFAULT_LLM_MODEL_NAME
    scope: RUN_AND_BUILD_TIME
    value: mistral-small-latest
  - key: DEFAULT_LLM_TEMPERATURE
    scope: RUN_AND_BUILD_TIME
    value: "0.7"
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
    value: EV[1:4M7RY7uDcyYqa1mz1FgYdbb2jQyq+SD4:lAHqDaLVw1kVaytnVH1sFiuhNkhrS7d2d5sLKm6V61KGud4PeT7OV6JAJxkeOM1I+dbIJu28pJxfB8hnZ9nuDKLW8eDM0fLcJ6EdNQnM2RnrSEd+QbCsrc6mnqNFtUEABsPEM7CynxIvKKC/3j+/JRhB2oNeHS+xBUrOqmjsJkKeiDrxi6HzE9mFxRJKx2WeH72O588=]
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    value: rediss://default:<EMAIL>:25061/0
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app

static_sites:
- build_command: npm ci && ./generate-config.sh && npm run build:prod
  envs:
  - key: VITE_SECURITY_REQUIRE_AUTH
    scope: BUILD_TIME
    value: "true"
  - key: VITE_SECURITY_TOKEN_VALIDATION
    scope: BUILD_TIME
    value: "true"
  - key: VITE_SECURITY_SESSION_TIMEOUT
    scope: BUILD_TIME
    value: "1800000"
  github:
    branch: main
    deploy_on_push: true
    repo: elgui/goali
  name: goali-frontend
  output_dir: /dist
  source_dir: frontend

