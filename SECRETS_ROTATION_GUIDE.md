# 🔐 Secrets Rotation Guide

This guide provides step-by-step instructions for rotating all secrets and credentials in the Goali application deployed on DigitalOcean App Platform.

## 📋 Overview

The following secrets need to be rotated periodically for security:

- **Django Secret Key** - Used for cryptographic signing
- **Django Admin Password** - Admin user password
- **Database Credentials** - PostgreSQL connection string
- **Redis/Valkey Credentials** - Cache and message broker
- **Mistral API Key** - AI service integration *(Section left empty as requested)*

## 🚨 Pre-Rotation Checklist

Before starting the rotation process:

1. **Schedule maintenance window** - Inform users of potential downtime
2. **Backup current configuration** - Export current `do.yaml` and note current secret values
3. **Prepare new credentials** - Generate all new secrets before starting
4. **Test environment ready** - Ensure you can test changes before applying to production

## 🔄 Rotation Procedures

### 1. Django Secret Key

**Purpose**: Used for cryptographic signing of sessions, cookies, and tokens.

**Steps**:
1. **Generate new secret key**:
   ```python
   python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
   ```

2. **Update in DigitalOcean**:
   - Go to DigitalOcean App Platform → Your App → Settings → Environment Variables
   - Find `DJANGO_SECRET_KEY` in both `goali-backend` and `celery-worker` services
   - Click "Edit" and replace with new value
   - Ensure type is set to "SECRET"

3. **Deploy changes**:
   - Click "Save" to trigger automatic redeployment
   - Monitor deployment logs for any issues

**⚠️ Impact**: Will invalidate all existing user sessions. Users will need to log in again.

### 2. Django Admin Password

**Purpose**: Password for the Django admin user account.

**Steps**:
1. **Generate new password**:
   ```bash
   # Generate a secure password
   openssl rand -base64 32
   ```

2. **Update in DigitalOcean**:
   - Go to App Platform → Settings → Environment Variables
   - Find `DJANGO_ADMIN_PASSWORD` in `goali-backend` service
   - Update with new password
   - Ensure type is set to "SECRET"

3. **Verify access**:
   - After deployment, test admin login at `/admin/`
   - Username: `admin`, Password: `<new_password>`

**⚠️ Impact**: Admin user will need to use new password for Django admin access.

### 3. Database Credentials (PostgreSQL)

**Purpose**: Connection credentials for the managed PostgreSQL database.

**Steps**:
1. **Reset password in DigitalOcean Database**:
   - Go to DigitalOcean Control Panel → Databases → Your PostgreSQL cluster
   - Click on the "Users & Databases" tab
   - Find the database user (typically `doadmin` for the default admin user)
   - Click the **three dots (⋯)** next to the user → Select **"Reset password"**
   - DigitalOcean will generate a new random password automatically
   - Copy the new connection string from the cluster overview page

2. **Alternative - Create new user**:
   - In the "Users & Databases" tab, enter a new username in "Add new user" field
   - Click "Save" to create the user with a generated password
   - Grant necessary privileges to the new user
   - Use the new user's credentials instead of the old ones

3. **Update DATABASE_URL**:
   - Format: `postgresql://username:password@host:port/database?sslmode=require`
   - Go to App Platform → Settings → Environment Variables
   - Update `DATABASE_URL` in both `goali-backend` and `celery-worker`
   - Ensure type is set to "SECRET"

4. **Test connection**:
   - Deploy and monitor logs for database connection success
   - Verify application functionality

**⚠️ Impact**: Brief downtime during credential update. Database connections will be reset.

**Note**: You cannot change the `doadmin` password manually - only reset it to a new random password via DigitalOcean's interface.

### 4. Redis/Valkey Credentials

**Purpose**: Cache and Celery message broker credentials.

**Steps**:
TODO


## ✅ Post-Rotation Verification

After rotating secrets, verify the following:

### Application Health
```bash
# Check deployment status
doctl apps get $APP_ID

# Check application logs
doctl apps logs $APP_ID --type=run --tail=50

# Test health endpoints
curl https://monkfish-app-jvgae.ondigitalocean.app/health/
```

### Service Functionality
1. **Frontend loads correctly** - Visit the application URL
2. **User authentication works** - Test login/logout
3. **Admin access works** - Login to `/admin/` with new password
4. **Database operations work** - Create/read data
5. **Background tasks work** - Verify Celery processing
6. **AI features work** - Test Mistral integration (if applicable)

### Security Verification
1. **Old sessions invalidated** - Previous user sessions should be logged out
2. **Secrets encrypted** - Verify secrets show as `[ENCRYPTED]` in DigitalOcean UI
3. **No plaintext secrets** - Check that no secrets are visible in logs

## 🚨 Emergency Rollback

If rotation causes issues:

1. **Immediate rollback**:
   - Revert to previous secret values in DigitalOcean UI
   - Trigger redeployment
   - Monitor for service restoration

2. **Database connection issues**:
   - Check DATABASE_URL format and credentials
   - Verify database user permissions
   - Check database firewall rules

3. **Redis connection issues**:
   - Verify REDIS_URL format and credentials
   - Check Valkey cluster status
   - Restart Celery workers if needed

## 📅 Rotation Schedule

**Recommended rotation frequency**:
- **Django Secret Key**: Every 6 months
- **Admin Password**: Every 3 months  
- **Database Credentials**: Every 6 months
- **Redis Credentials**: Every 6 months
- **API Keys**: As required by provider or security policy

## 🔒 Security Best Practices

1. **Never commit secrets** to version control
2. **Use DigitalOcean SECRET type** for all sensitive values
3. **Rotate during low-traffic periods** to minimize user impact
4. **Test in staging environment** before production rotation
5. **Document rotation dates** for compliance tracking
6. **Monitor for failed authentication** attempts after rotation
7. **Keep backup of working configuration** until new setup is verified

## 📞 Support

If you encounter issues during rotation:
- Check DigitalOcean App Platform logs
- Verify secret formatting and encoding
- Ensure all services have the updated credentials
- Contact DigitalOcean support for platform-specific issues
