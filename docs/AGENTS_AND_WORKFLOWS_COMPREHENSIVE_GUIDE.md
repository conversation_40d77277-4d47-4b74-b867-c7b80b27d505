# Goali Agents and Workflows: Technical Architecture Guide

*Last Updated: 2025-06-11*

## 🎯 **SYSTEM STATUS: FULLY OPERATIONAL (2025-06-11)**

### ✅ **ALL CRITICAL ISSUES RESOLVED**
- **Frontend-Backend Integration**: ✅ Complete message flow working perfectly
- **Chat UI**: ✅ Proper AI responses and wheel generation confirmed
- **Dashboard Statistics**: ✅ Receiving correct data from backend
- **Debug Message Display**: ✅ Clean display without "undefined" prefix
- **Wheel Activity Names**: ✅ Proper titles instead of generic names
- **User Profile Retrieval**: ✅ All domain relationship errors fixed
- **WebSocket Communication**: ✅ All message types handled correctly

### 📊 **Comprehensive Testing Results**
- **Backend Functionality**: 100% Working (wheel generation, AI responses, workflows)
- **Frontend Chat UI**: 100% Working (proper message display, no empty bubbles)
- **Connection Dashboard**: 100% Working (statistics showing legitimate values)
- **Agent Communication**: 100% Working (all 9 agents executing properly)
- **Workflow Execution**: 100% Working (complete user story flow validated)

## Overview

This document provides comprehensive technical documentation for Goali's multi-agent system architecture. It serves as the authoritative reference for understanding how agents and workflows operate, interact, and are managed by different system entities.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Agent Architecture](#agent-architecture)
3. [Workflow Architecture](#workflow-architecture)
4. [Data Flow and Communication](#data-flow-and-communication)
5. [Execution Entities](#execution-entities)
6. [Agent Specifications](#agent-specifications)
7. [Workflow Specifications](#workflow-specifications)
8. [Technical Implementation](#technical-implementation)
9. [Debugging and Monitoring](#debugging-and-monitoring)

---

## System Architecture

### Core Principles

Goali is built on a **multi-agent orchestration system** with the following architectural principles:

- **Agent Specialization**: Each agent has a focused responsibility and domain expertise
- **Workflow Orchestration**: LangGraph coordinates agent sequences for complex objectives
- **State Management**: Centralized state tracking with agent-specific updates
- **Communication Tracking**: Comprehensive monitoring of agent interactions and data flow
- **Execution Flexibility**: Support for real, mock, and hybrid execution modes

### System Components

```
┌─────────────────────────────────────────────────────────────────┐
│                    GOALI MULTI-AGENT SYSTEM                    │
├─────────────────────────────────────────────────────────────────┤
│  ConversationDispatcher (Entry Point)                          │
│  ├─ Message Classification & Context Extraction                │
│  ├─ Workflow Selection & Routing                               │
│  └─ WebSocket Session Management                               │
├─────────────────────────────────────────────────────────────────┤
│  MentorService (Singleton per User)                            │
│  ├─ User-facing Communication Hub                              │
│  ├─ Cross-workflow State Persistence                           │
│  ├─ Trust-based Adaptation                                     │
│  └─ Message Pre/Post-processing                                │
├─────────────────────────────────────────────────────────────────┤
│  LangGraph Workflow Engine                                     │
│  ├─ Agent Orchestration                                        │
│  ├─ State Transitions                                          │
│  ├─ Error Handling & Recovery                                  │
│  └─ Execution Mode Management                                  │
├─────────────────────────────────────────────────────────────────┤
│  Agent Ecosystem (9 Specialized Agents)                        │
│  ├─ Orchestrator Agent (Coordination)                          │
│  ├─ Resource Agent (Environment & Constraints)                 │
│  ├─ Engagement Agent (Historical Patterns)                     │
│  ├─ Psychological Agent (State Assessment)                     │
│  ├─ Strategy Agent (Decision Synthesis)                        │
│  ├─ Activity Agent (Content Generation)                        │
│  ├─ Ethical Agent (Safety Validation)                          │
│  └─ Mentor Agent (User Interface)                              │
├─────────────────────────────────────────────────────────────────┤
│  Communication & Monitoring Layer                              │
│  ├─ AgentCommunicationTracker                                  │
│  ├─ Performance Metrics Collection                             │
│  ├─ Tool Call Monitoring                                       │
│  └─ Error Context Tracking                                     │
└─────────────────────────────────────────────────────────────────┘
```

---

## Agent Architecture

### Agent Base Class Structure

All agents inherit from `BaseAgent` which provides:

- **Execution Framework**: Async processing with state management
- **Database Integration**: Real and mock database service support
- **Performance Tracking**: Built-in timing and metrics collection
- **Error Handling**: Standardized error recovery and reporting
- **Tool Integration**: Unified tool calling interface
- **Communication Tracking**: Automatic agent communication logging

### Agent Lifecycle

```python
class BaseAgent:
    async def process(self, state: StateT) -> Dict[str, Any]:
        """
        Standard agent processing lifecycle:
        1. Initialize execution context
        2. Extract relevant state data
        3. Execute agent-specific logic
        4. Generate output data
        5. Update state with results
        6. Track performance metrics
        7. Handle errors gracefully
        """
```

### Agent Communication Protocol

Agents communicate through a standardized protocol managed by `AgentCommunicationTracker`:

**Input Structure**:
```json
{
  "context_packet": {
    "user_profile_id": "string",
    "session_timestamp": "ISO-8601",
    "reported_environment": "string",
    "reported_time_availability": "string",
    "user_mood": "string",
    "user_energy": "string"
  }
}
```

**Output Structure**:
```json
{
  "output_data": {
    "agent_specific_data": {},
    "next_agent": "string",
    "confidence_score": "float",
    "processing_metadata": {}
  },
  "run_id": "uuid",
  "performance_metrics": {
    "duration_ms": "float",
    "token_usage": {"input": "int", "output": "int"},
    "tool_call_count": "int",
    "success": "boolean"
  }
}
```

### Agent State Management

Each agent updates the workflow state through role-specific fields:

- **Mentor**: `context_packet` (enhanced user context)
- **Resource**: `resource_context` (environment and constraints)
- **Engagement**: `engagement_analysis` (historical patterns)
- **Psychological**: `psychological_assessment` (current state)
- **Strategy**: `strategy_framework` (decision synthesis)
- **Activity**: `wheel` (generated activities)
- **Ethical**: `ethical_validation` (safety approval)

---

## Workflow Architecture

### LangGraph Integration

Workflows are implemented using LangGraph, providing:

- **State-based Execution**: Centralized state management across agent transitions
- **Conditional Routing**: Dynamic agent sequencing based on context and results
- **Error Recovery**: Built-in retry mechanisms and fallback strategies
- **Parallel Execution**: Support for concurrent agent operations where appropriate
- **Checkpointing**: State persistence for long-running workflows

### Workflow State Structure

```python
@dataclass
class WorkflowState:
    # Core Context
    context_packet: Dict[str, Any]
    workflow_id: str
    user_profile_id: str

    # Agent Outputs
    resource_context: Optional[Dict[str, Any]] = None
    engagement_analysis: Optional[Dict[str, Any]] = None
    psychological_assessment: Optional[Dict[str, Any]] = None
    strategy_framework: Optional[Dict[str, Any]] = None
    wheel: Optional[Dict[str, Any]] = None
    ethical_validation: Optional[Dict[str, Any]] = None

    # Execution Metadata
    current_agent: Optional[str] = None
    execution_path: List[str] = field(default_factory=list)
    error_context: Optional[Dict[str, Any]] = None

    # Performance Tracking
    start_time: Optional[str] = None
    agent_durations: Dict[str, float] = field(default_factory=dict)
    total_tokens: Dict[str, int] = field(default_factory=dict)
```

### Workflow Execution Modes

**Real Mode**:
- Live LLM API calls with actual model inference
- Real database queries and updates
- Actual tool executions with external services
- Full token consumption and cost tracking

**Mock Mode**:
- Simulated LLM responses using predefined templates
- Mock database operations with test data
- Simulated tool outputs for consistent testing
- Zero external API costs

**Hybrid Mode**:
- Selective real/mock execution per component
- Useful for debugging specific agent interactions
- Controlled cost management during development

---

## Data Flow and Communication

### Agent Communication Tracking

The system uses `AgentCommunicationTracker` to monitor all agent interactions:

**Data Captured**:
- **Agent Executions**: Complete input/output data for each agent
- **State Transitions**: Before/after workflow state for each execution
- **Performance Metrics**: Timing, token usage, tool calls, success rates
- **Tool Call Details**: Individual tool executions with parameters and results
- **LLM Interactions**: Prompts, responses, token counts, model parameters
- **Error Contexts**: Detailed error information with recovery attempts

**Storage Structure**:
```json
{
  "enabled": true,
  "workflow_id": "uuid",
  "agents": [
    {
      "agent": "resource",
      "stage": "resource_assessment",
      "input_data": {},
      "output_data": {
        "combined_resource_context": {},
        "feasibility_score": 0.85
      },
      "timestamp": "ISO-8601",
      "duration_ms": 1250.5,
      "success": true,
      "execution_context": {
        "tool_calls": [],
        "processing_steps": [],
        "execution_mode": {"real_llm": true, "real_db": true}
      },
      "performance_metrics": {
        "token_usage": {"input": 150, "output": 75},
        "tool_call_count": 3
      }
    }
  ],
  "state_transitions": [],
  "summary": {"total_communications": 6}
}
```

### Data Flow Issues and Solutions

**Problem Identified**: Modal display functions (`extractAgentOutputData`) were receiving poor data due to data structure mismatches between:
1. Rich agent `output_data` (e.g., `combined_resource_context`)
2. AgentCommunicationTracker storage format
3. BenchmarkRun database storage
4. Modal extraction logic

**Solution Implemented**: Enhanced extraction functions to check multiple data paths:
- Direct `output_data` field (new format)
- Legacy `output` field (older formats)
- Nested agent communications structures
- BenchmarkRun raw results paths
- **NEW**: Enhanced debugging data structure access (`enhanced_debugging_data.agents[]`)
- Graceful fallback to performance metrics

**Result**: Admin interface now displays rich agent output data instead of generic performance metrics and correctly shows "Real Mode" vs "Mock Mode" execution status.

### Agent Output Data Standards for Admin Interface

All agents must comply with standardized output data structures to ensure proper display in the benchmark history admin interface. The output data should be rich and meaningful for debugging purposes.

#### Data Storage Locations

Agent output data is stored in two main locations within benchmark results:

1. **agent_communications.agents[]** - Contains structured agent communication data with `output_data` field
2. **enhanced_debugging_data.agents[]** - Contains detailed debugging information with rich `output` field (prioritized by admin interface)

#### Required Agent-Specific Output Fields

Each agent must include its specialized output data:

- **Resource Agent**: `combined_resource_context` - Complete environmental and resource analysis
- **Engagement Agent**: `engagement_analysis` - Historical pattern analysis and recommendations
- **Psychological Agent**: `psychological_assessment` - Current state and trust evaluation
- **Strategy Agent**: `strategy_framework` - Comprehensive strategy synthesis
- **Activity Agent**: `wheel` - Complete wheel structure with activities and metadata
- **Ethical Agent**: `ethical_validation` - Validation results and safety considerations

#### Execution Mode Tracking

All agents must properly report their execution mode in the `execution_context.processing_steps` with:

```python
{
    "description": "enhanced_tracking_setup",
    "data": {
        "execution_mode": {
            "real_db": bool,
            "real_llm": bool,
            "real_tools": bool
        }
    }
}
```

This ensures the admin interface displays "Real Mode" vs "Mock Mode" correctly.

---

## Execution Entities

### ConversationDispatcher

**Role**: System entry point and workflow orchestrator

**Responsibilities**:
- Message classification and intent detection
- Context extraction from user input
- Workflow selection and routing
- WebSocket session management
- Integration with MentorService

**Technical Implementation**:
```python
class ConversationDispatcher:
    async def process_message(self, message: str, user_id: str) -> Dict[str, Any]:
        # 1. Extract context (mood, environment, time)
        # 2. Classify intent (wheel_generation, feedback, discussion)
        # 3. Route to appropriate workflow
        # 4. Return structured response
```

### MentorService (Singleton)

**Role**: Per-user singleton managing Mentor state and communication

**Architecture**:
- **Singleton Pattern**: One instance per user profile
- **State Persistence**: Trust levels, preferences, conversation context
- **Message Processing**: Pre/post-workflow message enhancement
- **Cross-workflow Continuity**: Maintains context across workflow boundaries

**Integration Points**:
- ConversationDispatcher routes all messages through MentorService
- Workflow results processed by MentorService before user delivery
- State management independent of individual workflow execution

### LangGraph Workflow Engine

**Role**: Agent orchestration and state management

**Capabilities**:
- **Dynamic Routing**: Conditional agent sequencing based on context
- **State Persistence**: Centralized workflow state management
- **Error Recovery**: Built-in retry and fallback mechanisms
- **Performance Tracking**: Execution timing and resource monitoring

### AgentCommunicationTracker

**Role**: Comprehensive monitoring of agent interactions

**Data Collection**:
- Agent input/output data with rich context
- Tool call details with execution metadata
- LLM interaction logs with token tracking
- Performance metrics and timing data
- Error contexts with recovery information

**Storage Integration**:
- Real-time data collection during workflow execution
- Structured export to BenchmarkRun.agent_communications
- Support for both real and mock execution modes

---

## Agent Specifications

### Agent Classification

Agents are classified into three categories based on their operational characteristics:

**System Agents** (Infrastructure):
- ConversationDispatcher: Entry point and routing
- Orchestrator Agent: Workflow coordination

**Analysis Agents** (Data Processing):
- Resource Agent: Environment and constraint analysis
- Engagement Agent: Historical pattern analysis
- Psychological Agent: State assessment
- Strategy Agent: Decision synthesis

**Action Agents** (Content Generation):
- Activity Agent: Content creation and customization
- Ethical Agent: Safety validation
- Mentor Agent: User communication

### 1. Orchestrator Agent

**Role**: Workflow coordination and state management

**Technical Specifications**:
- **File**: `backend/apps/main/agents/orchestrator_agent.py`
- **Base Class**: `BaseAgent`
- **Execution Mode**: Real/Mock LLM support
- **State Updates**: Coordinates all agent outputs

**Key Responsibilities**:
- Agent sequence determination based on context
- State distribution to specialized agents
- Conflict resolution using priority framework
- Error handling and workflow recovery
- Performance monitoring and optimization

**Input/Output**:
```python
# Input: WorkflowState with context_packet
# Output: Orchestrated agent execution plan
{
  "agent_sequence": ["resource", "engagement", "psychological", "strategy", "activity", "ethical"],
  "context_distribution": {...},
  "execution_metadata": {...}
}
```

### 2. Resource Agent

**Role**: Environment and resource constraint analysis

**Technical Specifications**:
- **File**: `backend/apps/main/agents/resource_agent.py`
- **Tools**: `get_environment_context`, `parse_time_availability`, `get_available_resources`
- **Output**: `combined_resource_context` with comprehensive analysis

**Key Responsibilities**:
- Environmental context analysis (privacy, space, noise)
- Time availability parsing and categorization
- Resource inventory assessment
- Constraint identification and impact analysis
- Feasibility scoring and recommendations

**Enhanced Output Structure**:
```python
{
  "combined_resource_context": {
    "environment": {
      "current_environment": {"name": "string", "code": "string"},
      "privacy_level": "int (0-100)",
      "space_size": "string",
      "noise_level": "string",
      "social_context": {...},
      "activity_support": {...}
    },
    "time": {
      "duration_minutes": "int",
      "flexibility": "string",
      "urgency": "string"
    },
    "resources": {
      "inventory_count": "int",
      "limitations_count": "int",
      "capabilities_summary": {...}
    },
    "analysis_summary": {
      "overall_feasibility_score": "float (0-1)",
      "primary_constraints": ["string"],
      "key_opportunities": ["string"],
      "recommended_activity_types": ["string"]
    }
  }
}
```

### 3. Engagement Agent

**Role**: Historical pattern analysis and user preference extraction

**Technical Specifications**:
- **File**: `backend/apps/main/agents/engagement_agent.py`
- **Tools**: `analyze_domain_engagement`, `analyze_completion_patterns`, `analyze_temporal_patterns`
- **Output**: `engagement_analysis` with behavioral insights

**Key Responsibilities**:
- Historical interaction pattern analysis
- Domain preference extraction and scoring
- Completion vs abandonment pattern detection
- Temporal pattern identification
- Behavioral insight generation and recommendations

### 4. Psychological Agent

**Role**: Psychological state assessment and challenge calibration

**Technical Specifications**:
- **File**: `backend/apps/main/agents/psychological_agent.py`
- **Tools**: `assess_current_mood`, `analyze_trust_phase`, `evaluate_hexaco_traits`
- **Output**: `psychological_assessment` with state evaluation

**Key Responsibilities**:
- Current psychological state assessment
- Trust phase determination (Foundation/Expansion)
- HEXACO trait analysis and expression patterns
- Growth opportunity identification
- Challenge calibration recommendations

### 5. Strategy Agent

**Role**: Multi-agent input synthesis and decision framework

**Technical Specifications**:
- **File**: `backend/apps/main/agents/strategy_agent.py`
- **Input**: All previous agent outputs
- **Output**: `strategy_framework` with integrated recommendations

**Key Responsibilities**:
- Multi-agent input integration and synthesis
- Gap analysis between traits and requirements
- Domain distribution planning and optimization
- Challenge calibration strategy development
- Growth pathway definition and prioritization

### 6. Activity Agent

**Role**: Content generation and activity customization

**Technical Specifications**:
- **File**: `backend/apps/main/agents/wheel_activity_agent.py`
- **Tools**: `select_activities`, `tailor_activity`, `create_wheel_structure`
- **Output**: `wheel` with complete activity package

**Key Responsibilities**:
- Activity selection from GenericActivity catalog
- Context-aware activity customization and tailoring
- Wheel construction with probability weights
- Value proposition development
- Resource requirement matching and adaptation

**Enhanced Output Structure**:
```python
{
  "wheel": {
    "wheel_id": "uuid",
    "activities": [
      {
        "activity_id": "uuid",
        "title": "Personalized Activity Title",
        "description": "Context-aware description",
        "instructions": "Detailed, tailored instructions",
        "domain": "wellness|creativity|physical|learning|social",
        "duration_minutes": "int",
        "difficulty_level": "int (1-5)",
        "value_proposition": "Specific benefits for user",
        "resource_requirements": [...],
        "personalization_metadata": {...}
      }
    ],
    "metadata": {
      "total_activities": "int",
      "domain_distribution": {...},
      "personalization_quality": "float (0-1)"
    }
  }
}
```

### 7. Ethical Agent

**Role**: Safety validation and ethical oversight

**Technical Specifications**:
- **File**: `backend/apps/main/agents/ethical_agent.py`
- **Tools**: `validate_activity_safety`, `check_ethical_alignment`
- **Output**: `ethical_validation` with approval status

**Key Responsibilities**:
- Activity ethical alignment review
- Psychological safety validation
- Challenge level appropriateness verification
- Transparency and autonomy confirmation
- Safety boundary enforcement

### 8. Mentor Agent & MentorService

**Role**: User-facing communication and singleton state management

**Technical Specifications**:
- **File**: `backend/apps/main/agents/mentor_agent.py`
- **Service**: `backend/apps/main/services/mentor_service.py`
- **Architecture**: Singleton pattern per user profile

**Key Responsibilities**:
- Natural language conversation management
- Trust-based communication adaptation
- Activity presentation and explanation
- Cross-workflow state persistence
- System-level user communication coordination

**MentorService Architecture**:
- **Singleton Pattern**: One instance per user profile
- **State Management**: Persistent conversation context and preferences
- **Message Processing**: Pre/post-workflow message enhancement
- **Integration**: Routes through ConversationDispatcher

---

## Workflow Specifications

### Primary Workflows

**Wheel Generation Workflow**:
- **File**: `backend/apps/main/workflows/wheel_generation_graph.py`
- **Agents**: Orchestrator → Resource → Engagement → Psychological → Strategy → Activity → Ethical → Mentor
- **Purpose**: Generate personalized activity wheels
- **Output**: Complete wheel package with user-facing message

**Discussion Workflow**:
- **Agents**: Mentor (primary) with optional specialist consultation
- **Purpose**: Handle conversational interactions and feedback
- **Output**: Contextual responses and state updates

**Feedback Processing Workflow**:
- **Agents**: Engagement → Psychological → Mentor
- **Purpose**: Process activity feedback and update user models
- **Output**: Updated preferences and acknowledgment

### Workflow Execution Patterns

**Sequential Execution**: Standard agent chain with state passing
**Conditional Routing**: Dynamic agent selection based on context
**Parallel Processing**: Concurrent execution where dependencies allow
**Error Recovery**: Automatic retry and fallback mechanisms

### Primary Workflow Types

| Workflow            | Purpose                                          | Duration | Key Agents                       |
| ------------------- | ------------------------------------------------ | -------- | -------------------------------- |
| **wheel_generation**  | Create personalized activity suggestions       | ~15s     | All agents in sequence           |
| **discussion**        | Handle general conversation and clarification    | Variable | Mentor, Orchestrator             |
| **onboarding**        | Process new user questionnaire into profile      | ~20s     | All agents for profile creation  |
| **activity_feedback** | Process activity completion feedback             | ~10s     | Mentor, Engagement Analytics     |
| **post_spin**         | Handle activity selection from wheel             | ~8s      | Mentor, Orchestrator             |
| **pre_spin_feedback** | Collect context before wheel generation        | ~5s      | Mentor, Orchestrator             |

### Workflow Architecture Patterns

#### 1. Sequential Processing Pattern
Used in **wheel_generation** and **onboarding**:
```
Orchestrator → Resource → Engagement → Psychological → Strategy → Activity → Ethical → Orchestrator
```

#### 2. Conversational Loop Pattern
Used in **discussion** workflow:
```
Orchestrator → Mentor ⟲ (until goal met) → Orchestrator → Termination
```

#### 3. Feedback Processing Pattern
Used in **activity_feedback**:
```
Orchestrator → Engagement Analytics → Psychological → Mentor → Orchestrator
```

### Workflow State Management

Each workflow maintains state through LangGraph's StateGraph with standardized state models:

```python
class WorkflowState(BaseModel):
    workflow_id: str
    user_profile_id: str
    user_ws_session_name: Optional[str]
    context_packet: Dict[str, Any]
    
    # Agent outputs
    resource_context: Optional[Dict[str, Any]]
    engagement_analysis: Optional[Dict[str, Any]]
    psychological_assessment: Optional[Dict[str, Any]]
    strategy_framework: Optional[Dict[str, Any]]
    wheel: Optional[Dict[str, Any]]
    ethical_validation: Optional[Dict[str, Any]]
    
    # State tracking
    current_stage: WorkflowStage
    next_agent: Optional[str]
    completed: bool
    error: Optional[str]
```

---

## Technical Implementation

### LangGraph Integration

*   **StateGraph**: Defines workflow structure and agent transitions
*   **Conditional Edges**: Route execution based on state conditions
*   **Agent Nodes**: Encapsulate individual agent logic
*   **Memory Management**: Persistent and transient memory systems

### Agent Base Class Pattern

```python
class LangGraphAgent:
    async def process(self, state: WorkflowState) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        # Extract state information
        # Process based on agent's responsibilities  
        # Return output_data and state_updates
        return output_data, state_updates
```

### Asynchronous Processing

*   **Celery Tasks**: Handle long-running workflow execution
*   **WebSocket Integration**: Real-time communication with frontend
*   **Result Handling**: Automatic delivery of workflow results
*   **Error Recovery**: Graceful handling of failures and timeouts

---

## Data Flow

### Message Processing Flow

1.  **User Input** → WebSocket → UserSessionConsumer
2.  **Message Echo** → Immediate user feedback
3.  **MentorService Pre-Processing** → Message enhancement with Mentor context
4.  **ConversationDispatcher** → Context extraction and classification from enhanced message
5.  **Workflow Initiation** → Celery task with context packet including Mentor state
6.  **Agent Processing** → Sequential or parallel agent execution (Mentor can participate)
7.  **Result Handling** → WorkflowResultHandler processes outputs through MentorService
8.  **MentorService Post-Processing** → Results formatted for user-friendly delivery
9.  **Response Delivery** → WebSocket delivery to client with Mentor-enhanced responses

### Context Packet Structure

```json
{
  "user_id": "user-profile-uuid",
  "session_timestamp": "2023-10-15T14:30:00Z",
  "reported_mood": "focused",
  "reported_environment": "home", 
  "reported_time_availability": "30 minutes",
  "reported_focus": "creative activities",
  "extraction_confidence": 0.85,
  "user_ws_session_name": "client_session_uuid"
}
```

### Memory Systems

#### Agent Memory
*   **Psychological Monitoring**: Trait patterns, trust development
*   **Engagement Analytics**: Domain metrics, completion patterns  
*   **Strategy Agent**: Baseline parameters, adaptation patterns
*   **Mentor Agent**: Communication preferences, effective approaches

#### Workflow Memory
*   **State Persistence**: LangGraph state management
*   **Context Continuity**: Cross-agent data sharing
*   **Error Context**: Recovery information and retry parameters

---

## Integration Patterns

### Error Handling Strategy

1.  **Agent-Level Errors**: Graceful degradation with fallback responses
2.  **Workflow-Level Errors**: Error Handler Agent provides recovery
3.  **System-Level Errors**: Notification and retry mechanisms
4.  **User Communication**: Transparent error communication without technical details

### Trust-Based Adaptation

*   **Foundation Phase** (0-60%): High success probability, clear structure
*   **Expansion Phase** (61-100%): Moderate challenge, growth focus
*   **Communication Adaptation**: Tone and complexity based on trust level
*   **Challenge Calibration**: Dynamic adjustment based on user response

### Ethical Framework Integration

*   **Benevolence**: All decisions prioritize user well-being
*   **Fairness**: Balanced representation without bias
*   **Transparency**: Clear explanations and rationales
*   **Autonomy**: Respect for user choice and boundaries
*   **Safety**: Psychological and physical safety validation

### Quality Assurance

*   **Output Benchmarks**: Standardized quality criteria for each agent
*   **Confidence Scoring**: Reliability metrics for all assessments
*   **Validation Chains**: Multi-layer validation before user delivery
*   **Continuous Learning**: Pattern recognition and system improvement

---

## Benchmarking System Architecture

### Agent vs. Workflow Benchmarking

The Goali benchmarking system supports two distinct evaluation approaches, each designed for different optimization goals:

#### Agent Benchmarking (`AgentBenchmarker`)

**Purpose**: Evaluate individual agent performance in isolation to optimize LLM configurations and agent instructions.

**Key Characteristics**:
*   **Single Agent Focus**: Tests one agent at a time with controlled inputs
*   **LLM Configuration Testing**: Evaluates different models, temperatures, and prompt strategies
*   **Instruction Optimization**: Measures how well agent instructions guide LLM behavior
*   **Semantic Quality Assessment**: Uses evaluator LLMs to assess output quality against criteria
*   **Context Variable Testing**: Tests agent performance across different user contexts (trust levels, moods, environments)

**Data Captured**:
*   **LLM Metrics**: Model used, temperature, token usage, cost estimation
*   **Performance Metrics**: Execution time, success rate, error patterns
*   **Semantic Evaluation**: Quality scores across multiple dimensions (relevance, accuracy, helpfulness)
*   **Context Sensitivity**: Performance variation across different user contexts
*   **Instruction Effectiveness**: How well the agent follows its defined instructions

**Optimization Focus**:
*   Fine-tuning LLM model selection and parameters
*   Improving agent instruction clarity and effectiveness
*   Optimizing prompt engineering strategies
*   Balancing cost vs. quality trade-offs

#### Workflow Benchmarking (`WorkflowBenchmarker`)

**Purpose**: Evaluate complete multi-agent workflows to optimize coordination, state management, and system-level performance.

**Key Characteristics**:
*   **Multi-Agent Coordination**: Tests agent interactions and handoffs within workflows
*   **State Management**: Evaluates how well workflow state is maintained and passed between agents
*   **System Performance**: Measures end-to-end execution time and resource usage
*   **Agent Communication Tracking**: Monitors all agent interactions and data flow
*   **Workflow Logic Validation**: Ensures proper execution flow and error handling

**Data Captured**:
*   **Agent Communications**: Complete input/output data for each agent in the workflow
*   **State Transitions**: Before/after workflow state for each agent execution
*   **Coordination Metrics**: Handoff efficiency, state consistency, error propagation
*   **System Performance**: Total execution time, stage-by-stage timing, resource utilization
*   **Workflow Integrity**: Proper execution flow, error handling, recovery mechanisms

**Optimization Focus**:
*   Improving agent coordination and handoff efficiency
*   Optimizing workflow state management
*   Reducing system-level bottlenecks
*   Enhancing error handling and recovery
*   Balancing workflow complexity vs. performance

### Technical Implementation Differences

#### Agent Benchmarking Implementation

**Location**: `backend/apps/main/services/benchmark_manager.py`

**Core Components**:
```python
class AgentBenchmarker:
    async def run_agent_benchmark(self, scenario, agent_role, params):
        # Single agent execution with controlled inputs
        # LLM configuration testing
        # Semantic evaluation with multiple evaluator models
        # Context variable testing across ranges
```

**Key Features**:
*   **Scenario-Based Testing**: Uses `BenchmarkScenario` objects with specific agent roles
*   **Context Variable Ranges**: Tests across trust levels, moods, and environments
*   **Semantic Evaluation**: Multi-dimensional quality assessment
*   **LLM Configuration Matrix**: Tests different models and parameters
*   **Statistical Analysis**: Confidence intervals, significance testing

#### Workflow Benchmarking Implementation

**Location**: `backend/apps/main/services/async_workflow_manager.py`

**Core Components**:
```python
class WorkflowBenchmarker:
    async def run_workflow_benchmark(self, scenario, workflow_type, params):
        # Multi-agent workflow execution
        # Agent communication tracking
        # State management validation
        # System performance measurement
```

**Key Features**:
*   **LangGraph Integration**: Uses actual workflow definitions for testing
*   **Agent Communication Tracking**: Monitors all agent interactions
*   **Mock Tool Registry**: Provides realistic tool responses without external dependencies
*   **Stage Performance Analysis**: Detailed timing for each workflow stage
*   **Token Usage Tracking**: Comprehensive token usage across all agents

### Benchmarking Data Structures

#### Agent Benchmark Results
```python
{
    "agent_role": "mentor",
    "llm_model": "gpt-4o-mini",
    "llm_temperature": 0.7,
    "semantic_score": 0.85,
    "semantic_evaluation_details": {
        "relevance": 0.9,
        "accuracy": 0.8,
        "helpfulness": 0.85
    },
    "context_sensitivity": {
        "trust_level_variance": 0.12,
        "mood_impact": 0.08
    },
    "performance_metrics": {
        "mean_duration_ms": 1250,
        "success_rate": 0.95,
        "token_usage": "1200/800"
    }
}
```

#### Workflow Benchmark Results
```python
{
    "workflow_type": "wheel_generation",
    "agent_communications": [
        {
            "agent": "Orchestrator",
            "stage": "orchestration_initial",
            "timestamp": "2025-06-05T13:45:18.390852",
            "duration_ms": 59.68,
            "success": true,
            "input_data": {},
            "output_data": {},
            "execution_context": {
                "tool_calls": [
                    {
                        "id": "uuid-123",
                        "tool_name": "get_environment_context",
                        "tool_input": {...},
                        "tool_output": {...},
                        "duration_ms": 4.83,
                        "execution_mode": "real"
                    }
                ],
                "llm_interactions": [],
                "decision_points": [...]
            },
            "performance_metrics": {
                "duration_ms": 59.68,
                "token_usage": {"input": 0, "output": 0},
                "tool_call_count": 0,
                "success": true
            }
        }
    ],
    "stage_performance": {
        "orchestrator_init": 150,
        "resource_analysis": 2100,
        "strategy_synthesis": 3200
    },
    "system_metrics": {
        "total_execution_time": 12.5,
        "total_token_usage": 15000,
        "agent_coordination_efficiency": 0.92
    }
}
```

**Data Structure Notes (June 2025)**:
- Agent communications are now stored as a direct array format for better performance
- Each agent communication includes rich execution context with detailed tool calls
- Tool calls contain complete input/output data and execution metadata
- Performance metrics are embedded in each agent communication record
- The UI handles both legacy object format (`{agents: [...]}`) and new array format

#### Execution Mode Tracking in Results
Workflow results are enriched with metadata to indicate the actual execution mode used:
```python
# Example structure in workflow_result from backend/apps/main/graphs/wheel_generation_graph.py
workflow_result = {
    # ... other result data ...
    "execution_mode": "real" if (any_real_llm or any_real_db or any_real_tools) else "mock", # Based on actual usage
    "real_llm_used": any_real_llm_actually_used,
    "real_tools_used": any_real_tools_actually_used,
    "real_db_used": any_real_db_actually_used,
    "tool_usage": extracted_tool_usage_counts, # From _extract_tool_usage
    "agent_communications": extracted_agent_communications_data # From _extract_agent_communications
    # Potentially: requested_execution_mode vs actual_execution_modes for detailed comparison
}
```
If a workflow runs entirely in mock mode, the `execution_mode` will be "mock", and specific metadata like `"mock_execution_note"` might be included.
Helper functions like `_extract_tool_usage(result: WheelGenerationState, ...)` and `_extract_agent_communications(result: WheelGenerationState)` (both within [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:)) are used to gather relevant data from the completed workflow state for inclusion in the results.

### Execution Mode Management
The benchmarking system provides granular control over execution modes (real vs. mock) for different components like LLMs, tools, and database interactions. This is primarily managed through the `WheelGenerationState` and a centralized agent configuration helper.

#### `WheelGenerationState` Model for Execution Control
The `WheelGenerationState` model, defined in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:146-152), is crucial for managing execution modes within workflows like wheel generation. It includes specific fields to control whether real or mock components are used:

```python
# From backend/apps/main/graphs/wheel_generation_graph.py
class WheelGenerationState(BaseModel):
    # ... other state fields ...
    # CRITICAL CHANGE: Defaults changed from False to True to eliminate silent fallbacks to mock mode
    # Unit tests should explicitly set these to False when mock mode is desired
    use_real_llm: bool = Field(default=True, description="Whether to use real LLM services instead of mocks")
    use_real_tools: bool = Field(default=True, description="Whether to use real tool implementations instead of mocks")
    use_real_db: bool = Field(default=True, description="Whether to use real database operations instead of mocks")
    mock_tools: Optional[Any] = Field(default=None, description="Mock tool registry for partial mocking")
    # ... other state fields ...
```
**Key Properties:**
- **Default Behavior**: All execution modes default to `True` (i.e., real mode) as of June 13, 2025 - CRITICAL CHANGE to eliminate silent fallbacks.
- **Silent Fallback Prevention**: Changed from mock defaults to prevent production systems from accidentally running in mock mode.
- **Explicit Mock Configuration**: To use mock components, each mode must be explicitly set to `False` (reversed from previous behavior).
- **Granular Control**: Each component (LLM, tools, DB) can be independently set to real or mock.
- **Mock Tool Registry**: Supports partial mocking scenarios by allowing a `mock_tools` registry to be passed in the state.
- **Production Ready**: System now defaults to production-ready real mode instead of development mock mode.

State Initialization for workflows, as seen in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:291-300), includes these parameters:
```python
# Example from backend/apps/main/graphs/wheel_generation_graph.py
initial_state = WheelGenerationState(
    # ... other initial state parameters ...
    use_real_llm=use_real_llm,        # Execution mode control
    use_real_tools=use_real_tools,    # Execution mode control
    use_real_db=use_real_db,          # Execution mode control
    mock_tools=mock_tools             # Mock tool registry
)
```

#### Agent Configuration for Execution Modes
A centralized helper function, `_configure_agent_for_execution_mode(state: WheelGenerationState, agent_name: str, user_profile_id: Optional[str])` located in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:169-175), is used within agent nodes to configure individual agents based on the execution mode parameters present in the `WheelGenerationState`.

```python
# Pattern from backend/apps/main/graphs/wheel_generation_graph.py
async def agent_node(state: WheelGenerationState) -> Dict[str, Any]:
    logger.debug(f"🎭 {agent_name} executing with real_llm={state.use_real_llm}, real_tools={state.use_real_tools}, real_db={state.use_real_db}")
    agent_kwargs = _configure_agent_for_execution_mode(state, agent_name, user_profile_id)
    agent = AgentClass(**agent_kwargs)
    result = await agent.process(state)
    # ...
    return result
```
This helper function is responsible for:
- **LLM Configuration**: If `state.use_real_llm` is true, it attempts to select and configure an appropriate real LLM service (e.g., preferring gpt-4o-mini, with fallbacks). Otherwise, it sets up for mock LLM usage.
- **Database Service**: Configures the agent to use the real database service if `state.use_real_db` is true.
- **Tool Registry**: If `state.use_real_tools` is true, it prepares the agent to use real tool implementations. If false and `state.mock_tools` is provided, it uses the mock tool registry.
- **Error Handling**: Includes mechanisms for graceful fallbacks and detailed logging if a requested real component cannot be configured.

#### Real Mode Implementation Details
All workflow agents have been updated to support real mode execution through the `_configure_agent_for_execution_mode` helper.
- **Supported Agents**: This includes the Orchestrator, Resource, Engagement Analytics, Psychological Monitoring, Strategy, Wheel/Activity, Ethical Oversight, and Error Handler agents.
- **Execution Mode Matrix**: The system supports a flexible matrix of execution modes:
  | Component          | Mock Mode                         | Real Mode                             | Hybrid Mode                         |
  |--------------------|-----------------------------------|---------------------------------------|-------------------------------------|
  | **LLM Calls**      | Simulated responses               | Real API calls to configured models   | Configurable per agent              |
  | **Tool Execution** | Mock tool registry                | Real tool implementations             | Mixed real/mock tools               |
  | **Database Ops**   | Mock database service             | Real database with transactions       | Real DB with mock tools             |
- **Configuration Examples**:
  ```python
  # Full real mode
  state = WheelGenerationState(use_real_llm=True, use_real_tools=True, use_real_db=True)

  # Hybrid mode - real LLM, mock tools, real DB
  state = WheelGenerationState(use_real_llm=True, use_real_tools=False, use_real_db=True, mock_tools=MockToolRegistry())

  # Mock mode (default)
  state = WheelGenerationState() # All flags default to False
  ```

### Benchmark Execution Flow

#### Dual Execution Paths (Real vs. Mock)
The `WheelWorkflowBenchmarkManager` (defined in [`backend/apps/main/services/wheel_workflow_benchmark_manager.py`](backend/apps/main/services/wheel_workflow_benchmark_manager.py:)) employs a dual execution architecture. Based on the execution mode parameters (`use_real_llm`, `use_real_tools`, `use_real_db`), it decides whether to run the actual workflow or a mock version:

```python
# Conceptual logic in WheelWorkflowBenchmarkManager
if use_real_llm or use_real_tools or use_real_db:
    # Run real workflow using the actual run_wheel_generation_workflow
    output = await self._run_real_wheel_generation_workflow(scenario, params, workflow_input_data)
else:
    # Run mock workflow
    output = await self._run_mock_wheel_generation_workflow(scenario, params)
```
- **Real Workflow Path**: This path invokes the actual `run_wheel_generation_workflow` function (from [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:)). The `WheelGenerationState` within this workflow is initialized with the requested execution mode parameters. If real components are not fully implemented or face configuration issues, this path might raise errors (e.g., `NotImplementedError` historically, or specific configuration errors now).
- **Mock Workflow Path**: This path uses a dedicated `_run_mock_wheel_generation_workflow` method. This method simulates the workflow's execution and generates mock agent outputs, avoiding engagement with real external services or complex internal logic.

#### Workflow Function Interface
The primary workflow function, `run_wheel_generation_workflow` (located in [`backend/apps/main/graphs/wheel_generation_graph.py`](backend/apps/main/graphs/wheel_generation_graph.py:305-311)), is designed to accept execution mode parameters. When called by the benchmarking system, these are typically passed via a `workflow_input` dictionary:

```python
# From backend/apps/main/graphs/wheel_generation_graph.py
async def run_wheel_generation_workflow(
    user_profile_id: Optional[str] = None,
    context_packet: Optional[Dict[str, Any]] = None,
    workflow_id: Optional[str] = None,
    workflow_input: Optional[Dict[str, Any]] = None # For benchmarking interface
) -> Dict[str, Any]:
    # Extracts use_real_llm, use_real_tools, use_real_db, mock_tools from workflow_input
    # Initializes WheelGenerationState with these parameters
    # ... rest of the workflow logic ...
```
This interface allows the `WheelWorkflowBenchmarkManager` to precisely control the execution mode of the workflow being benchmarked.

---

## Detailed Workflow Specifications

### Wheel Generation Workflow

**Purpose**: Create personalized activity recommendations based on user context and profile.

**Agent Sequence**:
1.  **Orchestrator** → Initial workflow setup and context distribution
2.  **Resource Agent** → Environmental and resource analysis
3.  **Engagement Analytics** → Historical pattern analysis
4.  **Psychological Monitoring** → Current state and trust assessment
5.  **Strategy Agent** → Comprehensive strategy synthesis
6.  **Wheel/Activity Agent** → Activity selection and wheel construction
7.  **Ethical Oversight** → Final validation and approval
8.  **Orchestrator** → Final integration and delivery preparation

**Key Outputs**:
*   Personalized activity wheel with 6-8 activities
*   Probability-weighted activity distribution
*   Detailed activity instructions and requirements
*   Value propositions connecting activities to user goals

**Success Criteria**:
*   All activities pass ethical validation
*   Challenge levels appropriate for trust phase
*   Resource requirements match user availability
*   Domain distribution aligns with preferences and growth needs

### Evaluation Criteria and Optimization Guidelines

The evaluation criteria have been clarified to reflect the architectural reality where the Mentor is the primary user-facing agent, while other agents function as specialized tools within workflows.

#### Agent Evaluation Criteria

##### Mentor Agent Evaluation Criteria

**Communication Quality Assessment**:
*   **Tone Appropriateness**: Emotional tone suitable for user's trust level and context
*   **Empathy and Support**: Emotional understanding and supportive communication
*   **Trust Building**: Effectiveness in building and maintaining user trust
*   **Clarity**: Clear, understandable communication appropriate for user's level
*   **Responsiveness**: Appropriate response to user's emotional state and needs

**Decision Making Quality**:
*   **Workflow Routing**: Accuracy in selecting appropriate workflows for user needs
*   **Context Assessment**: Quality of user context analysis and interpretation
*   **Boundary Respect**: Appropriate respect for user autonomy and boundaries
*   **Escalation Decisions**: When and how to escalate or seek additional support

**LLM Configuration Optimization**:
*   **Model Selection**: Compare performance across different LLM models for user interaction
*   **Temperature Tuning**: Balance between empathy/creativity and consistency
*   **Token Efficiency**: Minimize token usage while maintaining communication quality
*   **Cost Optimization**: Balance performance with operational costs

##### Specialized Agent Evaluation Criteria

**Structured Output Quality**:
*   **Schema Compliance**: Adherence to defined output schemas and data contracts
*   **Data Completeness**: Thoroughness of analysis within agent's domain
*   **Accuracy**: Correctness of domain-specific insights and recommendations
*   **Integration Quality**: How well outputs integrate with downstream agents

**Domain Expertise Assessment**:
*   **Resource Agent**: Accuracy of resource and capacity assessments
*   **Engagement Agent**: Quality of user pattern and preference analysis
*   **Psychological Agent**: Appropriateness of psychological state assessments
*   **Strategy Agent**: Effectiveness of strategy synthesis and recommendations
*   **Activity Agent**: Relevance and quality of activity selections
*   **Ethical Agent**: Thoroughness of safety and ethical validations

#### Workflow Evaluation Criteria

**Output Quality Focus** (Primary Evaluation Area):
*   **Relevance**: How well final outputs (e.g., wheel items) match user needs and context
*   **Appropriateness**: Suitability of content for user's trust level, preferences, and situation
*   **Completeness**: Thoroughness of the final deliverable
*   **Personalization**: Degree of customization based on user profile and context
*   **Actionability**: Practical value and implementability of recommendations

**System Performance**:
*   **Coordination Efficiency**: Clean data transfer and handoffs between agents
*   **State Consistency**: Proper maintenance of workflow state across agent transitions
*   **Error Handling**: How well errors are contained, handled, and recovered from
*   **Resource Utilization**: Efficiency in token usage, execution time, and system resources
*   **End-to-End Latency**: Total workflow execution time
*   **Stage Bottlenecks**: Identification of performance constraints
*   **Scalability**: Performance under different load conditions

**Workflow Integrity**:
*   **Execution Flow**: Proper sequence and conditional logic
*   **Data Validation**: Input/output validation at each stage
*   **Error Handling**: Graceful failure management
*   **User Experience**: Smooth and responsive interaction flow

**Note**: Tone analysis and communication style evaluation are **not** part of workflow evaluation criteria. These aspects are evaluated exclusively in Mentor agent benchmarks, as workflows focus on output quality and system performance rather than communication style.

### Optimization Strategies

#### For Agent Performance

1.  **Iterative Prompt Engineering**:
    *   Test different instruction formulations
    *   Measure semantic quality across dimensions
    *   Optimize for specific use cases and contexts

2.  **LLM Configuration Tuning**:
    *   Systematic testing of model/temperature combinations
    *   Cost-benefit analysis for different configurations
    *   Context-specific optimization (trust levels, user types)

3.  **Context Sensitivity Analysis**:
    *   Identify performance variations across user contexts
    *   Develop context-specific adaptations
    *   Ensure consistent quality across all scenarios

#### For Workflow Performance

1.  **Bottleneck Identification**:
    *   Analyze stage-by-stage performance metrics
    *   Identify coordination inefficiencies
    *   Optimize critical path components

2.  **State Management Optimization**:
    *   Minimize state transfer overhead
    *   Ensure data consistency across agents
    *   Implement efficient error recovery

3.  **Agent Coordination Enhancement**:
    *   Improve handoff protocols
    *   Reduce redundant processing
    *   Optimize parallel execution opportunities

### Discussion Workflow

**Purpose**: Handle general conversation, clarification requests, and information collection.

**Agent Sequence**:
1.  **Orchestrator** → Goal determination and context setup
2.  **Mentor Agent** → Conversational interaction (looping until goal met)
3.  **Orchestrator** → Completion assessment and termination

**Conversation Loop Logic**:
```python
while not state.is_goal_met:
    mentor_response = mentor_agent.process(state)
    state.update(mentor_response)
    if conversation_limit_reached or goal_achieved:
        state.is_goal_met = True
```

**Use Cases**:
*   General chat and relationship building
*   Missing information collection for other workflows
*   Clarification of user intent or system capabilities
*   Error communication and recovery guidance

### Onboarding Workflow

**Purpose**: Transform completed questionnaire responses into comprehensive user profile.

**Agent Sequence**:
1.  **Orchestrator** → Questionnaire analysis and data organization
2.  **Resource Agent** → Environmental context and resource establishment
3.  **Engagement Analytics** → Preference and pattern analysis
4.  **Psychological Monitoring** → Comprehensive psychological assessment
5.  **Strategy Agent** → Initial strategy framework creation
6.  **Ethical Oversight** → Profile validation and safety review
7.  **Orchestrator** → Final profile integration and completion

**Profile Components Created**:
*   Demographics and basic user information
*   HEXACO personality trait assessments
*   Environmental context and resource inventory
*   Skills, capabilities, and limitations
*   Beliefs, goals, and inspirational sources
*   Trust baseline and communication preferences
*   Initial strategy framework for future interactions

---

## Advanced Technical Details

### Agent Communication Protocols

**Context Packet Standards**:
*   Standardized format across all agent interactions
*   Confidence scoring for all extracted information
*   Metadata tracking for audit and debugging
*   Session information for result routing

**State Management**:
*   Immutable state transitions with full audit trail
*   Rollback capabilities for error recovery
*   Concurrent access protection for multi-agent workflows
*   Memory persistence across workflow boundaries

### Memory Architecture

**Agent-Specific Memory**:
```python
# Psychological Monitoring Agent Memory
{
    "trait_expression_patterns": {
        "conscientiousness": {
            "baseline": 45,
            "growth_trajectory": "increasing",
            "confidence": 0.78,
            "last_updated": "2023-10-15T14:30:00Z"
        }
    },
    "trust_development_history": [...],
    "growth_opportunity_indicators": [...]
}
```

**Workflow Memory**:
```python
# Strategy Agent Memory
{
    "goal_trait_skill_mapping": {...},
    "challenge_calibration_rationale": {...},
    "domain_engagement_strategy": {...},
    "growth_facilitation_approach": {...},
    "trust_development_pathway": {...}
}
```

### Performance Optimization

**Caching Strategies**:
*   Agent memory caching with TTL policies
*   Frequently accessed user data caching
*   GenericActivity catalog caching
*   Workflow result caching for similar contexts

**Parallel Processing**:
*   Independent agent processing where possible
*   Async/await patterns for I/O operations
*   Database connection pooling
*   Redis-based result caching

**Monitoring and Metrics**:
*   Agent execution time tracking
*   Workflow completion rates
*   Error frequency by agent and workflow type
*   User satisfaction correlation with agent performance

---

## Development Guidelines

### Adding New Agents

1.  **Define Agent Responsibility**: Clear, focused purpose
2.  **Specify Data Access**: Read/write permissions and memory requirements
3.  **Design Processing Logic**: Input validation, core processing, output formatting
4.  **Implement Base Class**: Inherit from LangGraphAgent
5.  **Add to Workflow**: Define position in agent sequence
6.  **Create Tests**: Unit tests and integration tests
7.  **Document Outputs**: Expected output format and quality benchmarks

### Creating New Workflows

1.  **Define Workflow Purpose**: Clear objective and success criteria
2.  **Design Agent Sequence**: Optimal order and dependencies
3.  **Specify State Model**: Required state fields and transitions
4.  **Implement Routing Logic**: Conditional edges and error handling
5.  **Add Result Handling**: Output formatting and delivery
6.  **Create Documentation**: Flow diagrams and technical specifications
7.  **Implement Testing**: End-to-end workflow testing

### Quality Assurance Standards

**Agent Output Requirements**:
*   Confidence scores for all assessments (0-100)
*   Supporting evidence for all recommendations
*   Clear rationales for all decisions
*   Appropriate error handling and fallbacks

**Workflow Validation**:
*   Complete state transitions without data loss
*   Proper error propagation and recovery
*   Consistent output formatting
*   Performance within acceptable time limits

**Ethical Compliance**:
*   All outputs reviewed by Ethical Oversight Agent
*   User well-being prioritized over system efficiency
*   Transparent decision-making processes
*   Respect for user autonomy and boundaries

---

## Troubleshooting Guide

### Common Issues

**Agent Timeout Errors**:
*   Check database connection health
*   Verify memory access patterns
*   Review processing complexity
*   Implement fallback responses

**Workflow State Corruption**:
*   Validate state model consistency
*   Check for concurrent access issues
*   Review error handling logic
*   Implement state recovery mechanisms

**Memory Inconsistencies**:
*   Verify memory update patterns
*   Check for race conditions
*   Review confidence score calculations
*   Implement memory validation

**Performance Degradation**:
*   Monitor agent execution times
*   Check database query efficiency
*   Review caching effectiveness
*   Analyze workflow bottlenecks

### Debugging Tools

**Workflow Tracing**:
*   Complete execution logs with timestamps
*   State transition tracking
*   Agent input/output logging
*   Error context preservation

**Performance Monitoring**:
*   Agent execution time metrics
*   Memory usage tracking
*   Database query analysis
*   Cache hit/miss ratios

**Quality Validation**:
*   Output format validation
*   Confidence score verification
*   Ethical compliance checking
*   User satisfaction correlation

---

## Data Model Enhancement Analysis (June 2025)

### Current System Assessment

The wheel generation workflow and benchmarking system provide a solid foundation for debugging but have opportunities for enhancement to achieve the high-quality debugging experience we're targeting.

#### Current Strengths
- **Comprehensive Workflow State Tracking**: Complete capture of agent outputs and state transitions
- **Enhanced Agent Communications**: Rich metadata with mentor-relevant insights and execution context
- **Execution Mode Tracking**: Detailed tracking of real vs mock execution per agent
- **Token and Cost Tracking**: Enhanced tracking with agent-specific estimates and real usage data
- **Tool Usage Analysis**: Comprehensive tool call tracking with mock vs real differentiation

#### Identified Enhancement Opportunities

##### 1. Agent-Level Debugging Granularity
**Current Gap**: Limited visibility into agent internal processing
**Enhancement Needed**:
- Agent input data capture alongside output data
- Intermediate reasoning steps and decision points
- LLM interaction details (prompts, responses, model parameters)
- Tool call sequences with full execution context

##### 2. LLM Interaction Transparency
**Current Gap**: No capture of actual prompts or raw LLM responses
**Enhancement Needed**:
- Full prompt engineering visibility for optimization
- Raw LLM response analysis for quality assessment
- Model-specific performance pattern tracking
- Detailed cost attribution per agent and decision point

##### 3. Enhanced Error Context
**Current Gap**: Limited context about error occurrence and resolution
**Enhancement Needed**:
- Detailed error context with execution state
- Error resolution path tracking
- Fallback usage effectiveness analysis
- Predictive error detection capabilities

### Implementation Roadmap

#### Phase 1: Core Debugging Infrastructure (High Priority)
1. **Enhanced Agent Communication Tracking**
   - Capture agent input data alongside output data
   - Track processing context and decision points
   - Implement standardized agent execution logging

2. **LLM Interaction Logging**
   - Store actual prompts sent to LLMs
   - Capture raw responses and model parameters
   - Track token usage and performance per interaction

3. **Error Context Enhancement**
   - Detailed error context capture
   - Resolution path tracking
   - Fallback usage monitoring

#### Phase 2: Advanced Monitoring (Medium Priority)
1. **Real-Time State Tracking**
   - Intermediate state capture during execution
   - State consistency validation across agents
   - Performance bottleneck identification

2. **Tool Call Sequence Analysis**
   - Detailed tool call context and sequencing
   - Tool performance and reliability metrics
   - Usage pattern optimization

#### Phase 3: Predictive Analytics (Low Priority)
1. **Alternative Path Analysis**
   - Decision alternatives and confidence tracking
   - Path optimization opportunity identification
   - Quality prediction modeling

2. **Proactive Quality Management**
   - Real-time quality prediction during execution
   - Early warning systems for quality issues
   - Automated optimization recommendations

### Expected Impact

#### For Debugging Experience
- **Complete Visibility**: Full transparency into workflow execution from input to output
- **Root Cause Analysis**: Precise identification of failure points with full context
- **Performance Optimization**: Data-driven insights for workflow and agent optimization
- **Quality Assurance**: Comprehensive quality tracking with predictive capabilities

#### For Development Workflow
- **Faster Debugging**: Reduced time to identify and fix issues with detailed execution traces
- **Better Testing**: More comprehensive test coverage with execution context validation
- **Improved Reliability**: Enhanced error handling with fallback effectiveness tracking
- **Enhanced Monitoring**: Real-time visibility into system health and performance patterns

### Technical Implementation Notes

The enhanced data model should integrate seamlessly with existing infrastructure:
- **BenchmarkRun.agent_communications**: Expand to include enhanced agent execution data
- **BenchmarkRun.raw_results**: Include LLM interaction logs and detailed error context
- **AgentCommunicationTracker**: Enhance to capture input data and processing steps
- **Admin Interface**: Update modals to display enhanced debugging information

This analysis provides the foundation for implementing comprehensive debugging capabilities that will significantly improve development efficiency and system reliability.

## Debugging and Analysis Tools (Updated June 2025)

The benchmarking system provides comprehensive debugging tools for analyzing workflow execution:

### Agent Communication Timeline
- Visual representation of agent interactions
- Tool call tracking and performance metrics
- Error detection and execution mode indicators
- **NEW (June 2025)**: Enhanced data access patterns supporting multiple data structure formats
- **NEW (June 2025)**: Bootstrap-powered modals with proper error handling

### Workflow Event Tree
- Interactive tree view of workflow execution
- Expandable nodes for detailed inspection
- Real-time performance analysis
- **NEW (June 2025)**: Click-to-expand agent execution details
- **NEW (June 2025)**: Activity crafting sequence visualization

### Tool Call Analysis
- Detailed tool call logs with input/output data
- Execution mode tracking (real vs mock)
- Performance bottleneck identification
- **NEW (June 2025)**: Enhanced tool call extraction from execution context
- **NEW (June 2025)**: Color-coded execution modes and success indicators

### Activity Crafting Details (NEW June 2025)
- **Show Tailoring Process**: Click on any wheel activity to see detailed crafting sequence
- **Agent Contributions**: View which agents contributed to each activity
- **Tool Call Breakdown**: See exact tool calls used to craft each activity
- **Timeline Visualization**: Step-by-step crafting process with timing information

### Enhanced Modal System (NEW June 2025)
- **Bootstrap Integration**: Proper modal functionality with CDN fallbacks
- **Multi-format Data Support**: Handles various agent communication data structures
- **Error Recovery**: Graceful degradation with informative error messages
- **Interactive Elements**: Expandable sections, tabbed interfaces, and detailed views

### UI Improvements Summary (June 2025)
The wheel generation evaluation modal has been completely overhauled to provide better debugging capabilities:

**Fixed Issues**:
- Bootstrap integration errors preventing modal functionality
- Agent data access issues with null/undefined errors
- Tool call visualization problems despite successful execution
- Missing activity crafting detail functions

**New Features**:
- Comprehensive activity crafting modal with timeline visualization
- Enhanced agent execution details with performance metrics
- Interactive timeline with expandable event details
- Color-coded execution modes and success indicators
- Proper error handling with informative fallback displays

**Technical Enhancements**:
- Multi-format data structure support for agent communications
- Enhanced tool call extraction from execution context
- Bootstrap CDN integration with fallback mechanisms
- Improved JavaScript error handling and user feedback

---

## Summary

This technical architecture guide provides comprehensive documentation for Goali's multi-agent system. The restructured guide focuses on:

### Key Documentation Areas

1. **System Architecture**: Multi-agent orchestration with specialized roles and responsibilities
2. **Agent Specifications**: Detailed technical specifications for all 8 agents with input/output structures
3. **Workflow Orchestration**: LangGraph-based workflow management with state persistence
4. **Data Flow Solutions**: Fixed agent communication data extraction issues in admin interface
5. **Execution Management**: Real/mock/hybrid execution modes with granular control
6. **Benchmarking Framework**: Comprehensive agent and workflow evaluation systems
7. **Debugging Tools**: Practical troubleshooting patterns and monitoring capabilities

### Technical Improvements Documented

- **✅ Fixed Data Flow Issues**: Enhanced `extractAgentOutputData()` to properly extract rich agent output and implemented comprehensive rich data rendering in agent execution detail modal
- **Resource Agent Enhancement**: Documented improved `combined_resource_context` structure
- **Execution Mode Control**: Comprehensive execution mode management through `WheelGenerationState`
- **Communication Tracking**: Complete agent interaction monitoring with `AgentCommunicationTracker`
- **Benchmarking Architecture**: Dual-path benchmarking for agents vs workflows
- **✅ Rich Output Display**: Implemented agent-specific rich data rendering with structured display for all agent types (Resource, Engagement, Psychological, Strategy, Activity, Ethical)

### Reference Documentation

For specific implementations, refer to:
- **Agent Details**: `docs/backend/agents/agents_description.md`
- **Benchmarking System**: `docs/backend/BENCHMARKING_SYSTEM.md`
- **Quality Evaluation**: `docs/backend/quality/CONTEXTUAL_EVALUATION_SYSTEM.md`
- **Agent Tools**: `docs/backend/AGENT_TOOLS_COMPREHENSIVE_GUIDE.md`

This guide serves as the authoritative technical reference for understanding, developing, and debugging Goali's multi-agent system architecture.
