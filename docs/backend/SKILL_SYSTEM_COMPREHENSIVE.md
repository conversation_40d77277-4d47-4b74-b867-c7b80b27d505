# Skill System Comprehensive Documentation

## Overview

The Goali skill system is a sophisticated architecture designed to calculate the precise challengingness of activities for individual users. It consists of two complementary systems:

1. **User Profile Skills** - Simple skill codes for user profiles (GenericSkill → Skill)
2. **Challengingness Calculation** - Complex skill system for activity matching (SkillDefinition, SkillAttribute)

## Core Architecture

### 1. Two-Tier Skill System

The skill system operates on two levels:

**Tier 1: User Profile Skills (Currently Active)**
```
GenericSkill (Simple skill codes from catalog)
    ↓ (instantiated as)
Skill (User-specific skill instances)
```

**Tier 2: Challengingness Calculation (Future Enhancement)**
```
SkillAttribute (Fundamental Components)
    ↓ (composed into)
SkillDefinition (Composite Skills)
    ↓ (applied to)
GenericDomain (Activity Domains)
    ↓ (linked to)
GenericSkill (User-Facing Skills)
```

### 2. Key Models and Relationships

#### SkillAttribute
**Purpose**: Fundamental cognitive, physical, social, emotional, creative, and technical components
**Examples**: Analytical Reasoning, Physical Strength, Empathic Understanding, Creative Thinking

**Key Fields**:
- `code`: Unique identifier (e.g., 'cog_analytical', 'phys_strength')
- `name`: Human-readable name
- `description`: Detailed explanation
- `base_decay_rate`: How quickly it deteriorates (0-100)
- `development_difficulty`: Inherent difficulty to develop (1-100)
- `development_timeframe`: Time needed to develop ('immediate', 'short', 'medium', 'long', 'lifetime')

#### SkillDefinition
**Purpose**: Composite skills made from weighted combinations of attributes
**Examples**: Creative Writing, Public Speaking, Programming, Leadership

**Key Fields**:
- `code`: Unique identifier (e.g., 'creative_writing', 'programming')
- `name`: Human-readable name
- `description`: Detailed explanation
- `tags`: Flexible categorization (JSON array)

**Relationships**:
- `attribute_compositions`: Many-to-many with SkillAttribute through SkillAttributeComposition
- `domain_applications`: Many-to-many with GenericDomain through SkillDomainApplication

#### GenericSkill (Tier 1 - Active)
**Purpose**: Simple skill codes used in user profiles for validation and import
**Status**: ✅ Fully implemented and integrated

**Key Fields**:
- `code`: Unique identifier (e.g., 'tech_coding_python', 'soft_empathy')
- `description`: Brief description of the skill

**Data Source**:
- Seeded from `user_profile_catalog.json` via `seed_db_55_generic_skills` command
- Integrated with user profile validation schema
- Automatically synchronized with import validation

**Integration**:
- Used by user profile import/validation system
- Referenced in user profile JSON schema
- Managed through admin interface commands

#### Skill (User-Specific - Tier 1)
**Purpose**: Individual user's proficiency in specific GenericSkill instances
**Status**: ✅ Fully implemented and working

**Key Fields**:
- `user_profile`: Link to UserProfile
- `generic_skill`: Link to GenericSkill
- `level`: Proficiency level (0-100)
- `user_awareness`: User's awareness of their proficiency (0-100)
- `user_enjoyment`: Enjoyment level (0-100)
- `description`: Personal description of skill manifestation
- `note`: Additional details
- `acquisition_date`: When skill was acquired
- `last_practiced`: When last used
- `practice_frequency`: How often practiced (daily, weekly, monthly, etc.)
- `formal_training`: Whether formally trained
- `training_details`: Details about training/acquisition (maps from acquisition_context in import)
- `contexts`: JSON field for application contexts
- `growth_goal`: Target level user aims to achieve
- `stagnation_point`: Level where progress stalled

### 3. Relationship Models

#### SkillAttributeComposition
**Purpose**: Defines how attributes combine to form skills with specific weights
**Key Field**: `weight` - Relative importance (0.1-10.0, higher = more important)

#### SkillDomainApplication
**Purpose**: Defines how skills apply to domains with contextual parameters
**Key Fields**:
- `relevance`: How relevant skill is to domain (10-100)
- `transfer_coefficient`: Effectiveness multiplier (0.1-2.0)
- `domain_specific_properties`: JSON field for domain-specific parameters

#### SkillTraitRelationship
**Purpose**: Maps how personality traits affect skill acquisition/application
**Key Field**: `impact` - Effect strength (-3 to +3, negative = hindering, positive = beneficial)

#### AttributeTraitInfluence
**Purpose**: Defines how traits influence fundamental attributes
**Key Field**: `impact` - Effect strength (-3 to +3)

## Challengingness Calculation System

### 1. Multi-Factor Analysis

The system calculates activity challengingness by considering:

1. **Required Skills**: What skills the activity demands
2. **User Proficiency**: User's current skill levels
3. **Trait Influences**: How user's personality affects skill application
4. **Domain Context**: How skills transfer to the activity's domain
5. **Skill Interactions**: How skills complement or conflict with each other

### 2. Calculation Flow

```
1. Activity Analysis
   ↓
2. Required Skills Identification
   ↓
3. User Skill Proficiency Lookup
   ↓
4. Trait Influence Application
   ↓
5. Domain Transfer Coefficient Application
   ↓
6. Skill Interaction Modeling
   ↓
7. Final Challengingness Score
```

### 3. Precision Factors

- **Attribute Composition**: Skills broken down to fundamental components
- **Trait Modifiers**: Personality influences on each attribute
- **Domain Specificity**: Context-aware skill effectiveness
- **Temporal Factors**: Skill decay and development timeframes
- **User Awareness**: Self-perception vs. actual ability

## Data Seeding Architecture

### Current Implementation

The skill system uses a two-command seeding approach:

#### 1. seed_db_50_skill_system.py (Tier 2 - Complex System)
**Purpose**: Seeds the complex skill system for future challengingness calculations
**Status**: ✅ Implemented but not yet used in production

Creates:
- **Fundamental Attributes** (26 total): Cognitive, Physical, Social, Emotional, Creative, Technical
- **Trait Influences** (40+ mappings): HEXACO traits → Skill attributes
- **Skill Definitions** (10 sample skills): Complex composite skills
- **Domain Applications** (70+ mappings): Skills → Domains with relevance coefficients

#### 2. seed_db_55_generic_skills.py (Tier 1 - Active System)
**Purpose**: Seeds GenericSkill objects for user profile validation and import
**Status**: ✅ Fully implemented and integrated

Creates:
- **GenericSkill objects** from `user_profile_catalog.json`
- **Skill codes** like 'tech_coding_python', 'soft_empathy', etc.
- **Automatic schema synchronization** with user profile validation

### Integration with Catalog Architecture

**Current Status**: ✅ GenericSkills are fully integrated with catalog system
- Seeded from `user_profile_catalog.json`
- Integrated with validation schemas
- Managed through admin interface
- Follows established catalog patterns

**Future Enhancement**: Extract SkillDefinition data to JSON catalog for Tier 2 system

## Admin Interface Integration

### 1. Command Management Interface

**Available Commands**:
- `seed_db_55_generic_skills` - Seed GenericSkill objects from catalog
- `list_generic_skills` - List all available skill codes with search
- `add_generic_skill` - Add new skill codes to the system
- `update_skill_schema` - Synchronize validation schema with database

**Features**:
- ✅ Integrated with command_management.html
- ✅ Supports bypass idempotent mechanism
- ✅ Supports external JSON file uploads
- ✅ Real-time progress reporting
- ✅ Error handling and validation

### 2. User Profile Import Integration

**Validation System**:
- ✅ Automatic schema synchronization
- ✅ Real-time skill code validation
- ✅ Comprehensive error reporting
- ✅ Integration with catalog management

**Import Process**:
- ✅ Field mapping (acquisition_context → training_details)
- ✅ Business object validation
- ✅ Database transaction safety
- ✅ Overwrite support for existing users

## Current Status and Implementation

### 1. Completed Features ✅

- **GenericSkill Model**: Properly implemented with code and description fields
- **Catalog Integration**: Fully integrated with user_profile_catalog.json
- **Validation System**: Complete schema synchronization and validation
- **Import System**: Working end-to-end user profile import with skill validation
- **Admin Interface**: Command management integration with all skill commands
- **Seeding Integration**: Integrated with run_seeders.py idempotent system

### 2. Working Workflow ✅

1. **Database Reset**: `reset_and_migrate.sh` clears all data
2. **Seeding**: `run_seeders` command seeds all catalogs including GenericSkills
3. **Schema Sync**: Validation schema automatically includes all skill codes
4. **Import**: User profiles with skill codes validate and import successfully
5. **Management**: Admin interface provides full skill management capabilities

### 3. Validated Components ✅

- **guigui.json import**: ✅ Working with all skill codes
- **Validation endpoint**: ✅ Proper error reporting for invalid codes
- **Schema synchronization**: ✅ Automatic updates when skills are added
- **Command integration**: ✅ All commands available in admin interface

## Future Enhancements

### 1. Tier 2 System Integration

The complex skill system (SkillDefinition, SkillAttribute) is implemented but not yet used in production. Future work should:

1. **Extract to Catalog**: Move SkillDefinition data to JSON catalog
2. **Link Systems**: Connect GenericSkill with SkillDefinition
3. **Implement Algorithms**: Activate challengingness calculation
4. **Test Integration**: Validate with real user profiles

### 2. Admin Interface Enhancements

1. **Django Admin Integration**: Add GenericSkill to admin interface
2. **Relationship Visualization**: Show skill relationships graphically
3. **Bulk Operations**: Add import/export functionality
4. **User Skills View**: Show user-specific skill instances

### 3. Documentation Enhancements

1. **API Documentation**: Document skill-related API endpoints
2. **Algorithm Documentation**: Detail challengingness calculation
3. **Integration Guide**: Document how to use skills in workflows
4. **Testing Guide**: Document skill system testing procedures

## Usage Guide

### 1. Seeding Skills

```bash
# Run all seeders including skills
python manage.py run_seeders

# Seed only GenericSkill objects
python manage.py seed_db_55_generic_skills

# Seed with external JSON
python manage.py seed_db_55_generic_skills --external-json path/to/skills.json

# Force re-seeding
python manage.py seed_db_55_generic_skills --bypass-idempotent
```

### 2. Managing Skills

```bash
# List all skills
python manage.py list_generic_skills

# Search for skills
python manage.py list_generic_skills --search python

# Add new skill
python manage.py add_generic_skill 'new_skill_code' 'Description'

# Update schema
python manage.py update_skill_schema
```

This skill system enables precise user profile validation and import, with future capabilities for activity matching based on user capabilities and preferences.
