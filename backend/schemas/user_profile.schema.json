{"type": "object", "required": ["user_account", "profile_name", "demographics"], "properties": {"user_account": {"type": "object", "required": ["username", "email"], "properties": {"username": {"type": "string", "description": "Unique username for login (3-150 chars, letters/numbers/underscore only)"}, "email": {"type": "string", "description": "User's email address"}, "first_name": {"type": "string", "description": "User's first name"}, "last_name": {"type": "string", "description": "User's last name"}, "password": {"type": "string", "description": "User password (minimum 8 characters, will be hashed on server)"}}}, "profile_name": {"type": "string", "description": "Display name for the user profile (1-255 characters)"}, "is_real": {"type": "boolean", "description": "True for real users, False for test/archetypal profiles"}, "demographics": {"type": "object", "required": ["full_name", "age", "gender", "location", "language", "occupation"], "properties": {"full_name": {"type": "string", "description": "User's complete full name"}, "age": {"type": "integer", "description": "User's current age in years (13-120)"}, "gender": {"type": "string", "description": "User's gender identity"}, "location": {"type": "string", "description": "User's current location or residence (city, region, country)"}, "language": {"type": "string", "description": "User's preferred or native language"}, "occupation": {"type": "string", "description": "User's current occupation or professional role"}}}, "environments": {"type": "array", "description": "User environments (1-5 environments recommended)", "items": {"type": "object", "required": ["environment_name", "environment_description", "is_current", "effective_start"], "properties": {"environment_name": {"type": "string", "description": "User's personal name for their primary environment"}, "environment_description": {"type": "string", "description": "Detailed description of the user's living/working environment"}, "is_current": {"type": "boolean", "description": "Whether this is the user's current primary environment (should be true)"}, "generic_environment_code": {"type": "string", "description": "Code referencing a generic environment archetype (optional)", "enum": ["ind_comm_arcade", "ind_comm_bar_nightclub", "ind_comm_bookstore", "ind_comm_cafe", "ind_comm_cinema", "ind_comm_cowork_space", "ind_comm_fitness_center", "ind_comm_hotel_lobby", "ind_comm_mall", "ind_comm_restaurant", "ind_comm_retail_store", "ind_comm_salon_spa", "ind_comm_supermarket", "ind_cultural_artgallery", "ind_cultural_arthouse", "ind_cultural_artisanworkshop", "ind_cultural_cinemacomplex", "ind_cultural_concerthall", "ind_cultural_culinaryarts", "ind_cultural_dancestudio", "ind_cultural_historicalarchive", "ind_cultural_library", "ind_cultural_literarysalon", "ind_cultural_museum", "ind_cultural_musicschool", "ind_cultural_operahouse", "ind_cultural_recordingstudio", "ind_cultural_theater", "ind_digital_enabled", "ind_edu_art_studio", "ind_edu_classroom", "ind_edu_computer_lab", "ind_edu_conference_room", "ind_edu_cooking_classroom", "ind_edu_gymnasium", "ind_edu_lab_science", "ind_edu_language_lab", "ind_edu_lecture_hall", "ind_edu_library", "ind_edu_maker_space", "ind_edu_music_room", "ind_edu_quiet_study", "ind_edu_sensory_room", "ind_edu_stem_lab", "ind_edu_theater", "ind_edu_virtual_reality", "ind_healthcare_dental_office", "ind_healthcare_emergency_room", "ind_healthcare_hospice", "ind_healthcare_hospital_ward", "ind_healthcare_icu", "ind_healthcare_imaging_center", "ind_healthcare_laboratory", "ind_healthcare_maternity_ward", "ind_healthcare_mental_health_facility", "ind_healthcare_outpatient_clinic", "ind_healthcare_pharmacy", "ind_healthcare_physical_therapy", "ind_healthcare_private_room", "ind_healthcare_rehab_center", "ind_healthcare_waiting_room", "ind_micro_space", "ind_nat_aquarium", "ind_nat_atrium", "ind_nat_botanical_garden", "ind_nat_conservatory", "ind_nat_indoor_waterfall", "ind_nat_terrarium", "ind_prof_breakroom", "ind_prof_callcenter", "ind_prof_conference", "ind_prof_coworking", "ind_prof_creativestudio", "ind_prof_cubicle", "ind_prof_executivesuite", "ind_prof_office_private", "ind_prof_openplan", "ind_prof_quietroom", "ind_prof_rooftopspace", "ind_prof_servercenter", "ind_prof_techlab", "ind_prof_trainingroom", "ind_prof_workshop", "ind_quiet_space", "ind_rec_arcade", "ind_rec_board_game_cafe", "ind_rec_bowling", "ind_rec_dance_studio", "ind_rec_escape_room", "ind_rec_gym", "ind_rec_indoor_climbing", "ind_rec_movie_theater", "ind_rec_pool_hall", "ind_rec_swimming_pool", "ind_rec_trampoline_park", "ind_rec_yoga_studio", "ind_residential_attic", "ind_residential_basement", "ind_residential_bathroom", "ind_residential_bedroom", "ind_residential_dining_room", "ind_residential_game_room", "ind_residential_guest_room", "ind_residential_hallway", "ind_residential_hobby_room", "ind_residential_home_gym", "ind_residential_home_office", "ind_residential_kitchen", "ind_residential_laundry_room", "ind_residential_living_room", "ind_residential_meditation_space", "ind_residential_reading_nook", "ind_residential_sunroom", "out_comm_food_truck_park", "out_comm_market", "out_comm_shopping_street", "out_cultural_amphitheater", "out_cultural_festival", "out_cultural_heritage", "out_cultural_openairdance", "out_cultural_sculpture", "out_edu_campus_quad", "out_edu_outdoor_classroom", "out_edu_school_yard", "out_healthcare_healing_garden", "out_nat_beach", "out_nat_canyon", "out_nat_cliffside", "out_nat_coral_reef", "out_nat_desert", "out_nat_farm", "out_nat_forest", "out_nat_garden", "out_nat_lake", "out_nat_meadow", "out_nat_mountain", "out_nat_river", "out_nat_savanna", "out_nat_tundra", "out_nat_volcanic_area", "out_nat_wetland", "out_prof_campusgrounds", "out_rec_beach", "out_rec_dog_park", "out_rec_golf_course", "out_rec_hiking_trail", "out_rec_outdoor_fitness", "out_rec_playground", "out_rec_skatepark", "out_rec_sports_field", "out_rec_water_park", "out_residential_backyard", "out_residential_front_yard", "out_residential_garden", "out_residential_patio_deck"]}, "effective_start": {"type": "string", "description": "Date when user started using this environment (YYYY-MM-DD format)"}, "effective_end": {"type": "string", "description": "Date when user will stop using this environment (YYYY-MM-DD format, optional)"}, "physical_properties": {"type": "object", "properties": {"rurality": {"type": "integer", "description": "Urban to rural scale: 0=urban, 100=rural"}, "noise_level": {"type": "integer", "description": "Ambient noise level: 0=silent, 100=very noisy"}, "light_quality": {"type": "integer", "description": "Natural light quality: 0=poor, 100=excellent"}, "accessibility": {"type": "integer", "description": "Physical accessibility level: 0=poor, 100=excellent"}, "air_quality": {"type": "integer", "description": "Air quality rating: 0=poor, 100=excellent"}, "has_natural_elements": {"type": "boolean", "description": "Presence of natural elements like plants, water, etc."}, "space_size": {"type": "string", "description": "Available space size: small, medium, large, or very_large"}}}, "social_context": {"type": "object", "properties": {"privacy_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of privacy available: 0=public, 100=completely private"}, "typical_occupancy": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Typical occupancy level: 0=empty, 100=extremely crowded"}, "social_interaction_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Level of social interactions: 0=none, 100=constant interaction"}, "formality_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Formality level: 0=very casual, 100=very formal"}, "safety_level": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Safety level: 0=unsafe, 100=very safe"}, "supervision_level": {"type": "string", "enum": ["none", "minimal", "moderate", "high", "constant"], "description": "Level of supervision or monitoring"}, "cultural_diversity": {"type": "integer", "minimum": 0, "maximum": 100, "description": "Cultural diversity: 0=homogeneous, 100=highly diverse"}}}, "activity_support": {"type": "object", "properties": {"creative_activities": {"type": "integer", "description": "Support for creative/artistic activities: 0=no support, 100=excellent support"}, "physical_activities": {"type": "integer", "description": "Support for physical exercise/movement: 0=no support, 100=excellent support"}, "intellectual_activities": {"type": "integer", "description": "Support for learning/intellectual pursuits: 0=no support, 100=excellent support"}, "social_activities": {"type": "integer", "description": "Support for social gatherings/interactions: 0=no support, 100=excellent support"}, "reflective_activities": {"type": "integer", "description": "Support for meditation/journaling/reflection: 0=no support, 100=excellent support"}, "productive_activities": {"type": "integer", "description": "Support for work/productive tasks: 0=no support, 100=excellent support"}}}, "psychological_qualities": {"type": "object", "properties": {"restorative_quality": {"type": "integer", "description": "How restorative and rejuvenating the environment feels: 0=draining, 100=highly restorative"}, "stimulation_level": {"type": "integer", "description": "Level of mental stimulation provided: 0=understimulating, 100=overstimulating"}, "aesthetic_appeal": {"type": "integer", "description": "Visual beauty and aesthetic appeal: 0=unappealing, 100=beautiful"}, "comfort_level": {"type": "integer", "description": "Physical and emotional comfort level: 0=uncomfortable, 100=very comfortable"}, "personal_significance": {"type": "integer", "description": "Personal meaning and emotional attachment: 0=no attachment, 100=deeply meaningful"}}}}}}, "traits": {"type": "array", "description": "HEXACO personality trait inclinations (15-24 traits recommended)", "items": {"type": "object", "required": ["trait_code", "strength", "awareness"], "properties": {"trait_code": {"type": "string", "description": "HEXACO trait code from database (24 valid codes)", "enum": ["agree_flexibility", "agree_forgiveness", "agree_gentleness", "agree_patience", "consc_diligence", "consc_organization", "consc_perfectionism", "consc_prudence", "emotion_anxiety", "emotion_dependence", "emotion_fearfulness", "emotion_sentimentality", "extra_liveliness", "extra_self_esteem", "extra_sociability", "extra_social_boldness", "honesty_fairness", "honesty_greed_avoidance", "honesty_modesty", "honesty_sincerity", "open_aesthetic", "open_creativity", "open_inquisitive", "open_unconventional"]}, "strength": {"type": "integer", "description": "Strength of this trait in personality: 0=very low, 100=very high"}, "awareness": {"type": "integer", "description": "User's self-awareness of this trait: 0=completely unaware, 100=highly aware"}, "manifestation": {"type": "string", "description": "How this trait manifests in the user's behavior and actions"}, "context": {"type": "string", "description": "The context or situations where this trait is most evident"}, "development_notes": {"type": "string", "description": "Notes about how this trait developed or manifests in behavior"}}}}, "beliefs": {"type": "array", "description": "Core beliefs and worldview elements (3-8 beliefs recommended)", "items": {"type": "object", "required": ["belief_statement", "strength", "certainty"], "properties": {"belief_code": {"type": "string", "description": "Code referencing a generic belief from the authoritative catalog (optional but recommended)"}, "belief_statement": {"type": "string", "description": "Clear, specific statement of what the user believes"}, "strength": {"type": "integer", "description": "How strongly the user holds this belief: 0=weak belief, 100=core conviction"}, "certainty": {"type": "integer", "description": "How certain the user is about this belief: 0=uncertain, 100=absolutely certain"}, "evidence_sources": {"type": "array", "items": {"type": "string"}, "description": "Sources or experiences that support this belief"}, "life_impact": {"type": "integer", "description": "How much this belief impacts daily decisions: 0=no impact, 100=guides all decisions"}, "emotionality": {"type": "integer", "description": "Emotional charge of this belief: -100=very negative, 0=neutral, 100=very positive", "minimum": -100, "maximum": 100}, "user_confidence": {"type": "integer", "description": "User's personal conviction in this belief: 0=very uncertain, 100=absolutely certain"}, "stability": {"type": "integer", "description": "Resistance to change of this belief: 0=very changeable, 100=deeply ingrained"}, "user_awareness": {"type": "integer", "description": "User's awareness of holding this belief: 0=unconscious, 100=highly conscious"}}}}, "aspirations": {"type": "array", "description": "Long-term goals and vision elements (2-5 aspirations recommended)", "items": {"type": "object", "required": ["title", "description", "importance"], "properties": {"title": {"type": "string", "description": "Concise title of the aspiration"}, "description": {"type": "string", "description": "Detailed description of what the user wants to achieve or become"}, "importance": {"type": "integer", "description": "How important this aspiration is: 0=nice to have, 100=life defining"}, "time_horizon": {"type": "string", "description": "Expected timeframe (e.g., '5 years', 'lifetime', 'within 2 years')"}, "current_progress": {"type": "integer", "description": "Current progress toward this aspiration: 0=not started, 100=achieved"}}}}, "intentions": {"type": "array", "description": "Short to medium-term goals and plans (2-8 intentions recommended)", "items": {"type": "object", "required": ["title", "description", "target_date", "commitment_level"], "properties": {"title": {"type": "string", "description": "Clear title of the intention"}, "description": {"type": "string", "description": "Specific description of what the user intends to accomplish"}, "target_date": {"type": "string", "description": "Target completion date (YYYY-MM-DD format)"}, "commitment_level": {"type": "integer", "description": "How committed the user is to this intention: 0=weak commitment, 100=fully committed"}, "success_criteria": {"type": "string", "description": "How the user will know they've achieved this intention"}}}}, "inspirations": {"type": "array", "description": "Sources of motivation and inspiration (2-6 sources recommended)", "items": {"type": "object", "required": ["source", "description", "strength"], "properties": {"source": {"type": "string", "description": "Source of inspiration (person, book, experience, philosophy, etc.)"}, "description": {"type": "string", "description": "How this source inspires the user and why it matters"}, "strength": {"type": "integer", "description": "How much this source inspires the user: 0=mild inspiration, 100=life changing"}, "reference_url": {"type": "string", "description": "URL reference if applicable (books, articles, videos)"}}}}, "skills": {"type": "array", "description": "Personal skills and capabilities (5-15 skills recommended)", "items": {"type": "object", "required": ["skill_code", "description", "level"], "properties": {"skill_code": {"type": "string", "description": "Skill code from database (40 valid codes)", "enum": ["communication", "creative_writing", "emotional_intelligence", "leadership", "meditation", "negotiation", "physical_fitness", "problem_solving", "programming", "public_speaking", "soft_active_listening", "soft_communication", "soft_conflict_resolution", "soft_critical_thinking", "soft_cultural_competence", "soft_emotional_regulation", "soft_empathy", "soft_grief_processing", "soft_introspection", "soft_leadership", "soft_mathematical", "soft_meditation", "soft_mindfulness", "soft_networking", "soft_persuasion", "soft_philosophical", "soft_presentation", "soft_problem_solving", "soft_research", "soft_resilience", "soft_scientific", "soft_strategic_thinking", "soft_stress_management", "soft_team_collaboration", "soft_therapeutic", "tech_ai_concepts", "tech_coding_javascript", "tech_coding_python", "tech_coding_web", "visual_art"]}, "description": {"type": "string", "description": "Personal description of how this skill manifests and is used"}, "level": {"type": "integer", "description": "Current skill level: 0=beginner, 50=intermediate, 100=expert"}, "user_awareness": {"type": "integer", "description": "How aware the user is of their skill level: 0=unaware, 100=very aware"}, "user_enjoyment": {"type": "integer", "description": "How much the user enjoys using this skill: 0=dislikes, 100=loves"}, "practice_frequency": {"type": "string", "description": "How often this skill is practiced (daily, weekly, monthly, rarely)"}, "acquisition_context": {"type": "string", "description": "How and where this skill was acquired (school, work, self-taught, etc.)"}}}}, "resources": {"type": "array", "description": "User's specific resources and tools (5-20 resources recommended). Each resource creates a UserResource object linked to a GenericResource via generic_resource.", "items": {"type": "object", "required": ["specific_name", "generic_resource"], "properties": {"specific_name": {"type": "string", "description": "User's specific name for this resource (e.g., 'My MacBook Pro', 'Dad's workshop', 'Local gym membership')"}, "generic_resource": {"type": "string", "description": "Generic resource code that this user resource relates to. The user resource will be linked to a GenericResource with this code. Use 'other' codes for resources that don't fit specific categories.", "enum": ["access_other", "access_transportation", "access_walkable_area", "ai_access", "art_basic_supplies", "art_paper", "books_reading", "connectivity_internet", "cook_equipment", "cook_ingredients", "creative_drawing_materials", "creative_instrument", "creative_other", "edu_book", "edu_writing_materials", "equip_athletic_wear", "equip_blindfold", "equip_board_game", "equip_business_cards", "equip_cleaning_tools", "equip_comfortable_shoes", "equip_day_pack", "equip_exercise_mat", "equip_journal", "equip_kettle", "equip_kitchen_basic", "equip_learning_tools", "equip_other", "equip_outdoor_basic", "equip_planner", "equip_professional_attire", "equip_protective_gear", "equip_running_shoes", "equip_timer", "equip_training_shoes", "equip_walking_shoes", "equip_water_bottle", "equip_writing_tool", "equip_yoga_block", "equip_yoga_mat", "finance_activity_fee", "finance_dining", "finance_entertainment", "finance_medium", "finance_small", "finance_travel", "food_snack", "food_tea", "garden_plants", "garden_tools", "home_dining_space", "home_kitchen", "home_other", "leisure_board_games", "leisure_chess_set", "leisure_other", "leisure_puzzle", "leisure_snacks", "material_wood", "media_music", "music_instruments", "office_desk", "outdoor_footwear", "outdoor_water", "resource_other", "skill_specific_tools", "space_workshop", "specialized_other", "stationery_journal", "stationery_markers", "stationery_paper", "stationery_pen", "stationery_pencil", "tech_camera", "tech_computer", "tech_computer_internet", "tech_gaming", "tech_laptop", "tech_music_player", "tech_navigation", "tech_other", "tech_phone", "tech_screen", "tech_smartphone", "tech_vr_headset", "transport_bicycle", "transport_specialcycle", "workshop_tools"]}, "location_details": {"type": "string", "description": "Specific location within environment (e.g., 'Stored in living room closet', 'Home office desk')"}, "ownership_details": {"type": "string", "description": "How the user owns, rents, borrows, or accesses this resource"}, "availability": {"type": "string", "description": "When this resource is available for use (text description, will be converted to boolean)"}, "condition": {"type": "string", "description": "Current condition or quality of the resource (descriptive text will be parsed to: broken, poor, fair, good, excellent)"}, "transportability": {"type": "string", "description": "How easily this resource can be moved (descriptive text will be parsed to: fixed, heavy, portable, pocket)"}, "notes": {"type": "string", "description": "Additional relevant information about this resource"}}}}, "limitations": {"type": "array", "description": "Personal constraints and limitations (2-10 limitations recommended)", "items": {"type": "object", "required": ["limitation_code", "description", "severity"], "properties": {"limitation_code": {"type": "string", "description": "Limitation code from database (50 valid codes)", "enum": ["cog_attention", "cog_executive", "cog_learning", "cog_literacy", "cog_math", "cog_memory", "cog_processing", "env_allergens", "env_crowds", "env_light", "env_noise", "env_outdoor", "env_temperature", "phys_balance", "phys_cardiovascular", "phys_chronic_pain", "phys_dexterity", "phys_hearing", "phys_mobility_general", "phys_mobility_lower", "phys_mobility_upper", "phys_respiration", "phys_speech", "phys_stamina", "phys_strength", "phys_vision", "psych_anxiety", "psych_confidence", "psych_depression", "psych_emotional_regulation", "psych_motivation", "psych_social_anxiety", "psych_stress", "psych_trauma", "res_digital", "res_equipment", "res_financial", "res_space", "res_support", "res_transportation", "social_communication", "social_conflict", "social_group", "social_interpretation", "social_strangers", "time_duration", "time_evening", "time_morning", "time_regularity", "time_transitions"]}, "description": {"type": "string", "description": "Personal description of how this limitation affects the user"}, "severity": {"type": "integer", "description": "How severely this limitation impacts the user: 0=minor inconvenience, 100=major barrier"}, "frequency": {"type": "string", "description": "How often this limitation is encountered (constantly, daily, weekly, situational)"}, "coping_strategies": {"type": "string", "description": "How the user currently copes with or works around this limitation"}, "is_temporary": {"type": "boolean", "description": "Whether this limitation is expected to be temporary or permanent"}}}}, "preferences": {"type": "array", "description": "Personal preferences and patterns (8-20 preferences recommended)", "items": {"type": "object", "required": ["pref_name", "pref_description", "pref_strength"], "properties": {"pref_name": {"type": "string", "description": "Name of the preference or pattern"}, "pref_description": {"type": "string", "description": "Detailed description of the preference and how it manifests"}, "pref_strength": {"type": "integer", "description": "Strength of preference: -100=strong dislike, 0=neutral, 100=strong preference"}, "user_awareness": {"type": "integer", "description": "How aware the user is of this preference: 0=unconscious, 100=very conscious"}, "context": {"type": "string", "description": "Context where this preference applies (work, social, personal, etc.)"}}}}, "current_mood": {"type": "object", "required": ["energy_level", "stress_level", "optimism", "social_engagement"], "properties": {"energy_level": {"type": "integer", "description": "Current energy level: 0=exhausted, 50=normal, 100=highly energetic"}, "stress_level": {"type": "integer", "description": "Current stress level: 0=completely relaxed, 100=extremely stressed"}, "optimism": {"type": "integer", "description": "Current optimism about the future: 0=pessimistic, 100=very optimistic"}, "social_engagement": {"type": "integer", "description": "Current desire for social interaction: 0=want to be alone, 100=want lots of social contact"}, "mood_description": {"type": "string", "description": "Qualitative description of overall current mood and emotional state"}}}, "trust_level": {"type": "object", "required": ["value"], "properties": {"value": {"type": "number", "minimum": 0, "maximum": 100, "description": "Overall trust level with guidance systems: 0=no trust, 100=complete trust"}, "domain_scores": {"type": "object", "properties": {"goal_setting": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust in goal-setting guidance: 0=no trust, 100=complete trust. MUST be ≤ overall trust value."}, "activity_recommendation": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust in activity recommendations: 0=no trust, 100=complete trust. MUST be ≤ overall trust value."}, "personal_growth": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust in personal growth guidance: 0=no trust, 100=complete trust. MUST be ≤ overall trust value."}, "lifestyle_guidance": {"type": "number", "minimum": 0, "maximum": 100, "description": "Trust in lifestyle guidance: 0=no trust, 100=complete trust. MUST be ≤ overall trust value."}}, "description": "Trust scores for each domain. Valid domains: creative, creative_auditory, creative_craft, creative_culinary, creative_design, creative_improv, creative_music, creative_observation, creative_perform, creative_visual, creative_writing, emot_aware, emot_comfort, emot_compass, emot_express, emot_forgive, emotional, emot_joy, emot_regulate, emot_stress, explor_adren, exploratory_adventurous, explor_cultural, explor_digital, explor_improv, explor_novel, explor_risk, explor_sensory, explor_travel, explor_unknown, general, intel_audio, intel_curiosity, intel_debate, intel_language, intel_learn, intellectual, intel_problem, intel_science, intel_strategic, intel_tech, leisure_collect, leisure_entertain, leisure_festive, leisure_hobby, leisure_nature, leisure_play, leisure_recreational, leisure_relax, leisure_social, phys_balance, phys_cardio, phys_chill, phys_dance, phys_flexibility, physical, phys_martial, phys_outdoor, phys_sports, phys_strength, prod_career, prod_financial, prod_habit, prod_health, prod_home, prod_organize, prod_skill, prod_time, prod_transition, productive_practical, refl_comfort, reflective, refl_grat, refl_journal, refl_meditate, refl_micro, refl_mindful, refl_persp, refl_philos, refl_values, soc_comm, soc_conflict, soc_connecting, soc_empathy, soc_family, soc_group, social, soc_leadership, soc_network, soc_romance, spirit_commun, spirit_connect, spirit_death, spirit_nature, spirit_purpose, spirit_ritual, spirit_transced, spiritual_existential, spirit_wisdom"}, "progression_notes": {"type": "string", "description": "Notes about how trust has developed over time and current factors"}}}}}