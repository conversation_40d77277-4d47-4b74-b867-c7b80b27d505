# Activity Tailoring Comprehensive Guide

## Overview

Activity tailoring is the **core business value** of Goali - the process of transforming generic activities from our catalog into personalized, contextually-relevant experiences for individual users. This system ensures that every activity recommendation is meaningful, achievable, and aligned with the user's current state, preferences, and growth trajectory.

## Architecture Overview

### Core Components

1. **Generic Activities** (`GenericActivity` model)
   - Template activities in the system catalog
   - Contains base instructions, domains, duration ranges
   - Linked to domains via `EntityDomainRelationship`

2. **Activity Tailoring Engine** (`activity_tools.py`)
   - Transforms generic activities into personalized experiences
   - Handles both LLM-based and database-based tailoring
   - Manages workflow origin detection (frontend vs benchmark)

3. **Domain Mapping System**
   - Maps high-level domains (`physical`, `creative`) to specific sub-domains
   - Enables flexible activity discovery across domain hierarchies

4. **Context Integration**
   - User profile, environment, mood, resources
   - Trust level and growth phase considerations
   - Real-time adaptation based on user state

## Tailoring Process Flow

### 1. Activity Discovery (`query_activity_catalog`)

```
Strategy Framework → Domain Extraction → Database Query → Activity Selection
```

**Key Features:**
- **Domain Expansion**: High-level domains (`physical`) expand to sub-domains (`phys_strength`, `phys_cardio`, etc.)
- **Context Filtering**: Duration, resources, trait requirements
- **Workflow Origin Detection**: Frontend (real) vs benchmark (test) workflows

**Domain Mapping:**
```python
domain_mapping = {
    'physical': ['Physical', 'phys_balance', 'phys_cardio', 'phys_dance', 'phys_flexibility', 
                'phys_martial', 'phys_outdoor', 'phys_chill', 'phys_sports', 'phys_strength'],
    'creative': ['Creative', 'creative_craft', 'creative_auditory', 'creative_observation', 
                'creative_writing', 'creative_culinary', 'creative_design', 'creative_improv', 
                'creative_music', 'creative_perform', 'creative_visual'],
    'intellectual': ['Intellectual', 'intel_debate', 'intel_audio', 'intel_curiosity', 
                    'intel_language', 'intel_learn', 'intel_problem', 'intel_science', 
                    'intel_strategic', 'intel_tech'],
    'social': ['Social', 'soc_comm', 'soc_conflict', 'soc_empathy', 'soc_family', 
              'soc_group', 'soc_leadership', 'soc_network', 'soc_romance', 'soc_connecting'],
    'emotional': ['Emotional', 'emot_comfort', 'emot_aware', 'emot_express', 'emot_regulate', 
                 'emot_forgive', 'emot_joy', 'emot_compass', 'emot_stress']
}
```

### 2. Activity Tailoring (`tailor_activity`)

```
Generic Activity → Context Analysis → Personalization → Tailored Activity
```

**Tailoring Methods:**

#### A. LLM-Based Tailoring (Frontend Workflows)
- **Trigger**: `workflow_origin == "frontend"`
- **Process**: Uses AI to adapt activities to user context
- **Benefits**: Highly personalized, contextually aware
- **Fallback**: Database-based tailoring if LLM fails

#### B. Database-Based Tailoring (All Workflows)
- **Process**: Rule-based adaptation using user data
- **Adaptations**:
  - **Limitations**: Physical, cognitive, social constraints
  - **Skills**: Difficulty adjustment based on user proficiency
  - **Environment**: Space and resource adaptations
  - **Mood**: Tone and approach modifications

### 3. Fallback System

When no suitable activities are found:

```
No Activities Found → Enhanced Fallback Generation → Diverse Default Activities
```

**Fallback Features:**
- **Domain Diversity**: Ensures 5+ different domains
- **Mood Adaptation**: Activities tailored to current emotional state
- **Environment Awareness**: Adapts to user's current space
- **Progressive Enhancement**: Better than basic defaults

## Technical Implementation

### Key Functions

#### `query_activity_catalog()`
- **Purpose**: Discovers relevant activities from database
- **Input**: Domains, user context, constraints
- **Output**: List of candidate activities
- **Features**: Domain expansion, workflow origin detection

#### `tailor_activity()`
- **Purpose**: Personalizes generic activities
- **Methods**: LLM-based (frontend) or database-based
- **Adaptations**: Context, limitations, skills, environment

#### `_query_activities_from_db()`
- **Purpose**: Database query with domain mapping
- **Features**: Async operation, domain expansion, filtering

#### `_tailor_activity_from_db()`
- **Purpose**: Rule-based activity personalization
- **Adaptations**: User limitations, skills, environment

### Database Models

#### GenericActivity
```python
class GenericActivity(BaseActivity):
    code = models.CharField(max_length=50, unique=True)
    domain_relationships = GenericRelation('EntityDomainRelationship')
    # Inherits: name, description, duration_range, instructions
```

#### ActivityTailored
```python
class ActivityTailored(BaseActivity):
    user_profile = models.ForeignKey(UserProfile)
    generic_activity = models.ForeignKey(GenericActivity)
    base_challenge_rating = models.IntegerField()
    challengingness = models.JSONField()
    version = models.IntegerField()
    tailorization_level = models.IntegerField()
```

## Workflow Integration

### Frontend Workflows (Real Users)
1. **Detection**: `workflow_origin == "frontend"`
2. **Method**: LLM-based tailoring preferred
3. **Fallback**: Database-based if LLM fails
4. **Quality**: High personalization, context awareness

### Benchmark Workflows (Testing)
1. **Detection**: `workflow_origin == "benchmark"`
2. **Method**: Enhanced fallback activities
3. **Purpose**: Consistent testing, performance measurement
4. **Quality**: Standardized, reproducible results

## Quality Assurance

### Activity Relevance Metrics
- **Domain Diversity**: Minimum 4-5 different domains
- **Context Alignment**: Activities match user state
- **Difficulty Calibration**: Appropriate challenge level
- **Resource Feasibility**: Available resources considered

### Tailoring Quality Indicators
- **Personalization Level**: 0-100 scale
- **Context Integration**: Environment, mood, resources
- **Adaptation Depth**: Number of modifications applied
- **User Feedback**: Acceptance and completion rates

## Error Handling

### Common Issues and Solutions

#### 1. No Activities Found
- **Cause**: Overly restrictive filters
- **Solution**: Enhanced fallback generation
- **Prevention**: Domain expansion, flexible criteria

#### 2. Import Errors
- **Cause**: Incorrect model imports
- **Solution**: Use `apps.activity.models` not `apps.main.models`
- **Prevention**: Proper import statements

#### 3. Field Errors
- **Cause**: Incorrect model field references
- **Solution**: Use correct field names (e.g., `generic_skill.description`)
- **Prevention**: Model documentation review

#### 4. Async Context Errors
- **Cause**: Sync operations in async context
- **Solution**: Use `sync_to_async` wrapper
- **Prevention**: Async-aware development

## Performance Optimization

### Database Queries
- **Prefetch Relations**: `prefetch_related('domain_relationships__domain')`
- **Select Related**: `select_related('user_profile', 'generic_activity')`
- **Query Limits**: Reasonable result set sizes
- **Indexes**: Optimized for common queries

### Caching Strategy
- **Activity Catalog**: Cache frequently accessed activities
- **Domain Mappings**: Static mapping cache
- **User Context**: Session-based caching
- **Tailored Results**: Temporary result caching

## Monitoring and Analytics

### Key Metrics
- **Activity Discovery Rate**: % of successful catalog queries
- **Tailoring Success Rate**: % of successful personalizations
- **Domain Distribution**: Balance across activity types
- **User Engagement**: Activity completion rates

### Logging Strategy
- **Debug Level**: Detailed operation logs
- **Info Level**: Key process milestones
- **Warning Level**: Fallback activations
- **Error Level**: System failures

## Future Enhancements

### Planned Improvements
1. **Machine Learning Integration**: Predictive activity recommendations
2. **Advanced Context Awareness**: Real-time environment sensing
3. **Social Tailoring**: Group activity adaptations
4. **Temporal Optimization**: Time-of-day activity matching
5. **Feedback Loop Integration**: User response learning

### Technical Debt
1. **Model Consistency**: Standardize field naming
2. **Error Handling**: More granular error types
3. **Testing Coverage**: Comprehensive unit tests
4. **Documentation**: API documentation updates

## Conclusion

Activity tailoring is the heart of Goali's value proposition. By transforming generic activities into personalized experiences, we ensure that every user interaction is meaningful, relevant, and growth-oriented. The system's dual approach (LLM-based for real users, rule-based for testing) provides both high-quality personalization and reliable testing capabilities.

The continuous improvement of this system directly impacts user engagement, satisfaction, and long-term growth outcomes. As the core business differentiator, activity tailoring deserves ongoing investment in quality, performance, and innovation.
