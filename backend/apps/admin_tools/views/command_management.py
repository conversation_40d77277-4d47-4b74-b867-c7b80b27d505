"""
Enhanced admin interface for catalog management with rich GUI features.
Provides comprehensive catalog management, seeding, and visualization capabilities.
"""
import json
import subprocess
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import sys

from apps.main.services.catalog_validation_service import CatalogValidationService
from apps.admin_tools.services.command_execution_service import execute_command_sync


@staff_member_required
def command_management_page(request):
    """Render the enhanced catalog management admin page with rich GUI features."""

    # Import media class for static file management
    from apps.admin_tools.media import CatalogManagementMedia
    media_instance = CatalogManagementMedia()
    
    # Enhanced available commands with bypass and external JSON capabilities
    available_commands = {
        'generate_codes_catalog': {
            'name': 'Generate Codes Catalog',
            'description': 'Generate comprehensive codes catalog from all JSON files and update validation schemas',
            'category': 'Catalog Management',
            'parameters': [
                {
                    'name': 'output',
                    'type': 'text',
                    'required': False,
                    'description': 'Custom output path (optional)',
                    'default': ''
                },
                {
                    'name': 'update_schemas',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Update JSON validation schemas',
                    'default': True
                }
            ],
            'estimated_time': '10-30 seconds',
            'risk_level': 'low',
            'supports_bypass': False,
            'supports_external_json': False
        },
        'seed_db_45_resources': {
            'name': 'Seed Resources',
            'description': 'Seed generic resources from JSON catalog with optional external data',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-15 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'run_seeders': {
            'name': 'Run All Seeders',
            'description': 'Execute all seeding commands in correct order',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism for all commands',
                    'default': False
                }
            ],
            'estimated_time': '1-3 minutes',
            'risk_level': 'high',
            'supports_bypass': True,
            'supports_external_json': False
        },
        'seed_db_20_limitations': {
            'name': 'Seed Limitations',
            'description': 'Seed user limitations from JSON catalog with optional external data',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_30_domains': {
            'name': 'Seed Domains',
            'description': 'Seed activity domains from JSON catalog with optional external data',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_60_beliefs': {
            'name': 'Seed Beliefs',
            'description': 'Seed belief types from JSON catalog with optional external data',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_10_hexacos': {
            'name': 'Seed HEXACO Traits',
            'description': 'Seed HEXACO personality traits from JSON catalog',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_40_envs': {
            'name': 'Seed Environments',
            'description': 'Seed environment types from JSON catalog with optional external data',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_50_skill_system': {
            'name': 'Seed Skill System',
            'description': 'Seed dynamic skill system (SkillDefinition, SkillAttribute) from JSON catalog',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional seeds',
                    'accept': '.json'
                }
            ],
            'estimated_time': '10-20 seconds',
            'risk_level': 'medium',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'seed_db_55_generic_skills': {
            'name': 'Seed Generic Skills',
            'description': 'Seed GenericSkill objects for user profiles from user_profile_catalog.json',
            'category': 'Database Seeding',
            'parameters': [
                {
                    'name': 'bypass_idempotent',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Bypass idempotent mechanism and force re-seeding',
                    'default': False
                },
                {
                    'name': 'external_json',
                    'type': 'file',
                    'required': False,
                    'description': 'Upload external JSON file for additional skill definitions',
                    'accept': '.json'
                }
            ],
            'estimated_time': '5-10 seconds',
            'risk_level': 'low',
            'supports_bypass': True,
            'supports_external_json': True
        },
        'list_generic_skills': {
            'name': 'List Generic Skills',
            'description': 'List all GenericSkill codes available in the system',
            'category': 'Skill Management',
            'parameters': [
                {
                    'name': 'format',
                    'type': 'select',
                    'required': False,
                    'description': 'Output format',
                    'default': 'table',
                    'options': ['table', 'json', 'list']
                },
                {
                    'name': 'search',
                    'type': 'text',
                    'required': False,
                    'description': 'Search for skills containing this text',
                    'default': ''
                }
            ],
            'estimated_time': '1-5 seconds',
            'risk_level': 'low',
            'supports_bypass': False,
            'supports_external_json': False
        },
        'add_generic_skill': {
            'name': 'Add Generic Skill',
            'description': 'Add a new GenericSkill to the system',
            'category': 'Skill Management',
            'parameters': [
                {
                    'name': 'skill_code',
                    'type': 'text',
                    'required': True,
                    'description': 'The unique code for the skill (e.g., tech_coding_python)',
                    'default': ''
                },
                {
                    'name': 'description',
                    'type': 'text',
                    'required': True,
                    'description': 'Description of the skill',
                    'default': ''
                },
                {
                    'name': 'update',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Update the skill if it already exists',
                    'default': False
                }
            ],
            'estimated_time': '1-5 seconds',
            'risk_level': 'medium',
            'supports_bypass': False,
            'supports_external_json': False
        },
        'update_skill_schema': {
            'name': 'Update Skill Schema',
            'description': 'Update the user profile schema with current skill codes from the database',
            'category': 'Schema Management',
            'parameters': [
                {
                    'name': 'backup',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Create a backup of the original schema',
                    'default': True
                },
                {
                    'name': 'verify',
                    'type': 'checkbox',
                    'required': False,
                    'description': 'Verify the schema update after completion',
                    'default': True
                }
            ],
            'estimated_time': '1-5 seconds',
            'risk_level': 'medium',
            'supports_bypass': False,
            'supports_external_json': False
        }
    }
    
    # Get validation service for catalog validation
    validation_service = CatalogValidationService()
    
    # Validate catalog files
    catalog_status = {}
    catalogs_to_check = ['resources', 'user_profile_catalog', 'domains', 'environments']
    
    for catalog_name in catalogs_to_check:
        try:
            is_valid, errors = validation_service.validate_catalog_structure(catalog_name)
            catalog_status[catalog_name] = {
                'valid': is_valid,
                'errors': errors,
                'last_checked': datetime.now().isoformat()
            }
        except Exception as e:
            catalog_status[catalog_name] = {
                'valid': False,
                'errors': [str(e)],
                'last_checked': datetime.now().isoformat()
            }
    
    context = {
        'available_commands': available_commands,
        'catalog_status': catalog_status,
        'page_title': 'Catalog Management',
        'total_commands': len(available_commands),
        'valid_catalogs': sum(1 for status in catalog_status.values() if status['valid']),
        'total_catalogs': len(catalog_status),
        'media': media_instance.media
    }
    
    return render(request, 'admin_tools/command_management.html', context)


@staff_member_required
@require_http_methods(["POST"])
@csrf_exempt
def execute_command(request):
    """Execute a management command with enhanced parameter handling."""

    try:
        # Handle both JSON and form data (for file uploads)
        if request.content_type and 'application/json' in request.content_type:
            data = json.loads(request.body)
            command_name = data.get('command')
            parameters = data.get('parameters', {})
            files = {}
        else:
            # Handle form data with files
            command_name = request.POST.get('command')
            parameters = dict(request.POST)
            files = dict(request.FILES)
            # Clean up parameters (remove command from parameters)
            if 'command' in parameters:
                del parameters['command']

        if not command_name:
            return JsonResponse({
                'success': False,
                'error': 'Command name is required'
            }, status=400)

        # Enhanced command validation with all seeding commands
        available_commands = [
            'generate_codes_catalog',
            'seed_db_10_hexacos',
            'seed_db_20_limitations',
            'seed_db_30_domains',
            'seed_db_40_envs',
            'seed_db_45_resources',
            'seed_db_50_skill_system',
            'seed_db_55_generic_skills',
            'seed_db_60_beliefs',
            'run_seeders',
            'list_generic_skills',
            'add_generic_skill',
            'update_skill_schema'
        ]

        if command_name not in available_commands:
            return JsonResponse({
                'success': False,
                'error': f'Command not allowed: {command_name}'
            }, status=400)
        
        # Handle bypass idempotent mechanism
        original_env = os.environ.get('SKIP_SEEDER_IDEMPOTENCY_CHECK')
        bypass_idempotent = parameters.get('bypass_idempotent', False)

        if bypass_idempotent:
            os.environ['SKIP_SEEDER_IDEMPOTENCY_CHECK'] = 'true'

        # Handle external JSON file upload
        external_json_path = None
        if 'external_json' in files and files['external_json']:
            try:
                uploaded_file = files['external_json'][0] if isinstance(files['external_json'], list) else files['external_json']
                # Save uploaded file temporarily
                temp_dir = Path(settings.BASE_DIR) / 'temp' / 'catalog_uploads'
                temp_dir.mkdir(parents=True, exist_ok=True)
                external_json_path = temp_dir / f"{command_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

                with open(external_json_path, 'wb') as f:
                    for chunk in uploaded_file.chunks():
                        f.write(chunk)

                # Validate JSON format
                with open(external_json_path, 'r', encoding='utf-8') as f:
                    json.load(f)  # This will raise an exception if invalid JSON

            except Exception as e:
                return JsonResponse({
                    'success': False,
                    'error': f'Invalid JSON file: {str(e)}'
                }, status=400)

        # Execute command using the centralized service with real-time progress reporting
        user_id = getattr(request.user, 'id', None)
        session_id = request.session.session_key

        return execute_command_sync(
            command_name=command_name,
            parameters=parameters,
            external_json_path=str(external_json_path) if external_json_path else None,
            bypass_idempotent=bypass_idempotent,
            user_id=str(user_id) if user_id else None,
            session_id=session_id
        )
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON in request body'
        }, status=400)
    
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Unexpected error: {str(e)}'
        }, status=500)

    finally:
        # Restore original environment variable
        if bypass_idempotent:
            if original_env is not None:
                os.environ['SKIP_SEEDER_IDEMPOTENCY_CHECK'] = original_env
            else:
                os.environ.pop('SKIP_SEEDER_IDEMPOTENCY_CHECK', None)

        # Clean up temporary files
        if external_json_path and external_json_path.exists():
            try:
                external_json_path.unlink()
            except Exception:
                pass  # Ignore cleanup errors


@staff_member_required
@require_http_methods(["GET"])
def validate_catalog(request, catalog_name):
    """Validate a specific catalog file."""
    
    validation_service = CatalogValidationService()
    
    try:
        is_valid, errors = validation_service.validate_catalog_structure(catalog_name)
        
        return JsonResponse({
            'success': True,
            'catalog_name': catalog_name,
            'valid': is_valid,
            'errors': errors,
            'validated_at': datetime.now().isoformat()
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


@staff_member_required
@require_http_methods(["GET"])
def get_command_status(request):
    """Get the current status of all commands and catalogs."""
    
    validation_service = CatalogValidationService()
    
    # Check catalog status
    catalog_status = {}
    catalogs_to_check = ['resources', 'user_profile_catalog', 'domains', 'environments']
    
    for catalog_name in catalogs_to_check:
        try:
            is_valid, errors = validation_service.validate_catalog_structure(catalog_name)
            catalog_status[catalog_name] = {
                'valid': is_valid,
                'errors': errors,
                'last_checked': datetime.now().isoformat()
            }
        except Exception as e:
            catalog_status[catalog_name] = {
                'valid': False,
                'errors': [str(e)],
                'last_checked': datetime.now().isoformat()
            }
    
    # Check if comprehensive catalog exists
    comprehensive_catalog_path = settings.BASE_DIR / 'data' / 'authoritative_catalogs' / 'comprehensive_codes_catalog.json'
    comprehensive_catalog_exists = comprehensive_catalog_path.exists()
    
    return JsonResponse({
        'success': True,
        'catalog_status': catalog_status,
        'comprehensive_catalog_exists': comprehensive_catalog_exists,
        'checked_at': datetime.now().isoformat()
    })


@staff_member_required
@require_http_methods(["GET"])
def get_catalog_data(request, catalog_name):
    """Get detailed catalog data for visualization."""

    try:
        catalog_path = Path(settings.BASE_DIR) / 'data' / 'authoritative_catalogs' / f'{catalog_name}.json'

        if not catalog_path.exists():
            return JsonResponse({
                'success': False,
                'error': f'Catalog file not found: {catalog_name}.json'
            }, status=404)

        with open(catalog_path, 'r', encoding='utf-8') as f:
            catalog_data = json.load(f)

        # Add metadata about the catalog
        file_stats = catalog_path.stat()
        catalog_info = {
            'name': catalog_name,
            'file_size': file_stats.st_size,
            'last_modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
            'data': catalog_data
        }

        return JsonResponse({
            'success': True,
            'catalog': catalog_info
        })

    except json.JSONDecodeError as e:
        return JsonResponse({
            'success': False,
            'error': f'Invalid JSON in catalog file: {str(e)}'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error loading catalog: {str(e)}'
        }, status=500)


@staff_member_required
@require_http_methods(["GET"])
def list_available_catalogs(request):
    """List all available catalog files with metadata."""

    try:
        catalogs_path = Path(settings.BASE_DIR) / 'data' / 'authoritative_catalogs'
        catalogs = []

        for json_file in catalogs_path.glob('*.json'):
            if json_file.name.startswith('comprehensive_codes_catalog'):
                continue  # Skip generated files

            try:
                file_stats = json_file.stat()
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Extract metadata
                metadata = data.get('metadata', {})

                # Count items in catalog
                item_count = 0
                for key, value in data.items():
                    if key != 'metadata' and isinstance(value, (dict, list)):
                        if isinstance(value, dict):
                            item_count += sum(len(v) if isinstance(v, list) else 1 for v in value.values())
                        else:
                            item_count += len(value)

                catalog_info = {
                    'name': json_file.stem,
                    'display_name': json_file.stem.replace('_', ' ').title(),
                    'file_size': file_stats.st_size,
                    'last_modified': datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                    'version': metadata.get('version', 'Unknown'),
                    'description': metadata.get('description', 'No description available'),
                    'item_count': item_count
                }

                catalogs.append(catalog_info)

            except Exception as e:
                # Add error info for problematic files
                catalogs.append({
                    'name': json_file.stem,
                    'display_name': json_file.stem.replace('_', ' ').title(),
                    'error': str(e),
                    'file_size': json_file.stat().st_size,
                    'last_modified': datetime.fromtimestamp(json_file.stat().st_mtime).isoformat()
                })

        return JsonResponse({
            'success': True,
            'catalogs': sorted(catalogs, key=lambda x: x['name'])
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': f'Error listing catalogs: {str(e)}'
        }, status=500)
