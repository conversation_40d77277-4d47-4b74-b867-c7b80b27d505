"""
Django management command to list all GenericSkill codes in the system
This command can be used through the admin interface to see available skill codes
"""
from django.core.management.base import BaseCommand
from apps.user.models import GenericSkill
import json


class Command(BaseCommand):
    help = 'List all GenericSkill codes available in the system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            choices=['table', 'json', 'list'],
            default='table',
            help='Output format (table, json, or list)'
        )
        parser.add_argument(
            '--search',
            type=str,
            help='Search for skills containing this text'
        )

    def handle(self, *args, **options):
        output_format = options['format']
        search_term = options.get('search')

        self.stdout.write("📋 GenericSkill Codes in System")
        self.stdout.write("=" * 60)

        # Get all skills
        skills = GenericSkill.objects.all().order_by('code')
        
        # Apply search filter if provided
        if search_term:
            skills = skills.filter(
                code__icontains=search_term
            ) | skills.filter(
                description__icontains=search_term
            )
            self.stdout.write(f"🔍 Filtering by: '{search_term}'")
            self.stdout.write("-" * 60)

        if not skills.exists():
            if search_term:
                self.stdout.write(f"❌ No skills found matching '{search_term}'")
            else:
                self.stdout.write("❌ No skills found in the system")
                self.stdout.write("💡 Run 'python manage.py seed_generic_skills.py' to populate skills")
            return

        # Output in requested format
        if output_format == 'json':
            skills_data = []
            for skill in skills:
                skills_data.append({
                    'code': skill.code,
                    'description': skill.description
                })
            self.stdout.write(json.dumps(skills_data, indent=2))
            
        elif output_format == 'list':
            for skill in skills:
                self.stdout.write(skill.code)
                
        else:  # table format
            self.stdout.write(f"{'Code':<25} | Description")
            self.stdout.write("-" * 80)
            
            for skill in skills:
                code = skill.code[:24]  # Truncate if too long
                description = skill.description[:50]  # Truncate if too long
                if len(skill.description) > 50:
                    description += "..."
                self.stdout.write(f"{code:<25} | {description}")

        self.stdout.write("-" * 60)
        self.stdout.write(f"📊 Total skills: {skills.count()}")
        
        if search_term:
            total_skills = GenericSkill.objects.count()
            self.stdout.write(f"📊 Total skills in system: {total_skills}")

        self.stdout.write("\n💡 Usage examples:")
        self.stdout.write("  - Add new skill: python manage.py add_generic_skill 'new_skill_code' 'Description'")
        self.stdout.write("  - Search skills: python manage.py list_generic_skills --search 'python'")
        self.stdout.write("  - JSON output: python manage.py list_generic_skills --format json")
