"""
Django management command to update the user profile schema with current skill codes
This ensures the validation schema stays in sync with available GenericSkill objects
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from apps.user.models import GenericSkill
import json
from pathlib import Path


class Command(BaseCommand):
    help = 'Update the user profile schema with current skill codes from the database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--backup',
            action='store_true',
            help='Create a backup of the original schema',
            default=True
        )
        parser.add_argument(
            '--verify',
            action='store_true',
            help='Verify the schema update after completion',
            default=True
        )

    def handle(self, *args, **options):
        create_backup = options['backup']
        verify_update = options['verify']

        self.stdout.write("🔧 Updating User Profile Schema with Current Skill Codes")
        self.stdout.write("=" * 70)

        try:
            # Load current schema
            schema_path = Path(settings.BASE_DIR) / 'schemas' / 'user_profile.schema.json'
            
            if not schema_path.exists():
                raise CommandError(f"Schema file not found at {schema_path}")

            with open(schema_path, 'r') as f:
                schema = json.load(f)
            self.stdout.write(f"✅ Loaded schema from {schema_path}")

            # Get current skill codes from database
            skill_codes = list(GenericSkill.objects.values_list('code', flat=True).order_by('code'))
            
            if not skill_codes:
                raise CommandError("No skill codes found in database. Run 'python manage.py seed_generic_skills.py' first.")

            self.stdout.write(f"📊 Found {len(skill_codes)} skill codes in database")

            # Update the schema
            try:
                # Navigate to the skills enum in the schema
                skills_enum = schema['properties']['skills']['items']['properties']['skill_code']['enum']
                old_count = len(skills_enum)
                
                self.stdout.write(f"📋 Current schema has {old_count} skill codes")
                self.stdout.write(f"🔄 Updating to {len(skill_codes)} skill codes")
                
                # Create backup if requested
                if create_backup:
                    backup_path = schema_path.with_suffix('.schema.json.backup')
                    with open(backup_path, 'w') as f:
                        json.dump(schema, f, indent=2)
                    self.stdout.write(f"💾 Created backup at {backup_path}")
                
                # Update the enum with current skill codes
                schema['properties']['skills']['items']['properties']['skill_code']['enum'] = skill_codes
                
                # Update the description to reflect the current count
                schema['properties']['skills']['items']['properties']['skill_code']['description'] = f"Skill code from database ({len(skill_codes)} valid codes)"
                
                # Write updated schema
                with open(schema_path, 'w') as f:
                    json.dump(schema, f, indent=2)
                
                self.stdout.write(f"✅ Updated schema successfully!")
                self.stdout.write(f"📈 Skill codes: {old_count} → {len(skill_codes)}")
                
                # Show some examples of the new codes
                self.stdout.write(f"\n📋 Sample skill codes now available:")
                for code in skill_codes[:10]:  # Show first 10
                    self.stdout.write(f"  - {code}")
                if len(skill_codes) > 10:
                    self.stdout.write(f"  ... and {len(skill_codes) - 10} more")
                
            except KeyError as e:
                raise CommandError(f"Schema structure error: {e}. The schema structure may have changed.")

            # Verify the update if requested
            if verify_update:
                self.stdout.write("\n🔍 Verifying Schema Update")
                self.stdout.write("-" * 40)
                
                # Reload and verify
                with open(schema_path, 'r') as f:
                    updated_schema = json.load(f)
                
                updated_skills_enum = updated_schema['properties']['skills']['items']['properties']['skill_code']['enum']
                db_skill_codes = set(GenericSkill.objects.values_list('code', flat=True))
                schema_skill_codes = set(updated_skills_enum)
                
                # Check if they match
                if db_skill_codes == schema_skill_codes:
                    self.stdout.write("✅ Schema and database are perfectly synchronized!")
                    self.stdout.write(f"📊 Both contain {len(db_skill_codes)} skill codes")
                else:
                    missing_in_schema = db_skill_codes - schema_skill_codes
                    extra_in_schema = schema_skill_codes - db_skill_codes
                    
                    if missing_in_schema:
                        self.stdout.write(f"⚠️ Missing in schema: {missing_in_schema}")
                    if extra_in_schema:
                        self.stdout.write(f"⚠️ Extra in schema: {extra_in_schema}")
                    
                    raise CommandError("Schema verification failed - database and schema are not synchronized")

            self.stdout.write("\n🎉 Schema update completed successfully!")
            self.stdout.write("✅ User profile validation will now work with all available skill codes")

        except Exception as e:
            raise CommandError(f"Failed to update schema: {str(e)}")
