"""
Django management command to add a new GenericSkill to the system
This command can be used through the admin interface to add missing skill codes
"""
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.user.models import GenericSkill
import json


class Command(BaseCommand):
    help = 'Add a new GenericSkill to the system'

    def add_arguments(self, parser):
        parser.add_argument(
            'skill_code',
            type=str,
            help='The unique code for the skill (e.g., tech_coding_python)'
        )
        parser.add_argument(
            'description',
            type=str,
            help='Description of the skill'
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='Update the skill if it already exists',
            default=False
        )

    def handle(self, *args, **options):
        skill_code = options['skill_code']
        description = options['description']
        update = options['update']

        self.stdout.write(f"🔧 Adding GenericSkill: {skill_code}")
        self.stdout.write(f"📝 Description: {description}")

        try:
            with transaction.atomic():
                # Check if skill already exists
                existing_skill = GenericSkill.objects.filter(code=skill_code).first()
                
                if existing_skill:
                    if update:
                        existing_skill.description = description
                        existing_skill.save()
                        self.stdout.write(
                            self.style.SUCCESS(f"✅ Updated existing skill: {skill_code}")
                        )
                        self.stdout.write(f"📝 New description: {description}")
                    else:
                        self.stdout.write(
                            self.style.WARNING(f"⚠️ Skill already exists: {skill_code}")
                        )
                        self.stdout.write(f"📝 Current description: {existing_skill.description}")
                        self.stdout.write("💡 Use --update flag to update the description")
                        return
                else:
                    # Create new skill
                    skill = GenericSkill.objects.create(
                        code=skill_code,
                        description=description
                    )
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Created new skill: {skill_code}")
                    )
                    self.stdout.write(f"📝 Description: {description}")

                # Show current total
                total_skills = GenericSkill.objects.count()
                self.stdout.write(f"📊 Total skills in system: {total_skills}")

        except Exception as e:
            raise CommandError(f"Failed to add skill: {str(e)}")

        self.stdout.write(self.style.SUCCESS("🎉 Operation completed successfully!"))
