"""
Django management command to seed GenericSkill objects from user_profile_catalog.json
This follows the same pattern as other seeding commands and integrates with run_seeders.py
"""
import os
import json
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from apps.user.models import GenericSkill
from apps.main.models import AppliedSeedingCommand


class Command(BaseCommand):
    help = 'Seeds GenericSkill objects from user_profile_catalog.json. Skips if already applied by run_seeders.'
    
    # Command name for idempotency tracking
    COMMAND_NAME = 'seed_db_55_generic_skills'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.catalog_path = Path(settings.BASE_DIR) / 'data' / 'authoritative_catalogs' / 'generic_skill_catalog.json'
        self._catalog_data = None

    def add_arguments(self, parser):
        parser.add_argument(
            '--bypass-idempotent',
            action='store_true',
            help='Bypass idempotent mechanism and force re-seeding',
            default=False
        )
        parser.add_argument(
            '--external-json',
            type=str,
            help='Path to external JSON file for additional skill definitions',
            default=None
        )

    def load_catalog(self):
        """Load catalog data from JSON file."""
        if self._catalog_data is None:
            try:
                with open(self.catalog_path, 'r', encoding='utf-8') as f:
                    self._catalog_data = json.load(f)
                    self.stdout.write(f"✅ Loaded catalog from {self.catalog_path}")
            except FileNotFoundError:
                raise CommandError(f"❌ Catalog file not found: {self.catalog_path}")
            except json.JSONDecodeError as e:
                raise CommandError(f"❌ Invalid JSON in catalog: {e}")
        return self._catalog_data

    def load_external_json(self, external_path):
        """Load additional skill definitions from external JSON file."""
        try:
            with open(external_path, 'r', encoding='utf-8') as f:
                external_data = json.load(f)
                self.stdout.write(f"✅ Loaded external data from {external_path}")
                return external_data.get('generic_skills', [])
        except FileNotFoundError:
            raise CommandError(f"❌ External JSON file not found: {external_path}")
        except json.JSONDecodeError as e:
            raise CommandError(f"❌ Invalid JSON in external file: {e}")

    def handle(self, *args, **options):
        bypass_idempotent = options.get('bypass_idempotent', False)
        external_json = options.get('external_json')

        # Check idempotency unless bypassed
        if not bypass_idempotent and AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
            self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
            return

        self.stdout.write("🌱 Seeding GenericSkill objects from generic_skill_catalog.json")
        self.stdout.write("=" * 70)

        try:
            # Load the main catalog
            catalog_data = self.load_catalog()
            
            # Extract generic skills from main catalog
            skill_definitions = catalog_data.get('generic_skills', [])
            if not skill_definitions:
                raise CommandError("❌ No generic_skills found in catalog")

            # Load external generic skills if provided
            if external_json:
                external_skills = self.load_external_json(external_json)
                skill_definitions.extend(external_skills)
                self.stdout.write(f"📊 Added {len(external_skills)} external generic skills")

            self.stdout.write(f"📊 Found {len(skill_definitions)} generic skills")

            # Execute the seeding logic within a transaction
            with transaction.atomic():
                created_count, updated_count = self.seed_generic_skills(skill_definitions)

                # Record successful completion (only if not bypassed)
                if not bypass_idempotent:
                    AppliedSeedingCommand.objects.get_or_create(command_name=self.COMMAND_NAME)

                self.stdout.write("\n" + "=" * 70)
                self.stdout.write("📊 Seeding Results:")
                self.stdout.write(f"✅ Created: {created_count} skills")
                self.stdout.write(f"🔄 Updated: {updated_count} skills")
                self.stdout.write(f"📈 Total skills in database: {GenericSkill.objects.count()}")
                self.stdout.write(f"✅ Command '{self.COMMAND_NAME}' completed successfully!")

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"❌ Command '{self.COMMAND_NAME}' failed: {str(e)}"))
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e

    @transaction.atomic
    def seed_generic_skills(self, skill_definitions):
        """Seed GenericSkill objects from skill definitions."""
        created_count = 0
        updated_count = 0

        for skill_data in skill_definitions:
            code = skill_data.get('code')
            name = skill_data.get('name', '')
            description = skill_data.get('description', '')

            if not code:
                self.stdout.write(f"⚠️ Skipping skill with no code: {skill_data}")
                continue

            # Create or update the GenericSkill
            skill, created = GenericSkill.objects.get_or_create(
                code=code,
                defaults={
                    'description': description
                }
            )

            if created:
                created_count += 1
                self.stdout.write(f"✅ Created skill: {code} - {description[:50]}...")
            else:
                # Update description if it's different
                if skill.description != description:
                    skill.description = description
                    skill.save()
                    updated_count += 1
                    self.stdout.write(f"🔄 Updated skill: {code} - {description[:50]}...")
                else:
                    self.stdout.write(f"⏭️ Skill already exists: {code}")

        return created_count, updated_count
