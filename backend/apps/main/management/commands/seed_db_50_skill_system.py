import os
import json
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.conf import settings
from apps.user.models import (
    SkillAttribute, AttributeTraitInfluence, SkillDefinition,
    SkillAttributeComposition, SkillDomainApplication
)
from apps.activity.models import GenericDomain
from apps.user.models import GenericTrait
from apps.main.models import AppliedSeedingCommand


class Command(BaseCommand):
    help = 'Seeds the dynamic skill system (attributes, influences, definitions, applications) from JSON catalog. Skips if already applied by run_seeders.'
    # Re-add command name definition
    COMMAND_NAME = 'seed_db_50_skill_system'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.catalog_path = Path(settings.BASE_DIR) / 'data' / 'authoritative_catalogs' / 'skill_system_catalog.json'
        self._catalog_data = None

    def load_skills_catalog(self):
        """Load skill system data from JSON catalog file."""
        if self._catalog_data is None:
            try:
                with open(self.catalog_path, 'r', encoding='utf-8') as f:
                    self._catalog_data = json.load(f)
                    self.stdout.write(f"Loaded skill system catalog from {self.catalog_path}")
            except FileNotFoundError:
                raise CommandError(f"Skill system catalog file not found: {self.catalog_path}")
            except json.JSONDecodeError as e:
                raise CommandError(f"Invalid JSON in skill system catalog: {e}")
        return self._catalog_data

    def handle(self, *args, **kwargs):
        # Re-add the initial check for the command itself
        if AppliedSeedingCommand.objects.filter(command_name=self.COMMAND_NAME).exists():
            self.stdout.write(self.style.WARNING(f"Command '{self.COMMAND_NAME}' has already been applied by run_seeders. Skipping."))
            return

        self.stdout.write(self.style.SUCCESS(f"Running command '{self.COMMAND_NAME}' logic (will update/create individual items)..."))

        try:
            # Load skill system data from JSON catalog
            catalog_data = self.load_skills_catalog()
            
            # 2. Execute the seeding logic within a transaction
            # Note: Individual methods already use @transaction.atomic,
            # but wrapping the whole handle ensures the AppliedSeedingCommand
            # is only created if ALL steps succeed.
            with transaction.atomic():
                # Seed the fundamental attributes
                self.seed_fundamental_attributes(catalog_data)

                # Seed the trait influences
                self.seed_trait_influences(catalog_data)

                # Seed skill definitions
                self.seed_skill_definitions(catalog_data)

                # Seed domain applications
                self.seed_domain_applications(catalog_data)

                # Ensure AppliedSeedingCommand.objects.create(...) remains removed from here.
                # run_seeders.py handles the recording upon successful completion of this command.
                self.stdout.write(self.style.SUCCESS(f"Finished command '{self.COMMAND_NAME}' logic! Skill system seeded."))

        except Exception as e:
            # Catch any exception during the transaction
            self.stderr.write(self.style.ERROR(f"Command '{self.COMMAND_NAME}' failed internally: {str(e)}"))
            # Re-raise the exception so run_seeders knows it failed and won't record success
            raise CommandError(f"Command '{self.COMMAND_NAME}' failed: {e}") from e

    @transaction.atomic
    def seed_fundamental_attributes(self, catalog_data):
        """Seed the core skill attributes that form the foundation of all skills."""
        self.stdout.write("Seeding fundamental skill attributes from catalog...")
        
        # Extract skill attributes from catalog
        skill_attributes = catalog_data.get('skill_attributes', {})
        all_attributes = []
        
        # Combine all attribute categories
        for category, attributes in skill_attributes.items():
            all_attributes.extend(attributes)
        
        # Create or update attributes
        attribute_count = 0
        for attr_data in all_attributes:
            attribute, created = SkillAttribute.objects.update_or_create(
                code=attr_data['code'],
                defaults={
                    'name': attr_data['name'],
                    'description': attr_data['description'],
                    'base_decay_rate': attr_data['base_decay_rate'],
                    'development_difficulty': attr_data['development_difficulty'],
                    'development_timeframe': attr_data['development_timeframe']
                }
            )
            
            if created:
                attribute_count += 1
                
        self.stdout.write(self.style.SUCCESS(f"Created {attribute_count} attributes from catalog."))

    @transaction.atomic
    def seed_trait_influences(self, catalog_data):
        """Seed the influences between personality traits and skill attributes."""
        self.stdout.write("Seeding trait-attribute influences from catalog...")
        
        # Extract trait influences from catalog
        influences_data = catalog_data.get('trait_influences', [])
        
        # Create or update influences
        influence_count = 0
        for influence_data in influences_data:
            trait_code = influence_data['trait_code']
            attr_code = influence_data['attribute_code']
            impact = influence_data['impact']
            
            try:
                trait = GenericTrait.objects.get(code=trait_code)
                attribute = SkillAttribute.objects.get(code=attr_code)
                
                influence, created = AttributeTraitInfluence.objects.update_or_create(
                    generic_trait=trait,
                    attribute=attribute,
                    defaults={'impact': impact}
                )
                
                if created:
                    influence_count += 1
                    
            except (GenericTrait.DoesNotExist, SkillAttribute.DoesNotExist):
                self.stdout.write(self.style.WARNING(f"Could not create influence: {trait_code} → {attr_code}"))
                continue
                
        self.stdout.write(self.style.SUCCESS(f"Created {influence_count} trait influences from catalog."))

    @transaction.atomic
    def seed_skill_definitions(self, catalog_data):
        """Seed sample skill definitions composed of attributes."""
        self.stdout.write("Seeding skill definitions and compositions from catalog...")
        
        # Extract skill definitions from catalog
        skill_definitions = catalog_data.get('skill_definitions', [])
        
        # Create or update skills
        skill_count = 0
        composition_count = 0
        
        for skill_data in skill_definitions:
            # Get or create the skill definition
            skill, created = SkillDefinition.objects.update_or_create(
                code=skill_data['code'],
                defaults={
                    'name': skill_data['name'],
                    'description': skill_data['description'],
                    'tags': skill_data['tags']
                }
            )
            
            if created:
                skill_count += 1
                
            # Create or update compositions
            for composition_data in skill_data['attribute_compositions']:
                attr_code = composition_data['attribute_code']
                weight = composition_data['weight']
                
                try:
                    attribute = SkillAttribute.objects.get(code=attr_code)
                    
                    composition, comp_created = SkillAttributeComposition.objects.update_or_create(
                        skill=skill,
                        attribute=attribute,
                        defaults={'weight': weight}
                    )
                    
                    if comp_created:
                        composition_count += 1
                        
                except SkillAttribute.DoesNotExist:
                    self.stdout.write(self.style.WARNING(f"Attribute not found: {attr_code}"))
                    continue
                    
        self.stdout.write(self.style.SUCCESS(f"Created {skill_count} skills with {composition_count} attribute compositions from catalog."))

    @transaction.atomic
    def seed_domain_applications(self, catalog_data):
        """Seed domain applications for the skills."""
        self.stdout.write("Seeding skill-domain applications from catalog...")
        
        # Extract domain applications from catalog
        domain_applications = catalog_data.get('domain_applications', [])
        
        # Create or update domain applications
        application_count = 0
        for app_data in domain_applications:
            skill_code = app_data['skill_code']
            domain_code = app_data['domain_code']
            relevance = app_data['relevance']
            transfer = app_data['transfer_coefficient']
            properties = app_data['domain_specific_properties']
            
            try:
                skill = SkillDefinition.objects.get(code=skill_code)
                domain = GenericDomain.objects.get(code=domain_code)
                
                application, created = SkillDomainApplication.objects.update_or_create(
                    skill=skill,
                    domain=domain,
                    defaults={
                        'relevance': relevance,
                        'transfer_coefficient': transfer,
                        'domain_specific_properties': properties
                    }
                )
                
                if created:
                    application_count += 1
                    
            except SkillDefinition.DoesNotExist:
                self.stdout.write(self.style.WARNING(
                    f"SkillDefinition not found: {skill_code}"
                ))
                continue
            except GenericDomain.DoesNotExist:
                self.stdout.write(self.style.WARNING(
                    f"GenericDomain not found: {domain_code}"
                ))
                continue
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error creating domain application for {skill_code} → {domain_code}: {str(e)}"
                ))
                continue
                
        self.stdout.write(self.style.SUCCESS(f"Created {application_count} domain applications from catalog."))
