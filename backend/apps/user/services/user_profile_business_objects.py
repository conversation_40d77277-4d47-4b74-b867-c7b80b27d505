"""
User Profile Business Objects

Pydantic models that mirror the JSON schema structure for user profile import.
These models provide validation and type safety for the import process.
"""

from pydantic import BaseModel, Field, validator, model_validator, field_validator
from typing import Optional, List, Dict, Any, Union
from datetime import date
from enum import Enum
import re


# Supporting Enums
class SpaceSizeEnum(str, Enum):
    small = "small"
    medium = "medium"
    large = "large"
    very_large = "very_large"


class FrequencyEnum(str, Enum):
    daily = "daily"
    weekly = "weekly"
    monthly = "monthly"
    rarely = "rarely"
    constantly = "constantly"
    situational = "situational"


class CategoryEnum(str, Enum):
    person = "person"
    media = "media"
    experience = "experience"
    philosophy = "philosophy"
    nature = "nature"


# Nested Environment Objects
class PhysicalPropertiesBO(BaseModel):
    """Physical properties of an environment"""
    rurality: Optional[int] = Field(None, ge=0, le=100, description="Urban to rural scale: 0=urban, 100=rural")
    noise_level: Optional[int] = Field(None, ge=0, le=100, description="Ambient noise level: 0=silent, 100=very noisy")
    light_quality: Optional[int] = Field(None, ge=0, le=100, description="Natural light quality: 0=poor, 100=excellent")
    accessibility: Optional[int] = Field(None, ge=0, le=100, description="Physical accessibility level: 0=poor, 100=excellent")
    air_quality: Optional[int] = Field(None, ge=0, le=100, description="Air quality rating: 0=poor, 100=excellent")
    has_natural_elements: Optional[bool] = Field(None, description="Presence of natural elements like plants, water, etc.")
    space_size: Optional[SpaceSizeEnum] = Field(None, description="Available space size")


class SocialContextBO(BaseModel):
    """Social context of an environment"""
    privacy_level: Optional[int] = Field(None, ge=0, le=100, description="Level of privacy available: 0=public, 100=completely private")
    typical_occupancy: Optional[int] = Field(None, ge=0, le=100, description="Typical occupancy level: 0=empty, 100=extremely crowded")
    social_interaction_level: Optional[int] = Field(None, ge=0, le=100, description="Level of social interactions: 0=none, 100=constant interaction")
    formality_level: Optional[int] = Field(None, ge=0, le=100, description="Formality level: 0=very casual, 100=very formal")
    safety_level: Optional[int] = Field(None, ge=0, le=100, description="Safety level: 0=unsafe, 100=very safe")
    supervision_level: Optional[str] = Field(None, description="Level of supervision: none, minimal, moderate, high, constant")
    cultural_diversity: Optional[int] = Field(None, ge=0, le=100, description="Cultural diversity: 0=homogeneous, 100=highly diverse")


class ActivitySupportBO(BaseModel):
    """Activity support capabilities of an environment"""
    creative_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for creative/artistic activities")
    physical_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for physical exercise/movement")
    intellectual_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for learning/intellectual pursuits")
    social_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for social gatherings/interactions")
    reflective_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for meditation/journaling/reflection")
    productive_activities: Optional[int] = Field(None, ge=0, le=100, description="Support for work/productive tasks")


class PsychologicalQualitiesBO(BaseModel):
    """Psychological qualities of an environment"""
    restorative_quality: Optional[int] = Field(None, ge=0, le=100, description="How restorative and rejuvenating the environment feels")
    stimulation_level: Optional[int] = Field(None, ge=0, le=100, description="Level of mental stimulation provided")
    aesthetic_appeal: Optional[int] = Field(None, ge=0, le=100, description="Visual beauty and aesthetic appeal")
    novelty_level: Optional[int] = Field(None, ge=0, le=100, description="Very familiar (0) to completely novel (100) for this specific user")
    comfort_level: Optional[int] = Field(None, ge=0, le=100, description="Physical and emotional comfort level")
    personal_significance: Optional[int] = Field(None, ge=0, le=100, description="Personal meaning and emotional attachment")
    emotional_associations: Optional[Dict[str, Any]] = Field(None, description="User's emotional associations with this specific environment")


# Core Component Objects
class UserAccountBO(BaseModel):
    """User account information"""
    username: str = Field(..., min_length=3, max_length=150, description="Unique username for login")
    email: str = Field(..., description="User's email address")
    first_name: Optional[str] = Field(None, description="User's first name")
    last_name: Optional[str] = Field(None, description="User's last name")
    password: Optional[str] = Field(None, min_length=8, description="User password (minimum 8 characters)")

    @validator('username')
    def validate_username(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('Username must contain only letters, numbers, and underscores')
        return v

    @validator('email')
    def validate_email(cls, v):
        if '@' not in v or '.' not in v.split('@')[-1]:
            raise ValueError('Invalid email format')
        return v


class DemographicsBO(BaseModel):
    """Demographics information"""
    full_name: str = Field(..., min_length=1, max_length=255, description="User's complete full name")
    age: int = Field(..., ge=13, le=120, description="User's current age in years")
    gender: str = Field(..., min_length=1, max_length=50, description="User's gender identity")
    location: str = Field(..., min_length=1, max_length=255, description="User's current location or residence")
    language: str = Field(..., min_length=1, max_length=50, description="User's preferred or native language")
    occupation: str = Field(..., min_length=1, max_length=255, description="User's current occupation or professional role")


class UserEnvironmentBO(BaseModel):
    """User environment information"""
    environment_name: str = Field(..., min_length=1, max_length=255, description="User's personal name for their environment")
    environment_description: str = Field(..., min_length=1, description="Detailed description of the environment")
    is_current: bool = Field(..., description="Whether this is the user's current primary environment")
    generic_environment_code: Optional[str] = Field(None, description="Code referencing a generic environment archetype")
    effective_start: Optional[str] = Field(None, description="Date when user started using this environment (YYYY-MM-DD)")
    effective_end: Optional[str] = Field(None, description="Date when user will stop using this environment (YYYY-MM-DD)")
    physical_properties: Optional[PhysicalPropertiesBO] = None
    social_context: Optional[SocialContextBO] = None
    activity_support: Optional[ActivitySupportBO] = None
    psychological_qualities: Optional[PsychologicalQualitiesBO] = None

    @validator('effective_start', 'effective_end')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                date.fromisoformat(v)
            except ValueError:
                raise ValueError('Date must be in YYYY-MM-DD format')
        return v

    @model_validator(mode='after')
    def validate_date_range(self):
        if self.effective_start and self.effective_end:
            start_date = date.fromisoformat(self.effective_start)
            end_date = date.fromisoformat(self.effective_end)
            if end_date < start_date:
                raise ValueError('effective_end must be after effective_start')
        return self


# Array Component Objects
class TraitInclinationBO(BaseModel):
    """HEXACO personality trait inclination"""
    trait_code: str = Field(..., description="HEXACO trait code (e.g., honesty_sincerity, extra_sociability)")
    strength: int = Field(..., ge=0, le=100, description="Strength of this trait in personality: 0=very low, 100=very high")
    awareness: int = Field(..., ge=0, le=100, description="User's self-awareness of this trait: 0=completely unaware, 100=highly aware")
    manifestation: Optional[str] = Field(None, description="How this trait manifests in the user's behavior and actions")
    context: Optional[str] = Field(None, description="The context or situations where this trait is most evident")
    development_notes: Optional[str] = Field(None, description="Notes about how this trait developed or manifests")


class BeliefBO(BaseModel):
    """Core belief and worldview element"""
    belief_statement: str = Field(..., min_length=1, description="Clear, specific statement of what the user believes")
    strength: int = Field(..., ge=0, le=100, description="How strongly the user holds this belief: 0=weak belief, 100=core conviction")
    certainty: int = Field(..., ge=0, le=100, description="How certain the user is about this belief: 0=uncertain, 100=absolutely certain")
    evidence_sources: Optional[List[str]] = Field(None, description="Sources or experiences that support this belief")
    life_impact: Optional[int] = Field(None, ge=0, le=100, description="How much this belief impacts daily decisions")
    emotionality: Optional[int] = Field(None, ge=-100, le=100, description="Emotional charge: -100=very negative, 0=neutral, 100=very positive")
    user_confidence: Optional[int] = Field(None, ge=0, le=100, description="User's personal conviction: 0=very uncertain, 100=absolutely certain")
    stability: Optional[int] = Field(None, ge=0, le=100, description="Resistance to change: 0=very changeable, 100=deeply ingrained")
    user_awareness: Optional[int] = Field(None, ge=0, le=100, description="User's awareness of holding this belief: 0=unconscious, 100=highly conscious")


class AspirationBO(BaseModel):
    """Long-term goal and vision element"""
    title: str = Field(..., min_length=1, description="Concise title of the aspiration")
    description: str = Field(..., min_length=1, description="Detailed description of what the user wants to achieve or become")
    importance: int = Field(..., ge=0, le=100, description="How important this aspiration is: 0=nice to have, 100=life defining")
    time_horizon: Optional[str] = Field(None, description="Expected timeframe (e.g., '5 years', 'lifetime', 'within 2 years')")
    current_progress: Optional[int] = Field(None, ge=0, le=100, description="Current progress toward this aspiration")


class IntentionBO(BaseModel):
    """Short to medium-term goal and plan"""
    title: str = Field(..., min_length=1, description="Clear title of the intention")
    description: str = Field(..., min_length=1, description="Specific description of what the user intends to accomplish")
    target_date: str = Field(..., description="Target completion date (YYYY-MM-DD format)")
    commitment_level: int = Field(..., ge=0, le=100, description="How committed the user is: 0=weak commitment, 100=fully committed")
    success_criteria: Optional[str] = Field(None, description="How the user will know they've achieved this intention")

    @validator('target_date')
    def validate_target_date(cls, v):
        try:
            date.fromisoformat(v)
        except ValueError:
            raise ValueError('target_date must be in YYYY-MM-DD format')
        return v


class InspirationBO(BaseModel):
    """Source of motivation and inspiration"""
    source: str = Field(..., min_length=1, description="Source of inspiration (person, book, experience, philosophy, etc.)")
    description: str = Field(..., min_length=1, description="How this source inspires the user and why it matters")
    strength: int = Field(..., ge=0, le=100, description="How much this source inspires the user: 0=mild inspiration, 100=life changing")
    reference_url: Optional[str] = Field(None, description="URL reference if applicable (books, articles, videos)")


class SkillBO(BaseModel):
    """Personal skill and capability"""
    skill_code: str = Field(..., description="Code referencing a generic skill type (e.g., communication, coding, cooking)")
    description: str = Field(..., min_length=1, description="Personal description of how this skill manifests and is used")
    level: int = Field(..., ge=0, le=100, description="Current skill level: 0=beginner, 50=intermediate, 100=expert")
    user_awareness: Optional[int] = Field(None, ge=0, le=100, description="How aware the user is of their skill level")
    user_enjoyment: Optional[int] = Field(None, ge=0, le=100, description="How much the user enjoys using this skill")
    practice_frequency: Optional[FrequencyEnum] = Field(None, description="How often this skill is practiced")
    acquisition_context: Optional[str] = Field(None, description="How and where this skill was acquired (school, work, self-taught, etc.)")
    

class ResourceBO(BaseModel):
    """Available resource and tool with inventory management capabilities"""
    specific_name: str = Field(..., min_length=1, description="User's specific name for this resource")
    generic_resource: str = Field(..., description="Code referencing a generic resource type (e.g., laptop, car, gym_membership)")
    location_details: Optional[str] = Field(None, description="Specific location within environment (e.g., 'Stored in living room closet')")
    ownership_details: Optional[str] = Field(None, description="How the user owns, rents, borrows, or accesses this resource")
    availability: Optional[str] = Field(None, description="When this resource is available for use (will be converted to boolean)")
    condition: Optional[str] = Field(None, description="Current condition: 'broken', 'poor', 'fair', 'good', or 'excellent'")
    notes: Optional[str] = Field(None, description="Additional relevant information about this resource")

    # New inventory-specific fields that can be inferred from text descriptions
    transportability: Optional[str] = Field(None, description="How easily moved: 'fixed', 'heavy', 'portable', or 'pocket'")

    @field_validator('availability')
    @classmethod
    def validate_availability(cls, v):
        if v is not None:
            # Parse descriptive availability text to boolean
            availability_text = v.lower().strip()

            # Map descriptive text to boolean values
            if any(word in availability_text for word in ['always', 'available', 'yes', 'true', 'accessible']):
                return True
            elif any(word in availability_text for word in ['never', 'unavailable', 'no', 'false', 'broken', 'not available']):
                return False
            else:
                # Default to True if we can't parse it clearly
                return True
        return True  # Default to available

    @field_validator('condition')
    @classmethod
    def validate_condition(cls, v):
        if v is not None:
            # Parse descriptive condition text to standard enum values
            condition_text = v.lower().strip()

            # Map descriptive text to enum values
            condition_mapping = {
                'broken': 'broken',
                'not working': 'broken',
                'damaged': 'broken',
                'poor': 'poor',
                'poor condition': 'poor',
                'barely working': 'poor',
                'fair': 'fair',
                'fair condition': 'fair',
                'okay': 'fair',
                'good': 'good',
                'good condition': 'good',
                'good working condition': 'good',
                'working condition': 'good',
                'working': 'good',
                'excellent': 'excellent',
                'excellent condition': 'excellent',
                'like new': 'excellent',
                'perfect': 'excellent',
                'new': 'excellent'
            }

            # Try to find a match
            for key, value in condition_mapping.items():
                if key in condition_text:
                    return value

            # If no match found, try to infer from keywords
            if any(word in condition_text for word in ['broken', 'damaged', 'not working']):
                return 'broken'
            elif any(word in condition_text for word in ['poor', 'bad', 'terrible']):
                return 'poor'
            elif any(word in condition_text for word in ['fair', 'okay', 'decent']):
                return 'fair'
            elif any(word in condition_text for word in ['good', 'working', 'fine']):
                return 'good'
            elif any(word in condition_text for word in ['excellent', 'perfect', 'new', 'great']):
                return 'excellent'

            # Default to 'good' if we can't parse it
            return 'good'
        return v

    @field_validator('transportability')
    @classmethod
    def validate_transportability(cls, v):
        if v is not None:
            # Parse descriptive transportability text to standard enum values
            transport_text = v.lower().strip()

            # Map descriptive text to enum values
            transport_mapping = {
                'fixed': 'fixed',
                'immovable': 'fixed',
                'built-in': 'fixed',
                'permanent': 'fixed',
                'heavy': 'heavy',
                'requires tools': 'heavy',
                'difficult to move': 'heavy',
                'portable': 'portable',
                'can carry': 'portable',
                'movable': 'portable',
                'transportable': 'portable',
                'pocket': 'pocket',
                'pocket-sized': 'pocket',
                'small': 'pocket',
                'handheld': 'pocket'
            }

            # Try to find a match
            for key, value in transport_mapping.items():
                if key in transport_text:
                    return value

            # If no match found, try to infer from context
            if any(word in transport_text for word in ['fixed', 'built', 'permanent', 'immovable']):
                return 'fixed'
            elif any(word in transport_text for word in ['heavy', 'difficult', 'tools', 'effort']):
                return 'heavy'
            elif any(word in transport_text for word in ['pocket', 'small', 'handheld', 'tiny']):
                return 'pocket'
            else:
                # Default to 'portable' for most items
                return 'portable'
        return v


class LimitationBO(BaseModel):
    """Personal constraint and limitation"""
    limitation_code: str = Field(..., description="Code referencing a generic limitation type (e.g., time_constraints, physical_disability)")
    description: str = Field(..., min_length=1, description="Personal description of how this limitation affects the user")
    severity: int = Field(..., ge=0, le=100, description="How severely this limitation impacts the user: 0=minor inconvenience, 100=major barrier")
    frequency: Optional[FrequencyEnum] = Field(None, description="How often this limitation is encountered")
    coping_strategies: Optional[str] = Field(None, description="How the user currently copes with or works around this limitation")
    is_temporary: Optional[bool] = Field(None, description="Whether this limitation is expected to be temporary or permanent")


class PreferenceBO(BaseModel):
    """Personal preference and pattern"""
    pref_name: str = Field(..., min_length=1, description="Name of the preference or pattern")
    pref_description: str = Field(..., min_length=1, description="Detailed description of the preference and how it manifests")
    pref_strength: int = Field(..., ge=-100, le=100, description="Strength of preference: -100=strong dislike, 0=neutral, 100=strong preference")
    user_awareness: Optional[int] = Field(None, ge=0, le=100, description="How aware the user is of this preference")
    context: Optional[str] = Field(None, description="Context where this preference applies (work, social, personal, etc.)")


# State Objects
class CurrentMoodBO(BaseModel):
    """Current psychological state"""
    energy_level: int = Field(..., ge=0, le=100, description="Current energy level: 0=exhausted, 50=normal, 100=highly energetic")
    stress_level: int = Field(..., ge=0, le=100, description="Current stress level: 0=completely relaxed, 100=extremely stressed")
    optimism: int = Field(..., ge=0, le=100, description="Current optimism about the future: 0=pessimistic, 100=very optimistic")
    social_engagement: int = Field(..., ge=0, le=100, description="Current desire for social interaction: 0=want to be alone, 100=want lots of social contact")
    mood_description: Optional[str] = Field(None, description="Qualitative description of overall current mood and emotional state")


class TrustLevelBO(BaseModel):
    """Trust metrics with guidance systems"""
    value: float = Field(..., ge=0, le=100, description="Overall trust level with guidance systems: 0=no trust, 100=complete trust")
    domain_scores: Optional[Dict[str, float]] = Field(None, description="Domain-specific trust scores")
    progression_notes: Optional[str] = Field(None, description="Notes about how trust has developed over time and current factors")

    @validator('domain_scores')
    def validate_domain_scores(cls, v, values):
        if v is not None:
            overall_value = values.get('value', 100)
            for domain, score in v.items():
                if score > overall_value:
                    raise ValueError(f'Domain score for {domain} ({score}) cannot exceed overall trust value ({overall_value})')
        return v


# Root Object
class UserProfileImportRequestBO(BaseModel):
    """Complete user profile import request"""
    # Required fields
    user_account: UserAccountBO = Field(..., description="User account information")
    profile_name: str = Field(..., min_length=1, max_length=255, description="Display name for the user profile")
    demographics: DemographicsBO = Field(..., description="Demographics information")

    # Optional fields
    is_real: Optional[bool] = Field(True, description="True for real users, False for test/archetypal profiles")
    environments: Optional[List[UserEnvironmentBO]] = Field(None, description="User environments (1-5 environments recommended)")
    traits: Optional[List[TraitInclinationBO]] = Field(None, description="HEXACO personality trait inclinations")
    beliefs: Optional[List[BeliefBO]] = Field(None, description="Core beliefs and worldview elements")
    aspirations: Optional[List[AspirationBO]] = Field(None, description="Long-term goals and vision elements")
    intentions: Optional[List[IntentionBO]] = Field(None, description="Short to medium-term goals and plans")
    inspirations: Optional[List[InspirationBO]] = Field(None, description="Sources of motivation and inspiration")
    skills: Optional[List[SkillBO]] = Field(None, description="Personal skills and capabilities")
    resources: Optional[List[ResourceBO]] = Field(None, description="Available resources and tools")
    limitations: Optional[List[LimitationBO]] = Field(None, description="Personal constraints and limitations")
    preferences: Optional[List[PreferenceBO]] = Field(None, description="Personal preferences and patterns")
    current_mood: Optional[CurrentMoodBO] = Field(None, description="Current psychological state")
    trust_level: Optional[TrustLevelBO] = Field(None, description="Trust metrics with guidance systems")

    @model_validator(mode='after')
    def validate_profile_consistency(self):
        """Validate cross-component consistency"""
        errors = []

        # Validate environments consistency
        if self.environments:
            current_envs = [env for env in self.environments if env.is_current]
            if len(current_envs) == 0:
                errors.append("At least one environment must be marked as current (is_current: true)")
            elif len(current_envs) > 1:
                errors.append("Only one environment should be marked as current (is_current: true)")

            if len(self.environments) > 5:
                errors.append("Too many environments specified (maximum 5 recommended)")

        # Validate trait count recommendations
        if self.traits and len(self.traits) > 24:
            errors.append("Too many traits specified (maximum 24 recommended)")

        # Validate belief count recommendations
        if self.beliefs and len(self.beliefs) > 8:
            errors.append("Too many beliefs specified (maximum 8 recommended)")

        # Validate skill awareness vs level consistency
        if self.skills:
            for i, skill in enumerate(self.skills):
                if skill.user_awareness and skill.user_awareness > skill.level + 20:
                    errors.append(f"Skill {i+1}: user_awareness ({skill.user_awareness}) seems unusually high compared to skill level ({skill.level})")

        if errors:
            raise ValueError('; '.join(errors))

        return self

    class Config:
        use_enum_values = True
        validate_assignment = True
