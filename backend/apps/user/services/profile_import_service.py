import json
import uuid
import logging
import time
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from django.db import transaction, IntegrityError
from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password
from django.core.exceptions import ValidationError
from django.conf import settings
from django.utils import timezone
import jsonschema
from pydantic import ValidationError as PydanticValidationError

from apps.user.models import (
    UserProfile, Demographics, UserEnvironment, UserTraitInclination,
    Belief, BeliefEvidence, BeliefInfluence, Aspiration, Intention,
    Inspiration, GoalInspiration, Skill, UserResource, UserLimitation,
    Preference, CurrentMood, TrustLevel, GenericTrait, GenericSkill,
    GenericResource, GenericUserLimitation, GenericEnvironment,
    UserEnvironmentPhysicalProperties, UserEnvironmentSocialContext,
    UserEnvironmentActivitySupport, UserEnvironmentPsychologicalQualities,
    Inventory
)
from .user_profile_business_objects import UserProfileImportRequestBO
from .environment_property_service import EnvironmentPropertyService

logger = logging.getLogger(__name__)


class ProfileImportError(Exception):
    """Custom exception for profile import errors with structured error responses"""

    def __init__(self, message: str, error_type: str = 'profile_import_error',
                 field_errors: Dict[str, List[str]] = None, context: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.field_errors = field_errors or {}
        self.context = context or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for JSON serialization"""
        return {
            'success': False,
            'error': self.message,
            'error_type': self.error_type,
            'field_errors': self.field_errors,
            'context': self.context
        }


class SchemaValidationError(ProfileImportError):
    """Exception for JSON schema validation errors"""

    def __init__(self, message: str, schema_errors: List[str] = None,
                 field_path: str = None, invalid_value: Any = None):
        super().__init__(
            message=message,
            error_type='schema_validation_error',
            context={
                'schema_errors': schema_errors or [],
                'field_path': field_path,
                'invalid_value': invalid_value
            }
        )
        self.schema_errors = schema_errors or []
        self.field_path = field_path
        self.invalid_value = invalid_value


class ReferenceValidationError(ProfileImportError):
    """Exception for reference code validation errors"""

    def __init__(self, message: str, invalid_references: Dict[str, List[str]] = None):
        super().__init__(
            message=message,
            error_type='reference_validation_error',
            context={
                'invalid_references': invalid_references or {}
            }
        )
        self.invalid_references = invalid_references or {}


class ConsistencyValidationError(ProfileImportError):
    """Exception for cross-field validation errors"""

    def __init__(self, message: str, consistency_errors: List[str] = None,
                 conflicting_fields: List[str] = None):
        super().__init__(
            message=message,
            error_type='consistency_validation_error',
            context={
                'consistency_errors': consistency_errors or [],
                'conflicting_fields': conflicting_fields or []
            }
        )
        self.consistency_errors = consistency_errors or []
        self.conflicting_fields = conflicting_fields or []


class ProfileImportService:
    """
    Service for importing complete user profiles from structured data.

    Handles the complex process of creating user accounts, profiles, and all
    related data in a transactional manner with proper error handling and
    validation. Includes comprehensive import history tracking.
    """

    def __init__(self, initiated_by: User = None):
        self.warnings = []
        self.created_records = 0
        self.updated_records = 0
        self._schema_cache = None
        self.initiated_by = initiated_by
        self.import_history = None
        self._start_time = None
        self._validation_start_time = None
        self._database_start_time = None
        # Performance optimization caches
        self._generic_entities_cache = {
            'traits': None,
            'skills': None,
            'resources': None,
            'limitations': None,
            'environments': None
        }

    def _load_json_schema(self) -> Dict[str, Any]:
        """Load and cache the JSON schema for validation"""
        if self._schema_cache is None:
            schema_path = Path(settings.BASE_DIR) / 'schemas' / 'user_profile.schema.json'
            try:
                with open(schema_path, 'r') as f:
                    self._schema_cache = json.load(f)
            except FileNotFoundError:
                raise SchemaValidationError(f"Schema file not found at {schema_path}")
            except json.JSONDecodeError as e:
                raise SchemaValidationError(f"Invalid JSON in schema file: {e}")
        return self._schema_cache

    def validate_json_schema(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate data against JSON schema

        Args:
            data: The data to validate

        Returns:
            Tuple of (is_valid, error_messages)
        """
        try:
            schema = self._load_json_schema()
            jsonschema.validate(data, schema)
            return True, []
        except jsonschema.ValidationError as e:
            field_path = '.'.join(str(p) for p in e.absolute_path)
            error_msg = f"Schema validation error at {field_path}: {e.message}"
            return False, [error_msg]
        except jsonschema.SchemaError as e:
            raise SchemaValidationError(
                message=f"Invalid schema: {e}",
                schema_errors=[str(e)]
            )

    def parse_business_objects(self, data: Dict[str, Any]) -> UserProfileImportRequestBO:
        """
        Parse validated data into business objects

        Args:
            data: The validated data

        Returns:
            UserProfileImportRequestBO instance

        Raises:
            ConsistencyValidationError: If business object validation fails
        """
        try:
            return UserProfileImportRequestBO(**data)
        except PydanticValidationError as e:
            error_messages = []
            field_errors = {}
            conflicting_fields = []

            for error in e.errors():
                field_path = '.'.join(str(p) for p in error['loc'])
                error_msg = error['msg']
                error_messages.append(f"{field_path}: {error_msg}")

                if field_path not in field_errors:
                    field_errors[field_path] = []
                field_errors[field_path].append(error_msg)
                conflicting_fields.append(field_path)

            raise ConsistencyValidationError(
                message=f"Business object validation failed: {'; '.join(error_messages)}",
                consistency_errors=error_messages,
                conflicting_fields=list(set(conflicting_fields))
            )

    def validate_reference_codes(self, bo: UserProfileImportRequestBO) -> List[str]:
        """
        Validate all reference codes against database

        Args:
            bo: The business object to validate

        Returns:
            List of validation error messages
        """
        errors = []

        # Validate trait codes
        if bo.traits:
            valid_trait_codes = set(GenericTrait.objects.values_list('code', flat=True))
            invalid_traits = [trait.trait_code for trait in bo.traits if trait.trait_code not in valid_trait_codes]
            if invalid_traits:
                errors.append(f"Invalid trait codes: {', '.join(invalid_traits)}")

        # Validate skill codes
        if bo.skills:
            valid_skill_codes = set(GenericSkill.objects.values_list('code', flat=True))
            invalid_skills = [skill.skill_code for skill in bo.skills if skill.skill_code not in valid_skill_codes]
            if invalid_skills:
                errors.append(f"Invalid skill codes: {', '.join(invalid_skills)}")

        # Validate resource codes
        if bo.resources:
            valid_generic_resources = set(GenericResource.objects.values_list('code', flat=True))
            invalid_resources = [resource.generic_resource for resource in bo.resources if resource.generic_resource not in valid_generic_resources]
            if invalid_resources:
                errors.append(f"Invalid resource codes: {', '.join(invalid_resources)}")

        # Validate limitation codes
        if bo.limitations:
            valid_limitation_codes = set(GenericUserLimitation.objects.values_list('code', flat=True))
            invalid_limitations = [limitation.limitation_code for limitation in bo.limitations if limitation.limitation_code not in valid_limitation_codes]
            if invalid_limitations:
                errors.append(f"Invalid limitation codes: {', '.join(invalid_limitations)}")

        # Validate environment codes
        if bo.environments:
            for i, env in enumerate(bo.environments):
                if env.generic_environment_code:
                    if not GenericEnvironment.objects.filter(code=env.generic_environment_code).exists():
                        errors.append(f"Invalid environment code in environment {i+1}: {env.generic_environment_code}")

        return errors

    def _preload_generic_entities(self):
        """Preload all generic entities for performance optimization"""
        if self._generic_entities_cache['traits'] is None:
            self._generic_entities_cache['traits'] = {
                trait.code: trait for trait in GenericTrait.objects.all()
            }

        if self._generic_entities_cache['skills'] is None:
            self._generic_entities_cache['skills'] = {
                skill.code: skill for skill in GenericSkill.objects.all()
            }

        if self._generic_entities_cache['resources'] is None:
            self._generic_entities_cache['resources'] = {
                resource.code: resource for resource in GenericResource.objects.all()
            }

        if self._generic_entities_cache['limitations'] is None:
            self._generic_entities_cache['limitations'] = {
                limitation.code: limitation for limitation in GenericUserLimitation.objects.all()
            }

        if self._generic_entities_cache['environments'] is None:
            self._generic_entities_cache['environments'] = {
                env.code: env for env in GenericEnvironment.objects.all()
            }

    def _get_cached_generic_entity(self, entity_type: str, code: str):
        """Get cached generic entity by type and code"""
        self._preload_generic_entities()
        return self._generic_entities_cache.get(entity_type, {}).get(code)

    def import_profile(self, profile_data: Dict[str, Any], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Import a complete user profile from structured data.

        Args:
            profile_data: Complete profile data matching the schema
            options: Import options (overwrite, validate, backup)

        Returns:
            Dict with import results including success status, profile info, and stats

        Raises:
            ProfileImportError: If import fails due to validation or data errors
        """
        if options is None:
            options = {}

        self.warnings = []
        self.created_records = 0
        self.updated_records = 0
        self._start_time = time.time()

        # Create import history record
        self._create_import_history(profile_data, options)
        
        try:
            # Phase 1: Validation and Parsing
            self._validation_start_time = time.time()
            self._update_import_status('in_progress')

            # 1. Validate JSON schema
            is_valid, schema_errors = self.validate_json_schema(profile_data)
            if not is_valid:
                error_msg = f"Schema validation failed: {'; '.join(schema_errors)}"
                self._add_import_error(error_msg, 'schema_validation')
                raise SchemaValidationError(error_msg)

            # 2. Parse to business objects
            business_object = self.parse_business_objects(profile_data)

            # 3. Validate reference codes
            reference_errors = self.validate_reference_codes(business_object)
            if reference_errors:
                # Parse reference errors into structured format
                invalid_refs = {}
                for error in reference_errors:
                    if 'trait codes:' in error:
                        invalid_refs['traits'] = error.split(': ')[1].split(', ')
                    elif 'skill codes:' in error:
                        invalid_refs['skills'] = error.split(': ')[1].split(', ')
                    elif 'resource codes:' in error:
                        invalid_refs['resources'] = error.split(': ')[1].split(', ')
                    elif 'limitation codes:' in error:
                        invalid_refs['limitations'] = error.split(': ')[1].split(', ')
                    elif 'environment code:' in error:
                        invalid_refs['environment'] = [error.split(': ')[1]]

                error_msg = f"Reference validation failed: {'; '.join(reference_errors)}"
                self._add_import_error(error_msg, 'reference_validation', {'invalid_references': invalid_refs})
                raise ReferenceValidationError(
                    message=error_msg,
                    invalid_references=invalid_refs
                )

            # Record validation completion time
            validation_time = time.time() - self._validation_start_time
            self._update_validation_time(validation_time)

            # Phase 2: Database Transaction
            self._database_start_time = time.time()
            with transaction.atomic():
                # 1. Create or get user account
                user = self._create_or_get_user(business_object.user_account.dict(), options)

                # 2. Create or get user profile
                profile_dict = {
                    'profile_name': business_object.profile_name,
                    'is_real': business_object.is_real
                }
                user_profile = self._create_or_get_user_profile(user, profile_dict, options)
                
                # 3. Import demographics
                self._import_demographics(user_profile, business_object.demographics.dict())

                # 4. Import environments
                if business_object.environments:
                    for environment in business_object.environments:
                        self._import_environment(user_profile, environment)

                # 5. Import personality traits
                if business_object.traits:
                    self._import_traits(user_profile, [trait.dict() for trait in business_object.traits])

                # 6. Import beliefs
                if business_object.beliefs:
                    self._import_beliefs(user_profile, [belief.dict() for belief in business_object.beliefs])

                # 7. Import goals (aspirations and intentions)
                if business_object.aspirations:
                    self._import_aspirations(user_profile, [aspiration.dict() for aspiration in business_object.aspirations])

                if business_object.intentions:
                    self._import_intentions(user_profile, [intention.dict() for intention in business_object.intentions])

                # 8. Import inspirations
                if business_object.inspirations:
                    self._import_inspirations(user_profile, [inspiration.dict() for inspiration in business_object.inspirations])

                # 9. Import skills
                if business_object.skills:
                    self._import_skills(user_profile, [skill.dict() for skill in business_object.skills])

                # 10. Import resources
                if business_object.resources:
                    self._import_resources(user_profile, [resource.dict() for resource in business_object.resources])
                
                # 11. Import limitations
                if business_object.limitations:
                    self._import_limitations(user_profile, [limitation.dict() for limitation in business_object.limitations])

                # 12. Import preferences
                if business_object.preferences:
                    self._import_preferences(user_profile, [preference.dict() for preference in business_object.preferences])

                # 13. Import current mood
                if business_object.current_mood:
                    self._import_current_mood(user_profile, business_object.current_mood.dict())

                # 14. Import trust level
                if business_object.trust_level:
                    self._import_trust_level(user_profile, business_object.trust_level.dict())
                
                # Record database operation time
                database_time = time.time() - self._database_start_time
                self._update_database_time(database_time)

                logger.info(f"Successfully imported profile: {user_profile.profile_name}")

                # Mark import as successful
                self._complete_import_success(user_profile)

                return {
                    'success': True,
                    'user_id': user.id,
                    'profile_id': user_profile.id,
                    'profile_name': user_profile.profile_name,
                    'created_records': self.created_records,
                    'updated_records': self.updated_records,
                    'warnings': self.warnings,
                    'import_history_id': str(self.import_history.id) if self.import_history else None
                }

        except Exception as e:
            logger.error(f"Profile import failed: {str(e)}")
            self._complete_import_failure(str(e))
            raise ProfileImportError(f"Import failed: {str(e)}")
    
    def _create_or_get_user(self, user_data: Dict[str, Any], options: Dict[str, Any]) -> User:
        """Create or retrieve user account"""
        username = user_data['username']
        email = user_data['email']
        
        # Check if user exists
        existing_user = User.objects.filter(username=username).first()
        if existing_user:
            if not options.get('overwriteExisting', False):
                raise ProfileImportError(f"User '{username}' already exists. Use overwriteExisting option to update.")
            
            # Update existing user (only update fields that are provided and not None)
            for field, value in user_data.items():
                if field == 'password':
                    # Only update password if a value is provided
                    if value:
                        existing_user.password = make_password(value)
                    # Skip password field if no value provided (don't set to None)
                elif hasattr(existing_user, field) and value is not None:
                    # Only update field if value is provided (not None)
                    setattr(existing_user, field, value)
            
            existing_user.save()
            self.updated_records += 1
            return existing_user
        else:
            # Create new user
            user_create_data = user_data.copy()

            # Ensure required fields have default values
            if 'first_name' not in user_create_data or not user_create_data['first_name']:
                user_create_data['first_name'] = user_create_data.get('username', 'User')

            if 'last_name' not in user_create_data or not user_create_data['last_name']:
                user_create_data['last_name'] = ''

            if 'password' in user_create_data and user_create_data['password']:
                user_create_data['password'] = make_password(user_create_data['password'])
            else:
                # Generate a random password if none provided
                import secrets
                import string
                alphabet = string.ascii_letters + string.digits
                random_password = ''.join(secrets.choice(alphabet) for i in range(12))
                user_create_data['password'] = make_password(random_password)
                self.warnings.append("No password provided, generated random password")

            user = User.objects.create(**user_create_data)
            self.created_records += 1
            return user
    
    def _create_or_get_user_profile(self, user: User, profile_data: Dict[str, Any], options: Dict[str, Any]) -> UserProfile:
        """Create or retrieve user profile"""
        profile_name = profile_data['profile_name']
        is_real = profile_data.get('is_real', True)
        
        # Check if profile exists
        existing_profile = UserProfile.objects.filter(user=user).first()
        if existing_profile:
            # Update existing profile
            existing_profile.profile_name = profile_name
            existing_profile.is_real = is_real
            existing_profile.save()
            self.updated_records += 1
            return existing_profile
        else:
            # Create new profile
            profile = UserProfile.objects.create(
                user=user,
                profile_name=profile_name,
                is_real=is_real
            )
            self.created_records += 1
            return profile
    
    def _import_demographics(self, user_profile: UserProfile, demographics_data: Dict[str, Any]):
        """Import demographics data"""
        demographics, created = Demographics.objects.update_or_create(
            user_profile=user_profile,
            defaults=demographics_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1

    def _import_environment(self, user_profile: UserProfile, environment_bo):
        """Import single user environment from business object"""
        # Convert business object to dict for database operations
        env_data = environment_bo.dict()

        # Extract nested data
        physical_props = env_data.pop('physical_properties', None)
        social_context = env_data.pop('social_context', None)
        activity_support = env_data.pop('activity_support', None)
        psychological_qualities = env_data.pop('psychological_qualities', None)

        # Handle generic environment reference
        generic_environment = None
        if env_data.get('generic_environment_code'):
            try:
                generic_environment = GenericEnvironment.objects.get(code=env_data['generic_environment_code'])
            except GenericEnvironment.DoesNotExist:
                self.warnings.append(f"Generic environment '{env_data['generic_environment_code']}' not found")

        # Remove generic_environment_code from env_data since it's not a model field
        env_data.pop('generic_environment_code', None)

        # Ensure required fields are set
        from datetime import date
        if 'effective_start' not in env_data or not env_data['effective_start']:
            env_data['effective_start'] = date.today()

        # Create or update environment
        environment, created = UserEnvironment.objects.update_or_create(
            user_profile=user_profile,
            environment_name=env_data['environment_name'],
            defaults={
                **env_data,
                'generic_environment': generic_environment
            }
        )

        if created:
            self.created_records += 1
        else:
            self.updated_records += 1

        # Use environment property service to create nested properties
        property_service = EnvironmentPropertyService()
        property_results = property_service.create_all_properties(
            user_environment=environment,
            physical_properties=physical_props,
            social_context=social_context,
            activity_support=activity_support,
            psychological_qualities=psychological_qualities
        )

        # Update our counters with the property service results
        self.created_records += property_results['created_count']
        self.updated_records += property_results['updated_count']

        # Set as current environment if specified
        if env_data.get('is_current', False):
            user_profile.current_environment = environment
            user_profile.save()
            self.updated_records += 1

    def _import_environments(self, user_profile: UserProfile, environments_data: List[Dict[str, Any]]):
        """Import user environments"""
        current_environment = None
        
        for env_data in environments_data:
            # Extract nested data
            physical_props = env_data.pop('physical_properties', {})
            social_context = env_data.pop('social_context', {})
            activity_support = env_data.pop('activity_support', {})
            psychological_qualities = env_data.pop('psychological_qualities', {})
            
            # Get or create generic environment if code provided
            generic_env = None
            if env_data.get('generic_environment_code'):
                try:
                    generic_env = GenericEnvironment.objects.get(code=env_data['generic_environment_code'])
                except GenericEnvironment.DoesNotExist:
                    self.warnings.append(f"Generic environment '{env_data['generic_environment_code']}' not found")
            
            # Create or update environment
            environment, created = UserEnvironment.objects.update_or_create(
                user_profile=user_profile,
                environment_name=env_data['environment_name'],
                defaults={
                    **env_data,
                    'generic_environment': generic_env
                }
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
            
            # Create or update physical properties
            if physical_props:
                props, props_created = UserEnvironmentPhysicalProperties.objects.update_or_create(
                    user_environment=environment,
                    defaults=physical_props
                )
                if props_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update social context
            if social_context:
                context, context_created = UserEnvironmentSocialContext.objects.update_or_create(
                    user_environment=environment,
                    defaults=social_context
                )
                if context_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update activity support
            if activity_support:
                support, support_created = UserEnvironmentActivitySupport.objects.update_or_create(
                    user_environment=environment,
                    defaults=activity_support
                )
                if support_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Create or update psychological qualities
            if psychological_qualities:
                qualities, qualities_created = UserEnvironmentPsychologicalQualities.objects.update_or_create(
                    user_environment=environment,
                    defaults=psychological_qualities
                )
                if qualities_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
            
            # Track current environment
            if env_data.get('is_current', False):
                current_environment = environment
        
        # Update user profile's current environment
        if current_environment:
            user_profile.current_environment = current_environment
            user_profile.save()
    
    def _import_traits(self, user_profile: UserProfile, traits_data: List[Dict[str, Any]]):
        """Import personality traits with performance optimization"""
        # Preload generic entities for performance
        self._preload_generic_entities()

        # Prepare bulk operations
        traits_to_create = []
        traits_to_update = []

        # Get existing user traits for this profile
        existing_traits = {
            trait.generic_trait.code: trait
            for trait in UserTraitInclination.objects.filter(user_profile=user_profile)
                                                    .select_related('generic_trait')
        }

        for trait_data in traits_data:
            trait_code = trait_data['trait_code']

            # Get cached generic trait
            generic_trait = self._get_cached_generic_entity('traits', trait_code)

            if not generic_trait:
                self.warnings.append(f"Generic trait '{trait_code}' not found")
                continue

            # Prepare trait inclination data (only valid model fields)
            inclination_data = {
                'strength': trait_data['strength'],
                'awareness': trait_data['awareness'],
                'manifestation': trait_data.get('manifestation'),
                'context': trait_data.get('context')
            }

            if trait_code in existing_traits:
                # Update existing trait
                existing_trait = existing_traits[trait_code]
                for field, value in inclination_data.items():
                    setattr(existing_trait, field, value)
                traits_to_update.append(existing_trait)
            else:
                # Create new trait
                traits_to_create.append(UserTraitInclination(
                    user_profile=user_profile,
                    generic_trait=generic_trait,
                    **inclination_data
                ))

        # Bulk operations
        if traits_to_create:
            UserTraitInclination.objects.bulk_create(traits_to_create)
            self.created_records += len(traits_to_create)

        if traits_to_update:
            UserTraitInclination.objects.bulk_update(
                traits_to_update,
                ['strength', 'awareness', 'manifestation', 'context']
            )
            self.updated_records += len(traits_to_update)
    
    def _import_beliefs(self, user_profile: UserProfile, beliefs_data: List[Dict[str, Any]]):
        """Import beliefs and evidence"""
        for belief_data in beliefs_data:
            # Map business object fields to model fields
            content = belief_data.get('belief_statement', belief_data.get('content', ''))
            if not content:
                self.warnings.append("Belief missing content/belief_statement, skipping")
                continue

            # Map business object fields to model fields
            model_data = {
                'content': content,
                'last_updated': date.today(),
                'strength': belief_data.get('strength', 50),
                'certainty': belief_data.get('certainty', 50),
                'life_impact': belief_data.get('life_impact', 50),
                'user_confidence': belief_data.get('user_confidence', belief_data.get('strength', 50)),
                'system_confidence': belief_data.get('certainty', 50),
                'emotionality': belief_data.get('emotionality', 0),  # Default neutral
                'stability': belief_data.get('stability', belief_data.get('strength', 50)),
                'user_awareness': belief_data.get('user_awareness', 100),  # Assume high awareness for explicitly stated beliefs
            }

            # Handle evidence sources
            evidence_sources = belief_data.get('evidence_sources', [])

            belief, created = Belief.objects.update_or_create(
                user_profile=user_profile,
                content=content,
                defaults=model_data
            )

            if created:
                self.created_records += 1
            else:
                self.updated_records += 1

            # Import evidence sources as BeliefEvidence
            for evidence_source in evidence_sources:
                evidence_obj, evidence_created = BeliefEvidence.objects.update_or_create(
                    belief=belief,
                    evidence_type='EXPERIENCE',  # Default type
                    description=evidence_source,
                    defaults={
                        'credibility_score': 70,  # Default credibility
                        'source': 'User provided'
                    }
                )

                if evidence_created:
                    self.created_records += 1
                else:
                    self.updated_records += 1
    
    def _import_aspirations(self, user_profile: UserProfile, aspirations_data: List[Dict[str, Any]]):
        """Import aspirations with proper field mapping"""
        for aspiration_data in aspirations_data:
            # Map business object fields to model fields
            mapped_data = self._map_aspiration_fields(aspiration_data)

            aspiration, created = Aspiration.objects.update_or_create(
                user_profile=user_profile,
                title=aspiration_data['title'],
                defaults=mapped_data
            )

            if created:
                self.created_records += 1
            else:
                self.updated_records += 1

    def _map_aspiration_fields(self, aspiration_data: Dict[str, Any]) -> Dict[str, Any]:
        """Map business object fields to Aspiration model fields"""
        mapped_data = aspiration_data.copy()

        # Map business object fields to model fields
        field_mapping = {
            'importance': 'importance_according_user',  # Map importance to importance_according_user
            'time_horizon': 'horizon',  # Map time_horizon to horizon
            # current_progress doesn't exist in model, so we'll remove it
        }

        for bo_field, model_field in field_mapping.items():
            if bo_field in mapped_data:
                mapped_data[model_field] = mapped_data.pop(bo_field)

        # Remove fields that don't exist in the model
        fields_to_remove = ['current_progress']
        for field in fields_to_remove:
            mapped_data.pop(field, None)

        # Set default values for required fields if missing
        if 'importance_according_user' not in mapped_data:
            mapped_data['importance_according_user'] = 50  # Default importance

        if 'importance_according_system' not in mapped_data:
            mapped_data['importance_according_system'] = mapped_data.get('importance_according_user', 50)

        if 'strength' not in mapped_data:
            mapped_data['strength'] = mapped_data.get('importance_according_user', 50)

        # Set default values for Aspiration-specific fields
        if 'domain' not in mapped_data:
            mapped_data['domain'] = 'General'

        if 'horizon' not in mapped_data:
            mapped_data['horizon'] = 'Medium-term'

        if 'level_of_ambition' not in mapped_data:
            mapped_data['level_of_ambition'] = 'Medium'

        return mapped_data
    
    def _import_intentions(self, user_profile: UserProfile, intentions_data: List[Dict[str, Any]]):
        """Import intentions with proper field mapping"""
        for intention_data in intentions_data:
            # Map business object fields to model fields
            mapped_data = self._map_intention_fields(intention_data)

            intention, created = Intention.objects.update_or_create(
                user_profile=user_profile,
                title=intention_data['title'],
                defaults=mapped_data
            )

            if created:
                self.created_records += 1
            else:
                self.updated_records += 1

    def _map_intention_fields(self, intention_data: Dict[str, Any]) -> Dict[str, Any]:
        """Map business object fields to Intention model fields"""
        mapped_data = intention_data.copy()

        # Map business object fields to model fields
        field_mapping = {
            'commitment_level': 'strength',  # Map commitment_level to strength (UserGoal field)
            'target_date': 'due_date',  # Map target_date to due_date (Intention field)
            # success_criteria maps directly to success_criteria (UserGoal field) - no mapping needed
        }

        for bo_field, model_field in field_mapping.items():
            if bo_field in mapped_data:
                mapped_data[model_field] = mapped_data.pop(bo_field)

        # Set default values for required fields if missing
        if 'importance_according_user' not in mapped_data:
            mapped_data['importance_according_user'] = mapped_data.get('strength', 50)

        if 'importance_according_system' not in mapped_data:
            mapped_data['importance_according_system'] = mapped_data.get('strength', 50)

        if 'strength' not in mapped_data:
            mapped_data['strength'] = 50  # Default commitment level

        # Set default values for Intention-specific fields
        if 'start_date' not in mapped_data:
            # If no start_date provided, use today
            from datetime import date
            mapped_data['start_date'] = date.today()

        if 'is_completed' not in mapped_data:
            mapped_data['is_completed'] = False

        if 'progress_notes' not in mapped_data:
            mapped_data['progress_notes'] = ''

        # Set default values for TemporalRecord fields
        if 'effective_start' not in mapped_data:
            mapped_data['effective_start'] = mapped_data.get('start_date', date.today())

        if 'duration_estimate' not in mapped_data:
            mapped_data['duration_estimate'] = 'To be determined'

        if 'effective_end' not in mapped_data:
            mapped_data['effective_end'] = mapped_data.get('due_date')

        return mapped_data
    
    def _import_inspirations(self, user_profile: UserProfile, inspirations_data: List[Dict[str, Any]]):
        """Import inspirations"""
        for inspiration_data in inspirations_data:
            inspiration, created = Inspiration.objects.update_or_create(
                user_profile=user_profile,
                source=inspiration_data['source'],
                defaults=inspiration_data
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_skills(self, user_profile: UserProfile, skills_data: List[Dict[str, Any]]):
        """Import skills with performance optimization"""
        # Preload generic entities for performance
        self._preload_generic_entities()

        # Prepare bulk operations
        skills_to_create = []
        skills_to_update = []

        # Get existing user skills for this profile
        existing_skills = {
            skill.generic_skill.code: skill
            for skill in Skill.objects.filter(user_profile=user_profile)
                                     .select_related('generic_skill')
        }

        for skill_data in skills_data:
            skill_code = skill_data['skill_code']

            # Get cached generic skill
            generic_skill = self._get_cached_generic_entity('skills', skill_code)

            if not generic_skill:
                # Create missing generic skill
                generic_skill = GenericSkill.objects.create(
                    code=skill_code,
                    description=skill_data.get('description', skill_code)
                )
                self._generic_entities_cache['skills'][skill_code] = generic_skill
                self.created_records += 1
                self.warnings.append(f"Created generic skill: {skill_code}")

            # Prepare skill data (only include fields that exist in the Skill model)
            # Handle None values properly to avoid database constraint violations
            skill_model_data = {
                'level': skill_data['level'],
                'user_awareness': skill_data.get('user_awareness') or 50,
                'user_enjoyment': skill_data.get('user_enjoyment') or 50,
                'practice_frequency': skill_data.get('practice_frequency') or 'rarely',
                'description': skill_data.get('description') or '',
                'note': skill_data.get('note') or ''
            }

            # Map acquisition_context to training_details (the actual field in the Skill model)
            if 'acquisition_context' in skill_data:
                skill_model_data['training_details'] = skill_data.get('acquisition_context', '')

            if skill_code in existing_skills:
                # Update existing skill
                existing_skill = existing_skills[skill_code]
                for field, value in skill_model_data.items():
                    setattr(existing_skill, field, value)
                skills_to_update.append(existing_skill)
            else:
                # Create new skill
                skills_to_create.append(Skill(
                    user_profile=user_profile,
                    generic_skill=generic_skill,
                    **skill_model_data
                ))

        # Bulk operations
        if skills_to_create:
            Skill.objects.bulk_create(skills_to_create)
            self.created_records += len(skills_to_create)

        if skills_to_update:
            Skill.objects.bulk_update(
                skills_to_update,
                ['level', 'user_awareness', 'user_enjoyment', 'practice_frequency',
                 'training_details', 'description', 'note']
            )
            self.updated_records += len(skills_to_update)
    
    def _import_resources(self, user_profile: UserProfile, resources_data: List[Dict[str, Any]]):
        """Import resources and create inventories"""
        # Get current environment for resource association
        current_env = user_profile.current_environment
        if not current_env:
            self.warnings.append("No current environment found for resources")
            return

        # Create or get a main inventory for this environment
        main_inventory, inventory_created = Inventory.objects.get_or_create(
            user_profile=user_profile,
            user_environment=current_env,
            name=f"{current_env.environment_name} Inventory",
            defaults={
                'notes': f"Main inventory for {current_env.environment_name}",
            }
        )

        if inventory_created:
            self.created_records += 1
            self.warnings.append(f"Created inventory: {main_inventory.name}")

        # Group resources by category for better organization
        resource_categories = {}
        for resource_data in resources_data:
            category = resource_data.get('category', 'General')
            if category not in resource_categories:
                resource_categories[category] = []
            resource_categories[category].append(resource_data)

        # Create category-specific inventories if there are many resources
        total_resources = len(resources_data)
        if total_resources > 10:  # Create category inventories for large collections
            for category, category_resources in resource_categories.items():
                if len(category_resources) > 3:  # Only create separate inventory if category has multiple items
                    category_inventory, cat_inv_created = Inventory.objects.get_or_create(
                        user_profile=user_profile,
                        user_environment=current_env,
                        name=f"{category} - {current_env.environment_name}",
                        defaults={
                            'notes': f"{category} resources in {current_env.environment_name}",
                        }
                    )

                    if cat_inv_created:
                        self.created_records += 1
                        self.warnings.append(f"Created category inventory: {category_inventory.name}")

                    # Process resources for this category
                    for resource_data in category_resources:
                        self._create_user_resource(resource_data, current_env, category_inventory)
                else:
                    # Add to main inventory if category is small
                    for resource_data in category_resources:
                        self._create_user_resource(resource_data, current_env, main_inventory)
        else:
            # Add all resources to main inventory if total is small
            for resource_data in resources_data:
                self._create_user_resource(resource_data, current_env, main_inventory)

    def _create_user_resource(self, resource_data: Dict[str, Any], current_env, inventory: Inventory):
        """Create a user resource and add it to the specified inventory"""
        # Get or create generic resource
        generic_resource, resource_created = GenericResource.objects.get_or_create(
            code=resource_data['generic_resource'],
            defaults={
                'resource_type': 'General',
                'description': resource_data.get('specific_name', resource_data['generic_resource']),
                'op_cost': 0,
                'acq_cost': 0
            }
        )

        if resource_created:
            self.created_records += 1
            self.warnings.append(f"Created generic resource: {resource_data['generic_resource']}")

        # Create or update user resource with proper field mapping
        # Parse availability to boolean
        availability = resource_data.get('availability', True)
        if isinstance(availability, str):
            availability_text = availability.lower().strip()
            if any(word in availability_text for word in ['always', 'available', 'yes', 'true', 'accessible']):
                availability = True
            elif any(word in availability_text for word in ['never', 'unavailable', 'no', 'false', 'broken', 'not available']):
                availability = False
            else:
                availability = True  # Default to available

        # Parse condition to enum value
        condition = resource_data.get('condition', 'good')
        if isinstance(condition, str):
            condition_text = condition.lower().strip()
            condition_mapping = {
                'broken': 1, 'not working': 1, 'damaged': 1,
                'poor': 2, 'poor condition': 2, 'barely working': 2,
                'fair': 3, 'fair condition': 3, 'okay': 3,
                'good': 4, 'good condition': 4, 'good working condition': 4, 'working condition': 4, 'working': 4,
                'excellent': 5, 'excellent condition': 5, 'like new': 5, 'perfect': 5, 'new': 5
            }

            # Try to find a match
            condition_value = 4  # Default to 'good'
            for key, value in condition_mapping.items():
                if key in condition_text:
                    condition_value = value
                    break
            condition = condition_value

        # Parse transportability to enum value
        transportability = resource_data.get('transportability')
        if transportability is None:
            # Default to 'portable' if not specified
            transportability = 3
        elif isinstance(transportability, str):
            transport_text = transportability.lower().strip()
            transport_mapping = {
                'fixed': 1, 'immovable': 1, 'built-in': 1, 'permanent': 1,
                'heavy': 2, 'requires tools': 2, 'difficult to move': 2,
                'portable': 3, 'can carry': 3, 'movable': 3, 'transportable': 3,
                'pocket': 4, 'pocket-sized': 4, 'small': 4, 'handheld': 4
            }

            # Try to find a match
            transport_value = 3  # Default to 'portable'
            for key, value in transport_mapping.items():
                if key in transport_text:
                    transport_value = value
                    break
            transportability = transport_value
        else:
            # If it's already an integer, use it
            transportability = int(transportability) if transportability else 3

        user_resource, created = UserResource.objects.update_or_create(
            specific_name=resource_data['specific_name'],
            user_environment=current_env,
            defaults={
                'generic_resource': generic_resource,
                'location_details': resource_data.get('location_details', ''),
                'ownership_details': resource_data.get('ownership_details', ''),
                'contact_info': resource_data.get('contact_info', ''),
                'notes': resource_data.get('notes', ''),
                'availability': availability,
                'condition': condition,
                'transportability': transportability,
            }
        )

        # Add the resource to the inventory
        inventory.resources.add(user_resource)

        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def _import_limitations(self, user_profile: UserProfile, limitations_data: List[Dict[str, Any]]):
        """Import limitations"""
        for limitation_data in limitations_data:
            # Get or create generic limitation
            generic_limitation, limitation_created = GenericUserLimitation.objects.get_or_create(
                code=limitation_data['limitation_code'],
                defaults={
                    'description': limitation_data.get('description', limitation_data['limitation_code']),
                    'limitation_type': 'GENERAL'  # Default type
                }
            )

            if limitation_created:
                self.created_records += 1
                self.warnings.append(f"Created generic limitation: {limitation_data['limitation_code']}")

            # Map business object fields to model fields
            from datetime import timedelta
            today = date.today()

            model_data = {
                'severity': limitation_data['severity'],
                'user_awareness': 100,  # Default high awareness for explicitly stated limitations
                'is_unlimited': limitation_data.get('is_temporary', True) == False,  # Invert is_temporary to is_unlimited
                'valid_until': today + timedelta(days=365),  # Default 1 year review
                'effective_start': today,
                'effective_end': today + timedelta(days=365),
                'duration_estimate': 'Ongoing'
            }

            # Override with any provided temporal data
            if 'frequency' in limitation_data:
                frequency = limitation_data['frequency']
                if frequency == 'constantly':
                    model_data['duration_estimate'] = 'Constant'
                elif frequency == 'daily':
                    model_data['duration_estimate'] = 'Daily occurrence'
                elif frequency == 'weekly':
                    model_data['duration_estimate'] = 'Weekly occurrence'
                elif frequency == 'situational':
                    model_data['duration_estimate'] = 'Situational'

            # Create or update user limitation
            limitation, created = UserLimitation.objects.update_or_create(
                user_profile=user_profile,
                generic_limitation=generic_limitation,
                defaults=model_data
            )

            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_preferences(self, user_profile: UserProfile, preferences_data: List[Dict[str, Any]]):
        """Import preferences"""
        from datetime import date, timedelta

        for preference_data in preferences_data:
            # Get environment if environment_specific
            environment = None
            if preference_data.get('environment_specific', False):
                environment = user_profile.current_environment

            # Add default temporal fields if missing (required by TemporalRecord)
            defaults = {**preference_data}
            if 'effective_start' not in defaults or not defaults['effective_start']:
                defaults['effective_start'] = date.today()
            if 'duration_estimate' not in defaults or not defaults['duration_estimate']:
                defaults['duration_estimate'] = 'ongoing'
            if 'effective_end' not in defaults or not defaults['effective_end']:
                # Set end date to 1 year from start for ongoing preferences
                start_date = defaults['effective_start']
                if isinstance(start_date, str):
                    from datetime import datetime
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                defaults['effective_end'] = start_date + timedelta(days=365)

            defaults['environment'] = environment

            preference, created = Preference.objects.update_or_create(
                user_profile=user_profile,
                pref_name=preference_data['pref_name'],
                defaults=defaults
            )
            
            if created:
                self.created_records += 1
            else:
                self.updated_records += 1
    
    def _import_current_mood(self, user_profile: UserProfile, mood_data: Dict[str, Any]):
        """Import current mood with field mapping"""
        from datetime import date, timedelta

        # Map guigui.json fields to CurrentMood model fields
        mapped_data = {}

        # Map description field
        if 'mood_description' in mood_data:
            mapped_data['description'] = mood_data['mood_description']
        elif 'description' in mood_data:
            mapped_data['description'] = mood_data['description']
        else:
            mapped_data['description'] = 'Current mood state'

        # Map height field (overall mood intensity)
        # Calculate average of available mood dimensions
        mood_dimensions = []
        if 'energy_level' in mood_data:
            mood_dimensions.append(mood_data['energy_level'])
        if 'optimism' in mood_data:
            mood_dimensions.append(mood_data['optimism'])
        if 'social_engagement' in mood_data:
            mood_dimensions.append(mood_data['social_engagement'])
        # Invert stress_level (high stress = lower mood)
        if 'stress_level' in mood_data:
            mood_dimensions.append(100 - mood_data['stress_level'])

        if mood_dimensions:
            mapped_data['height'] = int(sum(mood_dimensions) / len(mood_dimensions))
        elif 'height' in mood_data:
            mapped_data['height'] = mood_data['height']
        else:
            mapped_data['height'] = 70  # Default moderate mood

        # Map user_awareness
        mapped_data['user_awareness'] = mood_data.get('user_awareness', 80)  # Default high awareness

        # Set processed_at
        mapped_data['processed_at'] = datetime.now()

        # Add default temporal fields if missing (required by TemporalRecord)
        if 'effective_start' not in mapped_data:
            mapped_data['effective_start'] = date.today()
        if 'duration_estimate' not in mapped_data:
            mapped_data['duration_estimate'] = '1 day'
        if 'effective_end' not in mapped_data:
            mapped_data['effective_end'] = date.today() + timedelta(days=1)

        mood, created = CurrentMood.objects.update_or_create(
            user_profile=user_profile,
            defaults=mapped_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def _import_trust_level(self, user_profile: UserProfile, trust_data: Dict[str, Any]):
        """Import trust level with field mapping"""
        # Map guigui.json fields to TrustLevel model fields
        mapped_data = {}

        # Map value field (should be present)
        mapped_data['value'] = trust_data.get('value', 70)  # Default moderate trust

        # Map aggregate_type and aggregate_id
        mapped_data['aggregate_type'] = 'General'
        mapped_data['aggregate_id'] = 'GENERAL_TRUST'

        # Map notes field
        notes_parts = []
        if 'progression_notes' in trust_data:
            notes_parts.append(f"Progression: {trust_data['progression_notes']}")
        if 'domain_scores' in trust_data:
            domain_scores = trust_data['domain_scores']
            if isinstance(domain_scores, dict):
                domain_info = ', '.join([f"{k}: {v}" for k, v in domain_scores.items()])
                notes_parts.append(f"Domain scores: {domain_info}")
            else:
                notes_parts.append(f"Domain scores: {domain_scores}")

        mapped_data['notes'] = '; '.join(notes_parts) if notes_parts else 'General trust level'

        trust_level, created = TrustLevel.objects.update_or_create(
            user_profile=user_profile,
            defaults=mapped_data
        )
        
        if created:
            self.created_records += 1
        else:
            self.updated_records += 1
    
    def validate_profile_data(self, profile_data: Dict[str, Any]) -> List[str]:
        """
        Validate profile data before import.
        
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Validate required fields
        if 'user_account' not in profile_data:
            errors.append("user_account is required")
        elif not isinstance(profile_data['user_account'], dict):
            errors.append("user_account must be a dictionary")
        else:
            user_account = profile_data['user_account']
            if 'username' not in user_account:
                errors.append("username is required in user_account")
            if 'email' not in user_account:
                errors.append("email is required in user_account")
        
        if 'profile_name' not in profile_data:
            errors.append("profile_name is required")
        
        # Validate environments have only one current
        environments = profile_data.get('environments', [])
        if environments:
            current_count = sum(1 for env in environments if env.get('is_current', False))
            if current_count == 0:
                errors.append("At least one environment must be marked as current")
            elif current_count > 1:
                errors.append("Only one environment can be marked as current")
        
        # Validate trait codes
        traits = profile_data.get('traits', [])
        if traits:
            valid_trait_codes = set(GenericTrait.objects.values_list('code', flat=True))
            invalid_traits = [
                trait['trait_code'] for trait in traits 
                if trait.get('trait_code') not in valid_trait_codes
            ]
            if invalid_traits:
                errors.append(f"Invalid trait codes: {', '.join(invalid_traits)}")
        
        return errors

    # Import History Tracking Methods

    def _create_import_history(self, profile_data: Dict[str, Any], options: Dict[str, Any]):
        """Create an import history record to track the operation."""
        try:
            from apps.admin_tools.models import ImportHistory

            # Extract metadata from profile data
            source_metadata = {
                'profile_name': profile_data.get('profile_name', 'Unknown'),
                'has_demographics': 'demographics' in profile_data,
                'has_environment': 'environment' in profile_data,
                'has_traits': 'traits' in profile_data and len(profile_data.get('traits', [])) > 0,
                'has_skills': 'skills' in profile_data and len(profile_data.get('skills', [])) > 0,
                'has_resources': 'resources' in profile_data and len(profile_data.get('resources', [])) > 0,
                'data_size_kb': len(json.dumps(profile_data).encode('utf-8')) / 1024
            }

            self.import_history = ImportHistory.objects.create(
                import_type='validation_only' if options.get('validate_only') else 'single',
                initiated_by=self.initiated_by,
                total_profiles=1,
                import_options=options,
                source_metadata=source_metadata,
                status='pending'
            )

        except Exception as e:
            logger.warning(f"Failed to create import history: {e}")
            self.import_history = None

    def _update_import_status(self, status: str):
        """Update the import status."""
        if self.import_history:
            try:
                self.import_history.status = status
                self.import_history.save(update_fields=['status'])
            except Exception as e:
                logger.warning(f"Failed to update import status: {e}")

    def _add_import_error(self, error_message: str, error_type: str, context: Dict[str, Any] = None):
        """Add an error to the import history."""
        if self.import_history:
            try:
                self.import_history.add_error(error_message, error_type, context)
            except Exception as e:
                logger.warning(f"Failed to add import error: {e}")

    def _add_import_warning(self, warning_message: str, warning_type: str = 'general', context: Dict[str, Any] = None):
        """Add a warning to the import history."""
        if self.import_history:
            try:
                self.import_history.add_warning(warning_message, warning_type, context)
            except Exception as e:
                logger.warning(f"Failed to add import warning: {e}")

    def _update_validation_time(self, validation_time: float):
        """Update validation time in import history."""
        if self.import_history:
            try:
                self.import_history.validation_time_seconds = validation_time
                self.import_history.save(update_fields=['validation_time_seconds'])
            except Exception as e:
                logger.warning(f"Failed to update validation time: {e}")

    def _update_database_time(self, database_time: float):
        """Update database operation time in import history."""
        if self.import_history:
            try:
                self.import_history.database_time_seconds = database_time
                self.import_history.save(update_fields=['database_time_seconds'])
            except Exception as e:
                logger.warning(f"Failed to update database time: {e}")

    def _complete_import_success(self, user_profile: 'UserProfile'):
        """Mark the import as successfully completed."""
        if self.import_history:
            try:
                self.import_history.successful_imports = 1
                self.import_history.failed_imports = 0
                self.import_history.created_profiles = [str(user_profile.id)]

                # Calculate data quality metrics
                completeness_score = self._calculate_profile_completeness(user_profile)
                self.import_history.completeness_score = completeness_score / 100.0  # Convert to 0-1 scale
                self.import_history.data_quality_score = min(1.0, completeness_score / 80.0)  # 80% = good quality

                # Add any warnings that were collected
                for warning in self.warnings:
                    self.import_history.add_warning(warning, 'import_process')

                self.import_history.mark_completed(success_count=1, failure_count=0)

            except Exception as e:
                logger.warning(f"Failed to complete import success tracking: {e}")

    def _complete_import_failure(self, error_message: str):
        """Mark the import as failed."""
        if self.import_history:
            try:
                self.import_history.mark_failed(error_message)
            except Exception as e:
                logger.warning(f"Failed to complete import failure tracking: {e}")

    def _calculate_profile_completeness(self, profile: 'UserProfile') -> float:
        """Calculate profile completeness percentage for quality metrics."""
        try:
            total_fields = 10  # Adjust based on what we consider complete
            completed_fields = 0

            # Check basic profile info
            if profile.profile_name:
                completed_fields += 1

            # Check demographics
            if hasattr(profile, 'demographics') and profile.demographics:
                demo = profile.demographics
                if demo.full_name:
                    completed_fields += 1
                if demo.age and demo.age > 0:
                    completed_fields += 1

            # Check environment
            if profile.current_environment:
                completed_fields += 1

            # Check skills
            if profile.skills.exists():
                completed_fields += 1

            # Check resources
            total_resources = sum(env.user_resources.count() for env in profile.environments.all())
            if total_resources > 0:
                completed_fields += 1

            # Check preferences
            if profile.preferences.exists():
                completed_fields += 1

            # Check traits
            if hasattr(profile, 'usertraitinclination_set') and profile.usertraitinclination_set.exists():
                completed_fields += 1

            # Check beliefs
            if hasattr(profile, 'belief_set') and profile.belief_set.exists():
                completed_fields += 1

            # Check goals (aspirations/intentions)
            if (hasattr(profile, 'aspiration_set') and profile.aspiration_set.exists()) or \
               (hasattr(profile, 'intention_set') and profile.intention_set.exists()):
                completed_fields += 1

            return (completed_fields / total_fields) * 100

        except Exception as e:
            logger.warning(f"Failed to calculate profile completeness: {e}")
            return 0.0

    # Batch Import Methods

    def import_profiles_batch(self, profiles_data: List[Dict[str, Any]], options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Import multiple user profiles in batch with comprehensive tracking.

        Args:
            profiles_data: List of complete profile data dictionaries
            options: Batch import options (continue_on_error, validate_only, etc.)

        Returns:
            Dict with batch import results including success/failure counts and detailed results

        Raises:
            ProfileImportError: If batch import fails completely
        """
        if options is None:
            options = {}

        batch_start_time = time.time()
        total_profiles = len(profiles_data)
        successful_imports = 0
        failed_imports = 0
        import_results = []
        batch_errors = []
        batch_warnings = []

        # Create batch import history record
        batch_history = self._create_batch_import_history(total_profiles, options)

        print(f"🚀 Starting batch import of {total_profiles} profiles...")

        try:
            for i, profile_data in enumerate(profiles_data):
                profile_name = profile_data.get('profile_name', f'Profile {i+1}')
                print(f"📝 Importing profile {i+1}/{total_profiles}: {profile_name}")

                try:
                    # Create individual import service for each profile
                    individual_service = ProfileImportService(initiated_by=self.initiated_by)
                    result = individual_service.import_profile(profile_data, options)

                    if result['success']:
                        successful_imports += 1
                        import_results.append({
                            'index': i,
                            'profile_name': profile_name,
                            'status': 'success',
                            'profile_id': result['profile_id'],
                            'user_id': result['user_id'],
                            'import_history_id': result.get('import_history_id'),
                            'warnings': result.get('warnings', [])
                        })

                        # Collect warnings
                        if result.get('warnings'):
                            batch_warnings.extend([f"Profile {i+1}: {w}" for w in result['warnings']])

                    else:
                        failed_imports += 1
                        import_results.append({
                            'index': i,
                            'profile_name': profile_name,
                            'status': 'failed',
                            'error': 'Import returned success=False'
                        })

                except Exception as e:
                    failed_imports += 1
                    error_msg = str(e)
                    batch_errors.append(f"Profile {i+1} ({profile_name}): {error_msg}")

                    import_results.append({
                        'index': i,
                        'profile_name': profile_name,
                        'status': 'failed',
                        'error': error_msg
                    })

                    # Add error to batch history
                    if batch_history:
                        batch_history.add_error(f"Profile {i+1} failed: {error_msg}", 'profile_import')

                    # Check if we should continue on error
                    if not options.get('continue_on_error', True):
                        print(f"❌ Stopping batch import due to error in profile {i+1}")
                        break

                # Progress update
                if (i + 1) % 10 == 0 or (i + 1) == total_profiles:
                    progress = ((i + 1) / total_profiles) * 100
                    print(f"📊 Progress: {i+1}/{total_profiles} ({progress:.1f}%) - ✅ {successful_imports} success, ❌ {failed_imports} failed")

            # Calculate batch metrics
            batch_end_time = time.time()
            processing_time = batch_end_time - batch_start_time
            success_rate = (successful_imports / total_profiles) * 100 if total_profiles > 0 else 0

            # Update batch history
            if batch_history:
                batch_history.successful_imports = successful_imports
                batch_history.failed_imports = failed_imports
                batch_history.processing_time_seconds = processing_time

                # Add batch warnings
                for warning in batch_warnings:
                    batch_history.add_warning(warning, 'batch_import')

                # Calculate overall data quality score
                if successful_imports > 0:
                    # Average quality score from individual imports
                    quality_scores = []
                    for result in import_results:
                        if result['status'] == 'success' and result.get('import_history_id'):
                            try:
                                from apps.admin_tools.models import ImportHistory
                                individual_history = ImportHistory.objects.get(id=result['import_history_id'])
                                if individual_history.data_quality_score:
                                    quality_scores.append(individual_history.data_quality_score)
                            except:
                                pass

                    if quality_scores:
                        batch_history.data_quality_score = sum(quality_scores) / len(quality_scores)

                batch_history.mark_completed(successful_imports, failed_imports)

            # Prepare final result
            result = {
                'success': successful_imports > 0,
                'batch_import_history_id': str(batch_history.id) if batch_history else None,
                'total_profiles': total_profiles,
                'successful_imports': successful_imports,
                'failed_imports': failed_imports,
                'success_rate': success_rate,
                'processing_time_seconds': processing_time,
                'import_results': import_results,
                'errors': batch_errors,
                'warnings': batch_warnings,
                'summary': {
                    'total': total_profiles,
                    'success': successful_imports,
                    'failed': failed_imports,
                    'success_rate': f"{success_rate:.1f}%",
                    'processing_time': f"{processing_time:.2f}s"
                }
            }

            print(f"\n🎯 Batch Import Complete!")
            print(f"   Total: {total_profiles} profiles")
            print(f"   ✅ Success: {successful_imports} ({success_rate:.1f}%)")
            print(f"   ❌ Failed: {failed_imports}")
            print(f"   ⏱️ Time: {processing_time:.2f}s")

            return result

        except Exception as e:
            # Mark batch as failed
            if batch_history:
                batch_history.mark_failed(f"Batch import failed: {str(e)}")

            logger.error(f"Batch profile import failed: {str(e)}")
            raise ProfileImportError(f"Batch import failed: {str(e)}")

    def _create_batch_import_history(self, total_profiles: int, options: Dict[str, Any]):
        """Create a batch import history record."""
        try:
            from apps.admin_tools.models import ImportHistory

            source_metadata = {
                'batch_size': total_profiles,
                'import_type': 'batch',
                'options': options
            }

            history = ImportHistory.objects.create(
                import_type='batch',
                initiated_by=self.initiated_by,
                total_profiles=total_profiles,
                import_options=options,
                source_metadata=source_metadata,
                status='pending'
            )

            return history

        except Exception as e:
            logger.warning(f"Failed to create batch import history: {e}")
            return None
