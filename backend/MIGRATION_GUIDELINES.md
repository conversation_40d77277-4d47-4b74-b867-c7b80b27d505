# Django Migration Guidelines for Goali Project

## 🎯 Overview

This document establishes best practices for handling database migrations in the Goali project, based on Django 5.2 documentation and lessons learned from migration conflicts.

## 🚨 Current Issues & Resolution

### Identified Problems
1. **Multiple 0002_* migrations** in main app causing conflicts
2. **Complex merge migration** with multiple dependencies
3. **Migration state inconsistencies** requiring manual fixes

### Immediate Actions Required
```bash
# 1. Check current state
python migration_best_practices.py status

# 2. Validate migrations
python migration_best_practices.py validate

# 3. Fix conflicts (if any)
python fix_migration_conflicts.py
```

## 📋 Migration Workflow

### 1. Before Making Model Changes
```bash
# Always check current migration state
python manage.py showmigrations

# Ensure you're on the latest migrations
python manage.py migrate
```

### 2. Creating Migrations
```bash
# Make model changes first, then create migrations
python manage.py makemigrations

# Use descriptive names for complex migrations
python manage.py makemigrations --name add_user_profile_metadata user

# Review generated migration before applying
python manage.py sqlmigrate app_name migration_number
```

### 3. Handling Conflicts
```bash
# If multiple developers created migrations simultaneously
python manage.py makemigrations --merge

# Always review merge migrations manually
# Test merge migrations thoroughly
```

### 4. Applying Migrations
```bash
# See what will be applied
python manage.py migrate --plan

# Apply migrations (use our safe migration script)
python migration_best_practices.py migrate

# Or apply to specific app
python migration_best_practices.py migrate user
```

## 🔧 Migration Types & Best Practices

### Schema Migrations
```python
# ✅ Good: Simple field addition
class Migration(migrations.Migration):
    dependencies = [
        ('user', '0015_previous_migration'),
    ]
    
    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='new_field',
            field=models.CharField(max_length=100, null=True),
        ),
    ]
```

### Data Migrations
```python
# ✅ Good: Separate data migration
def populate_new_field(apps, schema_editor):
    UserProfile = apps.get_model('user', 'UserProfile')
    for profile in UserProfile.objects.all():
        profile.new_field = calculate_value(profile)
        profile.save()

class Migration(migrations.Migration):
    dependencies = [
        ('user', '0016_add_new_field'),
    ]
    
    operations = [
        migrations.RunPython(
            populate_new_field,
            migrations.RunPython.noop  # or reverse function
        ),
    ]
```

### Complex Migrations
```python
# ✅ Good: Break into smaller steps
# Migration 1: Add new field (nullable)
# Migration 2: Populate data
# Migration 3: Make field non-nullable
# Migration 4: Remove old field
```

## 🚫 What NOT to Do

### ❌ Never Edit Applied Migrations
```bash
# Don't edit migrations that have been applied in production
# Don't delete migration files that exist in production
# Don't change migration dependencies after applying
```

### ❌ Don't Mix Schema and Data Changes
```python
# Bad: Schema and data in same migration
operations = [
    migrations.AddField(...),  # Schema change
    migrations.RunPython(...), # Data change - separate this!
]
```

### ❌ Don't Skip Migration Numbers
```bash
# Bad: Creating 0005_* when 0003_* doesn't exist
# Always create migrations in sequence
```

## 🔄 Dependency Management

### App Dependencies (Goali Project)
```
admin_tools (no dependencies)
    ↓
user (depends on admin_tools)
    ↓
activity (depends on user)
    ↓
main (depends on user, activity)
```

### Migration Dependencies
```python
# Always specify correct dependencies
class Migration(migrations.Migration):
    dependencies = [
        ('user', '0015_latest_user_migration'),
        ('activity', '0008_latest_activity_migration'),
    ]
```

## 🧪 Testing Migrations

### Local Testing
```bash
# 1. Test on fresh database
python manage.py migrate --run-syncdb

# 2. Test migration rollback (if reversible)
python manage.py migrate app_name previous_migration

# 3. Test migration forward again
python manage.py migrate app_name latest_migration
```

### Staging Testing
```bash
# 1. Backup staging database
python migration_best_practices.py backup

# 2. Apply migrations to staging
python migration_best_practices.py migrate

# 3. Verify application functionality
# 4. Run full test suite
```

## 🚀 Production Deployment

### Pre-deployment Checklist
- [ ] All migrations tested on staging
- [ ] Database backup created
- [ ] Migration plan reviewed
- [ ] Rollback plan prepared
- [ ] Team notified of deployment

### Deployment Process
```bash
# 1. Backup production database
pg_dump production_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Apply migrations
python manage.py migrate

# 3. Verify application health
# 4. Monitor for issues
```

## 🛠️ Troubleshooting

### Common Issues

#### "Migration has unapplied dependencies"
```bash
# Check migration state
python manage.py showmigrations

# Apply missing dependencies first
python manage.py migrate app_name dependency_migration
```

#### "Conflicting migrations detected"
```bash
# Create merge migration
python manage.py makemigrations --merge

# Review and test merge migration
```

#### "Migration state corruption"
```bash
# Use our fix script (last resort)
python manage.py fix_migration_state

# Or manually fix in django_migrations table
```

## 📚 Resources

- [Django 5.2 Migration Documentation](https://docs.djangoproject.com/en/5.2/topics/migrations/)
- [Migration Best Practices](https://docs.djangoproject.com/en/5.2/howto/writing-migrations/)
- Project-specific scripts:
  - `migration_best_practices.py` - Safe migration management
  - `fix_migration_conflicts.py` - Conflict resolution
  - `apps/main/management/commands/fix_migration_state.py` - Emergency fixes

## 🔄 Regular Maintenance

### Weekly Tasks
- [ ] Review migration status across all environments
- [ ] Check for migration conflicts in development
- [ ] Validate migration dependencies

### Before Major Releases
- [ ] Audit all pending migrations
- [ ] Test complete migration sequence on staging
- [ ] Prepare rollback procedures
- [ ] Document any breaking changes
