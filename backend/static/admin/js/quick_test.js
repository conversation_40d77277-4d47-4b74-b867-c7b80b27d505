// Merged QuickTest Module - Reusable quick benchmark testing functionality with enhancements

class QuickTest {
    constructor(config = {}) {
        this.config = {
            scenariosApiUrl: config.scenariosApiUrl || '/admin/benchmarks/api/scenarios/',
            benchmarkRunApiUrl: config.benchmarkRunApiUrl || '/admin/benchmarks/api/run/',
            taskStatusApiUrl: config.taskStatusApiUrl || '/admin/benchmarks/api/task-status/',
            templatesApiUrl: config.templatesApiUrl || '/admin/benchmarks/api/templates/',
            showSuccessMessage: config.showSuccessMessage || this.defaultShowSuccess,
            showErrorMessage: config.showErrorMessage || this.defaultShowError,
            storageKey: config.storageKey || 'quickTestConfig',
            ...config
        };

        this.currentTaskId = null;

        // Properties from QuickTestModalEnhancements
        this.modal = null; // Will be set in init
        this.currentTab = 'basic';
        this.validationState = {
            scenario: false,
            template: false,
            profile: false
        };
        this.isDragging = false;
        this.dragOffset = { x: 0, y: 0 };
        this.autoSaveTimer = null;
        this.keyboardShortcuts = new Map();


        this.init();
    }

    init() {
        console.log('QuickTest: Starting initialization...');

        this.bindEvents();
        this.loadConfig();
        this.populateScenarios();
        this.populateTemplates();
        this.populateFakeUserProfiles();

        // Initialize modal enhancements
        this.modal = document.getElementById('quick-test-config-modal');
        if (!this.modal) {
            console.warn('QuickTest: Modal not found for enhancements');
            return;
        }

        console.log('QuickTest: Modal found, initializing enhancements...');
        // Add a small delay to ensure DOM is fully ready
        setTimeout(() => {
            this.initializeModalEnhancements();
        }, 100);
    }

    initializeModalEnhancements() {
        console.log('QuickTest: Setting up modal enhancements...');

        try {
            this.setupTabNavigation();
            console.log('QuickTest: Tab navigation setup complete');

            this.setupCollapsibleSections();
            console.log('QuickTest: Collapsible sections setup complete');

            this.setupFieldValidation();
            console.log('QuickTest: Field validation setup complete');

            this.setupPreviewFeatures();
            console.log('QuickTest: Preview features setup complete');

            this.setupEnhancedEventListeners();
            console.log('QuickTest: Enhanced event listeners setup complete');

            this.setupKeyboardNavigation();
            console.log('QuickTest: Keyboard navigation setup complete');

            this.setupDragFunctionality();
            console.log('QuickTest: Drag functionality setup complete');

            this.setupAutoSave();
            console.log('QuickTest: Auto-save setup complete');

            this.setupAccessibilityFeatures();
            console.log('QuickTest: Accessibility features setup complete');

            console.log('QuickTest: All enhancements initialization complete');
        } catch (error) {
            console.error('QuickTest: Error during modal enhancements initialization:', error);
        }
    }

    bindEvents() {
        // Quick test button
        const quickTestBtn = document.getElementById('quick-test-btn');
        if (quickTestBtn) {
            quickTestBtn.addEventListener('click', () => this.runTest());
        }

        // Configure button
        const configureBtn = document.getElementById('configure-quick-test-btn');
        if (configureBtn) {
            configureBtn.addEventListener('click', () => this.showConfig());
        }

        // Configuration modal events (listeners moved to setupEnhancedEventListeners)
        // Keep hideConfig and saveConfig calls here as they are core QuickTest actions
        const configModal = document.getElementById('quick-test-config-modal');
        if (configModal) {
             // Cancel button
            const cancelBtn = document.getElementById('cancel-config-btn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => this.hideConfig());
            }

            // Save configuration form
            const configForm = document.getElementById('quick-test-config-form');
            if (configForm) {
                configForm.addEventListener('submit', (e) => this.saveConfig(e));
            }

            // Template selection change (listener moved to setupFieldValidation)

            // Close modal when clicking outside (listener moved to setupAccessibilityFeatures or similar)

            // Close modal with X button (listener moved to setupAccessibilityFeatures or similar)
        }
    }

    loadConfig() {
        const config = localStorage.getItem(this.config.storageKey);
        if (config) {
            try {
                const parsed = JSON.parse(config);
                this.updateButtonState(parsed);
            } catch (e) {
                console.warn('QuickTest: Failed to parse saved quick test config:', e);
                this.updateButtonState(null);
            }
        } else {
            this.updateButtonState(null);
        }
    }

    updateButtonState(config) {
        const quickTestBtn = document.getElementById('quick-test-btn');
        const quickTestDetails = document.getElementById('quick-test-details');

        if (!quickTestBtn) return;

        if (config && config.scenarioId) {
            quickTestBtn.disabled = false;
            quickTestBtn.innerHTML = '<i class="fas fa-play"></i> Run Quick Test';
            if (quickTestDetails) {
                quickTestDetails.textContent = `Ready: ${config.scenarioName || 'Scenario'} (${config.runs || 1} run${config.runs > 1 ? 's' : ''})`;
            }
        } else {
            quickTestBtn.disabled = true;
            quickTestBtn.innerHTML = '<i class="fas fa-cog"></i> Configure First';
            if (quickTestDetails) {
                quickTestDetails.textContent = 'Click Configure to set up quick test parameters';
            }
        }
    }

    async populateScenarios() {
        const scenarioSelect = document.getElementById('quick-scenario-select');
        if (!scenarioSelect) return;

        try {
            const response = await fetch(this.config.scenariosApiUrl);
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            console.log('QuickTest: Fetched scenarios data:', data); // Keep log
            scenarioSelect.innerHTML = '<option value="">-- Select Scenario --</option>';

            if (data.scenarios) {
                data.scenarios.forEach(scenario => {
                    if (scenario.is_active) {
                        const option = document.createElement('option');
                        option.value = scenario.id;
                        option.textContent = `${scenario.name} (${scenario.agent_role})`;
                        option.dataset.name = scenario.name;
                        scenarioSelect.appendChild(option);
                        console.log(`QuickTest: Added scenario option ${scenario.id}:`, option.dataset); // Keep log
                    }
                });
            }
        } catch (error) {
            console.error('QuickTest: Error loading scenarios for quick test:', error);
        }
    }

    async populateTemplates() {
        const templateSelect = document.getElementById('quick-template-select');
        if (!templateSelect) return;

        try {
            const response = await fetch(this.config.templatesApiUrl);
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            console.log('QuickTest: Fetched templates data:', data); // Keep log
            templateSelect.innerHTML = '<option value="">-- Select Template --</option>';

            if (data.templates) {
                data.templates.forEach(template => {
                    if (template.is_active) {
                        const option = document.createElement('option');
                        option.value = template.id;
                        option.textContent = `${template.name} (${template.category})`;
                        option.dataset.name = template.name;
                        option.dataset.description = template.description;
                        option.dataset.criteria = JSON.stringify(template.criteria || {});
                        templateSelect.appendChild(option);
                        console.log(`QuickTest: Added template option ${template.id}:`, option.dataset); // Keep log
                    }
                });
            }
        } catch (error) {
            console.error('QuickTest: Error loading templates for quick test:', error);
        }
    }

    async populateFakeUserProfiles() {
        const profileSelect = document.getElementById('quick-user-profile-select');
        if (!profileSelect) return;

        try {
            // Fetch user profiles with is_real=False filter
            const response = await fetch('/admin/benchmarks/api/user-profiles/?is_real=false');
            if (!response.ok) throw new Error(`HTTP error ${response.status}`);

            const data = await response.json();
            console.log('QuickTest: Fetched user profiles data:', data); // Keep log
            profileSelect.innerHTML = '<option value="">-- Select Test Profile --</option>';

            if (data.profiles && Array.isArray(data.profiles)) {
                data.profiles.forEach(profile => {
                    const option = document.createElement('option');
                    option.value = profile.id;

                    // Create descriptive text with trust level and demographics
                    const trustInfo = profile.trust_level ? `Trust: ${profile.trust_level}` : '';
                    const demoInfo = profile.demographics ?
                        `${profile.demographics.age}y, ${profile.demographics.gender || 'N/A'}` : '';
                    const description = [trustInfo, demoInfo].filter(Boolean).join(' | ');

                    option.textContent = `${profile.profile_name}${description ? ` (${description})` : ''}`;
                    option.dataset.profileData = JSON.stringify(profile);
                    profileSelect.appendChild(option);
                    console.log(`QuickTest: Added profile option ${profile.id}:`, option.dataset); // Keep log
                });
            }
        } catch (error) {
            console.error('QuickTest: Error loading fake user profiles for quick test:', error);
        }
    }

    getExecutionModeParams() {
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const executionMode = executionModeSelect?.value || 'mock';

        const modeParams = {
            'mock': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: false
            },
            'real-tools': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: false
            },
            'real-llm': {
                use_real_llm: true,
                use_real_tools: false,
                use_real_db: false
            },
            'real-db': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: true
            },
            'partial-real': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: true
            },
            'full-real': {
                use_real_llm: true,
                use_real_tools: true,
                use_real_db: true
            }
        };

        return modeParams[executionMode] || modeParams['mock'];
    }

    getExecutionModeParamsFromConfig(config) {
        const executionMode = config.executionMode || 'mock';

        const modeParams = {
            'mock': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: false
            },
            'real-tools': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: false
            },
            'real-llm': {
                use_real_llm: true,
                use_real_tools: false,
                use_real_db: false
            },
            'real-db': {
                use_real_llm: false,
                use_real_tools: false,
                use_real_db: true
            },
            'partial-real': {
                use_real_llm: false,
                use_real_tools: true,
                use_real_db: true
            },
            'full-real': {
                use_real_llm: true,
                use_real_tools: true,
                use_real_db: true
            }
        };

        return modeParams[executionMode] || modeParams['mock'];
    }

    showConfig() {
        console.log('🔧 QuickTest: showConfig() called');
        const configModal = document.getElementById('quick-test-config-modal');
        if (!configModal) {
            console.error('❌ QuickTest: Modal element not found!');
            return;
        }

        console.log('✅ QuickTest: Modal element found:', configModal);

        // Load current configuration
        const config = localStorage.getItem(this.config.storageKey);
        if (config) {
            try {
                const parsed = JSON.parse(config);
                this.populateConfigForm(parsed);
            } catch (e) {
                console.warn('QuickTest: Failed to parse saved config:', e);
            }
        }

        // Force display with multiple methods
        console.log('🔧 QuickTest: Setting modal display...');
        configModal.style.display = 'block';
        configModal.style.visibility = 'visible';
        configModal.style.opacity = '1';
        configModal.classList.remove('hidden');

        console.log('✅ QuickTest: Modal should now be visible');
        console.log('   Display:', configModal.style.display);
        console.log('   Visibility:', configModal.style.visibility);
        console.log('   Opacity:', configModal.style.opacity);

        // Ensure modal enhancements are initialized and applied when showing modal
        // This is now handled in init and event listeners
    }

    hideConfig() {
        const configModal = document.getElementById('quick-test-config-modal');
        if (configModal) {
            configModal.style.display = 'none';
        }
    }

    populateConfigForm(config) {
        const scenarioSelect = document.getElementById('quick-scenario-select');
        const runsInput = document.getElementById('quick-runs-input');
        const semanticEvalCheckbox = document.getElementById('quick-semantic-eval');
        const templateSelect = document.getElementById('quick-template-select');
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const userProfileSelect = document.getElementById('quick-user-profile-select');

        // Enhanced fields
        const detailedLoggingCheckbox = document.getElementById('quick-detailed-logging');
        const saveArtifactsCheckbox = document.getElementById('quick-save-artifacts');
        const timeoutInput = document.getElementById('quick-timeout-input');
        const retryAttemptsInput = document.getElementById('quick-retry-attempts');

        if (scenarioSelect) scenarioSelect.value = config.scenarioId || '';
        if (runsInput) runsInput.value = config.runs || 1;
        if (semanticEvalCheckbox) semanticEvalCheckbox.checked = config.semanticEval !== false;
        if (templateSelect) templateSelect.value = config.templateId || '';
        if (executionModeSelect) executionModeSelect.value = config.executionMode || 'mock';
        if (userProfileSelect) userProfileSelect.value = config.userProfileId || '';

        // Populate enhanced fields
        if (detailedLoggingCheckbox) detailedLoggingCheckbox.checked = config.detailedLogging || false;
        if (saveArtifactsCheckbox) saveArtifactsCheckbox.checked = config.saveArtifacts || false;
        if (timeoutInput) timeoutInput.value = config.timeout || 300;
        if (retryAttemptsInput) retryAttemptsInput.value = config.retryAttempts || 1;

        // Update template preview if a template is selected
        if (config.templateId) {
            this.updateTemplatePreview(config.templateId);
        }

        // Trigger validation for loaded values
        if (config.scenarioId) {
            this.validateField('scenario', config.scenarioId, 'scenario-status');
        }
        if (config.templateId) {
            this.validateField('template', config.templateId, 'template-status');
        }
        if (config.userProfileId) {
            this.validateField('profile', config.userProfileId, 'profile-status');
        }
        if (config.executionMode) {
            this.updateExecutionModeInfo(config.executionMode);
        }
    }

    // Keep the original updateTemplatePreview from quick_test.js
    updateTemplatePreview(templateId) {
        const templateSelect = document.getElementById('quick-template-select');
        const templatePreview = document.getElementById('quick-template-preview');

        if (!templateSelect || !templatePreview) return;

        const selectedOption = templateSelect.querySelector(`option[value="${templateId}"]`);
        if (!selectedOption) {
            console.warn(`QuickTest: No option found for template ID: ${templateId}`); // Added log
            // Clear preview if no option is found
            templatePreview.style.display = 'none';
            return;
        }

        console.log('QuickTest: Selected template option dataset:', selectedOption.dataset); // Added log

        const templateName = selectedOption.dataset.name;
        const templateDescription = selectedOption.dataset.description;
        const templateCriteria = this.parseJsonSafely(selectedOption.dataset.criteria, {}); // Use safe parse
        console.log('QuickTest: Parsed template criteria:', templateCriteria); // Added log


        let criteriaHtml = '';
        if (Object.keys(templateCriteria).length > 0) {
            criteriaHtml = '<h5>Evaluation Criteria:</h5><ul class="criteria-list">'; // Use .criteria-list
            for (const [dimension, criteria] of Object.entries(templateCriteria)) {
                criteriaHtml += `<li class="criteria-item"><strong>${dimension}:</strong> ${Array.isArray(criteria) ? criteria.join(', ') : criteria}</li>`; // Use .criteria-item
            }
            criteriaHtml += '</ul>';
        }

        templatePreview.innerHTML = `
            <div class="template-preview-content">
                <h4>${templateName}</h4>
                <p>${templateDescription}</p>
                ${criteriaHtml}
            </div>
        `;
        templatePreview.style.display = 'block';
    }

    saveConfig(e) {
        e.preventDefault();

        const scenarioSelect = document.getElementById('quick-scenario-select');
        const runsInput = document.getElementById('quick-runs-input');
        const semanticEvalCheckbox = document.getElementById('quick-semantic-eval');
        const templateSelect = document.getElementById('quick-template-select');
        const executionModeSelect = document.getElementById('quick-execution-mode-select');
        const userProfileSelect = document.getElementById('quick-user-profile-select');

        // Enhanced fields
        const detailedLoggingCheckbox = document.getElementById('quick-detailed-logging');
        const saveArtifactsCheckbox = document.getElementById('quick-save-artifacts');
        const timeoutInput = document.getElementById('quick-timeout-input');
        const retryAttemptsInput = document.getElementById('quick-retry-attempts');

        if (!scenarioSelect || !scenarioSelect.value) {
            alert('Please select a scenario');
            return;
        }

        if (!templateSelect || !templateSelect.value) {
            alert('Please select an evaluation template');
            return;
        }

        if (!userProfileSelect || !userProfileSelect.value) {
            alert('Please select a user profile');
            return;
        }

        const selectedScenario = scenarioSelect.options[scenarioSelect.selectedIndex];
        const selectedTemplate = templateSelect.options[templateSelect.selectedIndex];
        const selectedProfile = userProfileSelect.options[userProfileSelect.selectedIndex];

        const config = {
            scenarioId: scenarioSelect.value,
            scenarioName: selectedScenario.dataset.name || selectedScenario.textContent,
            runs: parseInt(runsInput?.value) || 1,
            semanticEval: semanticEvalCheckbox?.checked !== false,
            templateId: templateSelect.value,
            templateName: selectedTemplate.dataset.name,
            templateDescription: selectedTemplate.dataset.description,
            templateCriteria: this.parseJsonSafely(selectedTemplate.dataset.criteria, {}),
            executionMode: executionModeSelect?.value || 'mock',
            userProfileId: selectedProfile.value,
            userProfileName: selectedProfile.textContent,
            // Enhanced configuration options
            detailedLogging: detailedLoggingCheckbox?.checked || false,
            saveArtifacts: saveArtifactsCheckbox?.checked || false,
            timeout: parseInt(timeoutInput?.value) || 300,
            retryAttempts: parseInt(retryAttemptsInput?.value) || 1
        };

        // Save to localStorage
        localStorage.setItem(this.config.storageKey, JSON.stringify(config));

        // Update button state
        this.updateButtonState(config);

        // Hide modal
        this.hideConfig();

        this.config.showSuccessMessage('Quick test configuration saved!');
    }

    // Default message functions
    defaultShowSuccess(message) {
        console.log('QuickTest: Success:', message); // Added log
    }

    defaultShowError(message) {
        console.error('QuickTest: Error:', message); // Added log
    }

    handleTestError(error) {
        const errorMessage = error.message || 'Unknown error';

        // Check if this is an execution mode related error
        const isExecutionModeError = errorMessage.includes('Real workflow mode failed') ||
                                   errorMessage.includes('real mode') ||
                                   errorMessage.includes('execution failed');

        if (isExecutionModeError) {
            // Get current execution mode
            const executionModeSelect = document.getElementById('quick-execution-mode-select');
            const currentMode = executionModeSelect?.value || 'unknown';

            // Create enhanced error message with guidance
            let enhancedMessage = `Execution Mode Error: ${errorMessage}\n\n`;
            enhancedMessage += `Selected Mode: ${this.getExecutionModeDisplayName(currentMode)}\n\n`;
            enhancedMessage += `The real workflow execution failed. This usually means:\n`;
            enhancedMessage += `• The real workflow implementation is not fully ready\n`;
            enhancedMessage += `• There's a configuration issue with external services\n`;
            enhancedMessage += `• API keys or authentication are missing\n\n`;
            enhancedMessage += `Suggested Actions:\n`;
            enhancedMessage += `• Try using "Mock Mode" for testing purposes\n`;
            enhancedMessage += `• If you need real functionality, try "Real Tools Only" mode first\n`;
            enhancedMessage += `• Check the browser console for more detailed error information\n`;
            enhancedMessage += `• Contact support if the issue persists`;

            this.config.showErrorMessage(enhancedMessage);

            // Optionally switch to mock mode automatically
            if (confirm('Would you like to switch to Mock Mode and try again?')) {
                if (executionModeSelect) {
                    executionModeSelect.value = 'mock';
                }
            }
        } else {
            // Standard error handling
            this.config.showErrorMessage(`Quick test failed: ${errorMessage}`);
        }
    }

    // Keep the getExecutionModeDisplayName from quick_test.js for test error handling
    getExecutionModeDisplayName(mode) {
        const modeNames = {
            'mock': '🎭 Mock Mode (Safe - No costs)',
            'real-tools': '🛠️ Real Tools Only',
            'real-llm': '🧠 Real LLM Only',
            'real-db': '🗄️ Real Database Only',
            'partial-real': '⚡ Partial Real (Tools + DB)',
            'full-real': '🚀 Full Real Mode (All components)'
        };
        return modeNames[mode] || mode;
    }

    async runTest() {
        const config = localStorage.getItem(this.config.storageKey);
        if (!config) {
            alert('Please configure the quick test first');
            return;
        }

        let parsedConfig;
        try {
            parsedConfig = JSON.parse(config);
        } catch (e) {
            alert('Invalid configuration. Please reconfigure the quick test.');
            return;
        }

        const statusDiv = document.getElementById('quick-test-status');
        const resultsDiv = document.getElementById('quick-test-results');
        const statusText = document.getElementById('quick-test-status-text');
        const progressBar = document.getElementById('quick-test-progress');
        const detailsText = document.getElementById('quick-test-details');
        const quickTestBtn = document.getElementById('quick-test-btn');

        if (!statusDiv || !statusText || !progressBar) {
            this.config.showErrorMessage('Quick test UI elements not found');
            return;
        }

        // Show status, hide results
        statusDiv.classList.remove('hidden');
        if (resultsDiv) resultsDiv.classList.add('hidden');
        if (quickTestBtn) quickTestBtn.disabled = true;

        try {
            statusText.textContent = 'Preparing test...';
            progressBar.style.width = '10%';
            progressBar.style.backgroundColor = '#2196f3';
            if (detailsText) detailsText.textContent = `Running ${parsedConfig.scenarioName} with ${parsedConfig.runs} run(s)`;

            // Get execution mode parameters
            const executionModeParams = this.getExecutionModeParamsFromConfig(parsedConfig);

            // Prepare test parameters using the selected template
            const testParams = {
                scenario_id: parseInt(parsedConfig.scenarioId),
                evaluation_template_id: parseInt(parsedConfig.templateId),
                params: {
                    runs: parsedConfig.runs,
                    semantic_evaluation: parsedConfig.semanticEval,
                    context_variables: {
                        trust_level: 35 // Default trust level for quick tests
                    },
                    // Add execution mode parameters
                    ...executionModeParams,
                    // Add user profile - user_profile_id is now mandatory
                    user_profile_id: parsedConfig.userProfileId
                }
            };

            statusText.textContent = 'Submitting test request...';
            progressBar.style.width = '30%';

            // Launch the benchmark test
            const response = await fetch(this.config.benchmarkRunApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(testParams)
            });

            let result;
            try {
                result = await response.json();
            } catch (parseError) {
                throw new Error(`Failed to parse server response: ${parseError.message}`);
            }

            if (!response.ok) {
                throw new Error(result.error || `HTTP error ${response.status}`);
            }

            if (!result.success) {
                throw new Error(result.error || 'Test failed to start');
            }

            this.currentTaskId = result.task_id;
            statusText.textContent = 'Test running...';
            progressBar.style.width = '60%';
            if (detailsText) detailsText.textContent = `Task ID: ${result.task_id}`;

            // Poll for results
            await this.pollResults(result.task_id, statusText, progressBar, detailsText);

        } catch (error) {
            console.error('QuickTest: Error running quick test:', error);
            statusText.textContent = `Error: ${error.message}`;
            progressBar.style.width = '100%';
            progressBar.style.backgroundColor = '#dc3545';
            if (detailsText) detailsText.textContent = 'Test failed';

            // Enhanced error handling for execution mode issues
            this.handleTestError(error);
        } finally {
            if (quickTestBtn) quickTestBtn.disabled = false;
            this.currentTaskId = null;
        }
    }

    async pollResults(taskId, statusText, progressBar, detailsText) {
        const maxAttempts = 60; // 5 minutes max
        let attempts = 0;

        const poll = async () => {
            attempts++;

            try {
                const response = await fetch(`/admin/benchmarks/api/task/${taskId}/status/`);

                let result;
                try {
                    result = await response.json();
                } catch (parseError) {
                    throw new Error(`Failed to parse server response: ${parseError.message}`);
                }

                if (!response.ok) {
                    throw new Error(result.error || `HTTP error ${response.status}`);
                }

                // The API returns 'status' field, not 'state'
                const taskStatus = result.status || 'pending';

                if (taskStatus === 'completed') {
                    statusText.textContent = 'Test completed!';
                    progressBar.style.width = '100%';
                    progressBar.style.backgroundColor = '#28a745';
                    if (detailsText) detailsText.textContent = 'Processing results...';

                    // Show results - the result data is in the 'result' field
                    this.displayResults(result.result || result);

                    // Hide status after a moment
                    setTimeout(() => {
                        const statusDiv = document.getElementById('quick-test-status');
                        if (statusDiv) statusDiv.classList.add('hidden');
                    }, 2000);

                } else if (taskStatus === 'failed') {
                    const errorMessage = result.error || 'Test failed';
                    // Handle execution mode errors during polling
                    this.handleTestError(new Error(errorMessage));
                    throw new Error(errorMessage);
                } else if (taskStatus === 'pending' || taskStatus === 'started' || taskStatus === 'progress') {
                    // Update progress
                    const progress = Math.min(60 + (attempts * 2), 95);
                    progressBar.style.width = `${progress}%`;
                    statusText.textContent = 'Test in progress...';

                    if (attempts < maxAttempts) {
                        setTimeout(poll, 5000); // Poll every 5 seconds
                    } else {
                        throw new Error('Test timeout - taking too long to complete');
                    }
                } else {
                    // Unknown state, continue polling
                    if (attempts < maxAttempts) {
                        setTimeout(poll, 5000);
                    } else {
                        throw new Error('Test timeout');
                    }
                }
            } catch (error) {
                console.error('QuickTest: Error polling task status:', error); // Added log
                throw error;
            }
        };

        // Start polling
        setTimeout(poll, 2000); // Wait 2 seconds before first poll
    }

    displayResults(result) {
        const resultsDiv = document.getElementById('quick-test-results');
        const resultsContent = document.getElementById('quick-test-results-content');

        if (!resultsDiv || !resultsContent) return;

        if (!result || !result.runs || result.runs.length === 0) {
            resultsContent.innerHTML = '<p class="text-muted">No results available</p>';
            resultsDiv.classList.remove('hidden');
            return;
        }

        const run = result.runs[0]; // Get first run
        const semanticScore = run.semantic_score || 0;
        const executionTime = run.execution_time || 0;
        const tokenUsage = run.token_usage || 'N/A';
        const cost = run.cost || 0;

        resultsContent.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 15px;">
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 24px; font-weight: bold; color: ${semanticScore >= 0.7 ? '#28a745' : semanticScore >= 0.5 ? '#ffc107' : '#dc3545'};">
                        ${(semanticScore * 100).toFixed(1)}%
                    </div>
                    <div style="font-size: 12px; color: #666;">Semantic Score</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 18px; font-weight: bold; color: #333;">
                        ${(executionTime * 1000).toFixed(0)}ms
                    </div>
                    <div style="font-size: 12px; color: #666;">Execution Time</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 16px; font-weight: bold; color: #333;">
                        ${tokenUsage}
                    </div>
                    <div style="font-size: 12px; color: #666;">Token Usage</div>
                </div>
                <div style="text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <div style="font-size: 16px; font-weight: bold; color: #333;">
                        $${cost.toFixed(4)}
                    </div>
                    <div style="font-size: 12px; color: #666;">Estimated Cost</div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <a href="/admin/benchmarks/history/" class="btn btn-secondary" target="_blank">
                    <i class="fas fa-external-link-alt"></i> View in History
                </a>
                <button onclick="window.quickTestInstance?.runTest()" class="btn btn-primary" style="margin-left: 10px;">
                    <i class="fas fa-redo"></i> Run Again
                </button>
            </div>
        `;

        resultsDiv.classList.remove('hidden');

        // Show success message
        this.config.showSuccessMessage(`Quick test completed! Semantic score: ${(semanticScore * 100).toFixed(1)}%`);
    }

    // Get CSRF token from cookies
    getCsrfToken() {
        const name = 'csrftoken';
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to safely parse JSON
    parseJsonSafely(jsonString, defaultValue) {
        try {
            const parsed = JSON.parse(jsonString);
            // If parsed is null and we expect an object, return defaultValue
            if (parsed === null && typeof defaultValue === 'object' && defaultValue !== null) {
                return defaultValue;
            }
            return parsed;
        } catch (e) {
            console.warn('QuickTest: Failed to parse JSON string safely:', jsonString, e); // Added log
            return defaultValue;
        }
    }

    // --- Methods from QuickTestModalEnhancements ---

    setupTabNavigation() {
        console.log('QuickTest: Setting up tab navigation...');

        const tabButtons = this.modal.querySelectorAll('.tab-button');
        const tabContents = this.modal.querySelectorAll('.tab-content');

        console.log(`QuickTest: Found ${tabButtons.length} tab buttons and ${tabContents.length} tab contents`);

        if (tabButtons.length === 0) {
            console.warn('QuickTest: No tab buttons found in modal');
            return;
        }

        if (tabContents.length === 0) {
            console.warn('QuickTest: No tab contents found in modal');
            return;
        }

        tabButtons.forEach((button, index) => {
            const tabName = button.getAttribute('data-tab');
            console.log(`QuickTest: Setting up tab button ${index}: ${tabName}`);

            button.addEventListener('click', (e) => {
                e.preventDefault();
                console.log(`QuickTest: Tab button clicked: ${tabName}`);
                this.switchTab(tabName, tabButtons, tabContents);
            });
        });

        // Ensure the first tab is active by default
        if (tabButtons.length > 0 && tabContents.length > 0) {
            const firstTab = tabButtons[0].getAttribute('data-tab');
            console.log(`QuickTest: Setting default active tab: ${firstTab}`);
            this.switchTab(firstTab, tabButtons, tabContents);
        }
    }

    switchTab(targetTab, tabButtons, tabContents) {
        console.log(`QuickTest: Switching to tab: ${targetTab}`);

        // Remove active class from all buttons and contents
        tabButtons.forEach(btn => {
            btn.classList.remove('active');
            console.log(`QuickTest: Removed active from button: ${btn.getAttribute('data-tab')}`);
        });

        tabContents.forEach(content => {
            content.classList.remove('active');
            console.log(`QuickTest: Removed active from content: ${content.id}`);
        });

        // Add active class to clicked button and corresponding content
        const activeButton = this.modal.querySelector(`[data-tab="${targetTab}"]`);
        const activeContent = this.modal.querySelector(`#${targetTab}-tab`);

        console.log(`QuickTest: Active button found: ${!!activeButton}`);
        console.log(`QuickTest: Active content found: ${!!activeContent}`);

        if (activeButton && activeContent) {
            activeButton.classList.add('active');
            activeContent.classList.add('active');

            // Force display with JavaScript - override any CSS
            activeContent.style.setProperty('display', 'block', 'important');
            activeContent.style.setProperty('visibility', 'visible', 'important');
            activeContent.style.setProperty('opacity', '1', 'important');

            this.currentTab = targetTab;

            // Debug: Check if classes were actually added
            console.log(`QuickTest: Button has active class: ${activeButton.classList.contains('active')}`);
            console.log(`QuickTest: Content has active class: ${activeContent.classList.contains('active')}`);
            console.log(`QuickTest: Content computed display: ${window.getComputedStyle(activeContent).display}`);
            console.log(`QuickTest: Content computed visibility: ${window.getComputedStyle(activeContent).visibility}`);
            console.log(`QuickTest: Content inline display: ${activeContent.style.display}`);

            console.log(`QuickTest: Successfully switched to tab: ${targetTab}`);

            // Update preview if switching to preview tab
            if (targetTab === 'preview') {
                this.updateConfigPreview();
            }
        } else {
            console.error(`QuickTest: Failed to switch to tab ${targetTab} - button: ${!!activeButton}, content: ${!!activeContent}`);
        }
    }

    setupCollapsibleSections() {
        console.log('QuickTest: Setting up collapsible sections...');
        const collapsibleSections = this.modal.querySelectorAll('.form-section.collapsible');
        console.log(`QuickTest: Found ${collapsibleSections.length} collapsible sections`);

        collapsibleSections.forEach((section, index) => {
            const header = section.querySelector('.section-header');
            if (header) {
                console.log(`QuickTest: Setting up collapsible section ${index}`);

                // Remove any existing event listeners to prevent duplicates
                const newHeader = header.cloneNode(true);
                header.parentNode.replaceChild(newHeader, header);

                // Add single event listener with proper handling
                newHeader.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    console.log(`QuickTest: Toggling section ${index}`);

                    // Toggle collapsed class
                    section.classList.toggle('collapsed');
                    console.log(`QuickTest: Section ${index} collapsed: ${section.classList.contains('collapsed')}`);

                    // Call toggle section method
                    this.toggleSection(section);
                }, { once: false });

                // Mark as having listener for debugging
                newHeader._hasClickListener = true;
                console.log(`QuickTest: Added click listener to section ${index}`); // Added log
            }
        });
    }

    toggleSection(section) {
        const isCurrentlyCollapsed = section.classList.contains('collapsed');
        console.log(`QuickTest: Section currently collapsed: ${isCurrentlyCollapsed}`); // Added log

        // Use a flag to prevent rapid toggling
        if (section.dataset.toggling === 'true') {
            console.log('QuickTest: Section is already toggling, ignoring...'); // Added log
            return;
        }

        // Check if section is in an active tab
        const parentTab = section.closest('.tab-content');
        const isInActiveTab = !parentTab || parentTab.classList.contains('active');
        console.log(`QuickTest: Section is in active tab: ${isInActiveTab}`); // Added log

        if (!isInActiveTab) {
            console.log('QuickTest: Section is not in active tab, ignoring toggle'); // Added log
            return;
        }

        section.dataset.toggling = 'true';

        // Toggle the collapsed state
        if (isCurrentlyCollapsed) {
            section.classList.remove('collapsed');
            console.log('QuickTest: Expanding section'); // Added log
        } else {
            section.classList.add('collapsed');
            console.log('QuickTest: Collapsing section'); // Added log
        }

        // Animate the toggle icon
        const icon = section.querySelector('.toggle-icon');
        if (icon) {
            icon.style.transform = section.classList.contains('collapsed')
                ? 'rotate(-90deg)'
                : 'rotate(0deg)';
        }

        // Update section content visibility with high specificity
        const content = section.querySelector('.section-content');
        if (content) {
            // Get computed style to debug what's happening
            const computedStyle = window.getComputedStyle(content);
            console.log(`QuickTest: Content computed display before change: ${computedStyle.display}`); // Added log
            console.log(`QuickTest: Parent tab active: ${parentTab ? parentTab.classList.contains('active') : 'no parent tab'}`); // Added log

            if (section.classList.contains('collapsed')) {
                // Use multiple methods to ensure hiding works
                content.style.setProperty('display', 'none', 'important');
                content.style.setProperty('visibility', 'hidden', 'important');
                content.style.setProperty('opacity', '0', 'important');
                content.style.setProperty('max-height', '0', 'important');
                content.style.setProperty('overflow', 'hidden', 'important');
                console.log('QuickTest: Applied collapsed styles'); // Added log
            } else {
                // Use multiple methods to ensure showing works
                content.style.setProperty('display', 'block', 'important');
                content.style.setProperty('visibility', 'visible', 'important');
                content.style.setProperty('opacity', '1', 'important');
                content.style.setProperty('max-height', 'none', 'important');
                content.style.setProperty('overflow', 'visible', 'important');
                console.log('QuickTest: Applied expanded styles'); // Added log
            }

            // Check computed style after change
            setTimeout(() => {
                const newComputedStyle = window.getComputedStyle(content);
                console.log(`QuickTest: Content computed display after change: ${newComputedStyle.display}`); // Added log
                console.log(`QuickTest: Content computed visibility after change: ${newComputedStyle.visibility}`); // Added log
                console.log(`QuickTest: Content computed max-height after change: ${newComputedStyle.maxHeight}`); // Added log

                // Additional debugging: check if content is actually visible
                const rect = content.getBoundingClientRect();
                console.log(`QuickTest: Content bounding rect:`, { // Added log
                    width: rect.width,
                    height: rect.height,
                    top: rect.top,
                    left: rect.left,
                    visible: rect.width > 0 && rect.height > 0
                });
            }, 50);
        }

        // Reset the toggling flag after a short delay
        setTimeout(() => {
            section.dataset.toggling = 'false';
        }, 300);
    }

    setupFieldValidation() {
        // Scenario validation
        const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
        if (scenarioSelect) {
            scenarioSelect.addEventListener('change', () => {
                this.validateField('scenario', scenarioSelect.value, 'scenario-status');
            });
        }

        // Template validation
        const templateSelect = this.modal.querySelector('#quick-template-select');
        if (templateSelect) {
            templateSelect.addEventListener('change', () => {
                this.validateField('template', templateSelect.value, 'template-status');
                if (templateSelect.value) {
                    // Call the correct updateTemplatePreview from QuickTest class
                    this.updateTemplatePreview(templateSelect.value);
                } else {
                     // Hide preview if no template is selected
                    const templatePreview = document.getElementById('quick-template-preview');
                    if (templatePreview) {
                        templatePreview.style.display = 'none';
                    }
                }
            });
        }

        // Profile validation
        const profileSelect = this.modal.querySelector('#quick-user-profile-select');
        if (profileSelect) {
            profileSelect.addEventListener('change', () => {
                this.validateField('profile', profileSelect.value, 'profile-status');
            });
        }

        // Execution mode info
        const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
        if (executionModeSelect) {
            executionModeSelect.addEventListener('change', () => {
                this.updateExecutionModeInfo(executionModeSelect.value);
            });
        }
    }

    validateField(fieldName, value, statusElementId) {
        const statusElement = this.modal.querySelector(`#${statusElementId}`);
        if (!statusElement) return;

        let isValid = false;
        let message = '';

        switch (fieldName) {
            case 'scenario':
                isValid = value && value.trim() !== '';
                message = isValid ? 'Scenario selected' : 'Please select a scenario';
                break;
            case 'template':
                isValid = value && value.trim() !== '';
                message = isValid ? 'Template selected' : 'Please select an evaluation template';
                break;
            case 'profile':
                isValid = value && value.trim() !== '';
                message = isValid ? 'Profile selected' : 'Please select a user profile';
                break;
        }

        this.validationState[fieldName] = isValid;
        this.updateFieldStatus(statusElement, isValid, message);
        this.updateConfigPreview();
    }

    updateFieldStatus(statusElement, isValid, message) {
        statusElement.className = `field-status ${isValid ? 'success' : 'error'}`;
        statusElement.textContent = message;
    }

    updateExecutionModeInfo(mode) {
        const infoElement = this.modal.querySelector('#execution-mode-info');
        if (!infoElement) return;

        const modeInfo = this.getExecutionModeInfoEnhancements(mode); // Use enhancements version

        if (modeInfo.showInfo) {
            infoElement.innerHTML = `
                <div class="mode-details">
                    <strong>${modeInfo.title}</strong>
                    <p>${modeInfo.description}</p>
                    ${modeInfo.warnings ? `<div class="warnings">${modeInfo.warnings}</div>` : ''}
                </div>
            `;
            infoElement.className = `execution-mode-info ${modeInfo.level}`;
            infoElement.style.display = 'block';
        } else {
            infoElement.style.display = 'none';
        }
    }

    // Keep the getExecutionModeInfo from quick_test_modal_enhancements.js
    getExecutionModeInfoEnhancements(mode) {
        const modeInfoMap = {
            'mock': {
                showInfo: false
            },
            'real-tools': {
                showInfo: true,
                title: 'Real Tools Mode',
                description: 'Uses actual tool implementations but mocked LLM and database.',
                level: 'warning',
                warnings: 'Tool calls will execute real operations.'
            },
            'real-llm': {
                showInfo: true,
                title: 'Real LLM Mode',
                description: 'Uses actual LLM API calls but mocked tools and database.',
                level: 'warning',
                warnings: 'This will consume LLM API credits and incur costs.'
            },
            'real-db': {
                showInfo: true,
                title: 'Real Database Mode',
                description: 'Uses actual database operations but mocked LLM and tools.',
                level: 'warning',
                warnings: 'Database operations will affect real data.'
            },
            'partial-real': {
                showInfo: true,
                title: 'Partial Real Mode',
                description: 'Uses real tools and database but mocked LLM.',
                level: 'warning',
                warnings: 'Tool calls and database operations will execute real operations.'
            },
            'full-real': {
                showInfo: true,
                title: 'Full Real Mode',
                description: 'Uses all real components: LLM, tools, and database.',
                level: 'danger',
                warnings: 'This will consume API credits, execute real operations, and incur costs.'
            }
        };

        return modeInfoMap[mode] || { showInfo: false };
    }


    setupPreviewFeatures() {
        // Validate configuration button
        const validateBtn = this.modal.querySelector('#validate-config-btn');
        if (validateBtn) {
            validateBtn.addEventListener('click', () => {
                this.validateConfiguration();
            });
        }

        // Test connection button
        const testConnectionBtn = this.modal.querySelector('#test-connection-btn');
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', () => {
                this.testConnection();
            });
        }

        // Reset configuration button
        const resetBtn = this.modal.querySelector('#reset-config-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetConfiguration();
            });
        }

        // Export configuration button
        const exportBtn = this.modal.querySelector('#export-config-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportConfiguration();
            });
        }

        // Import configuration button
        const importBtn = this.modal.querySelector('#import-config-btn');
        if (importBtn) {
            importBtn.addEventListener('click', () => {
                this.importConfiguration();
            });
        }

        // Set up health check monitoring
        this.setupHealthCheckMonitoring();
    }

    setupEnhancedEventListeners() {
        // Listen for form changes to update preview
        const form = this.modal.querySelector('#quick-test-config-form');
        if (form) {
            form.addEventListener('change', () => {
                if (this.currentTab === 'preview') {
                    this.updateConfigPreview();
                }
            });
        }

         // Close modal when clicking outside
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hideConfig(); // Call hideConfig from QuickTest
            }
        });

        // Close modal with X button
        const closeBtn = this.modal.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideConfig()); // Call hideConfig from QuickTest
        }
    }

    updateConfigPreview() {
        const summaryElement = this.modal.querySelector('#config-summary');
        if (!summaryElement) return;

        const config = this.getCurrentConfiguration();
        const isValid = this.isConfigurationValid();

        let html = `
            <div class="config-summary-grid">
                <div class="summary-section">
                    <h6>📋 Test Configuration</h6>
                    <div class="summary-item">
                        <span class="label">Scenario:</span>
                        <span class="value ${config.scenario ? 'valid' : 'invalid'}">${config.scenario || 'Not selected'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Runs:</span>
                        <span class="value">${config.runs}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Semantic Evaluation:</span>
                        <span class="value">${config.semanticEval ? 'Enabled' : 'Disabled'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>🔧 Execution Settings</h6>
                    <div class="summary-item">
                        <span class="label">Mode:</span>
                        <span class="value">${this.getExecutionModeDisplayNameEnhancements(config.executionMode)}</span> // Use enhancements version
                    </div>
                    <div class="summary-item">
                        <span class="label">User Profile:</span>
                        <span class="value">${config.userProfile || 'None selected'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>📊 Evaluation</h6>
                    <div class="summary-item">
                        <span class="label">Template:</span>
                        <span class="value ${config.template ? 'valid' : 'invalid'}">${config.template || 'Not selected'}</span>
                    </div>
                </div>

                <div class="summary-section">
                    <h6>🚀 Advanced Options</h6>
                    <div class="summary-item">
                        <span class="label">Detailed Logging:</span>
                        <span class="value">${config.detailedLogging ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Save Artifacts:</span>
                        <span class="value">${config.saveArtifacts ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Timeout:</span>
                        <span class="value">${config.timeout}s</span>
                    </div>
                    <div class="summary-item">
                        <span class="label">Retry Attempts:</span>
                        <span class="value">${config.retryAttempts}</span>
                    </div>
                </div>
            </div>

            <div class="config-status ${isValid ? 'valid' : 'invalid'}">
                <strong>Configuration Status:</strong>
                ${isValid ? '✅ Ready to save' : '❌ Missing required fields'}
            </div>
        `;

        summaryElement.innerHTML = html;
    }

    getCurrentConfiguration() {
        const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
        const runsInput = this.modal.querySelector('#quick-runs-input');
        const semanticEvalCheckbox = this.modal.querySelector('#quick-semantic-eval');
        const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
        const userProfileSelect = this.modal.querySelector('#quick-user-profile-select');
        const templateSelect = this.modal.querySelector('#quick-template-select');
        const detailedLoggingCheckbox = this.modal.querySelector('#quick-detailed-logging');
        const saveArtifactsCheckbox = this.modal.querySelector('#quick-save-artifacts');
        const timeoutInput = this.modal.querySelector('#quick-timeout-input');
        const retryAttemptsInput = this.modal.querySelector('#quick-retry-attempts');

        return {
            scenario: scenarioSelect?.selectedOptions[0]?.textContent || '',
            runs: runsInput?.value || 1,
            semanticEval: semanticEvalCheckbox?.checked || false,
            executionMode: executionModeSelect?.value || 'mock',
            userProfile: userProfileSelect?.selectedOptions[0]?.textContent || '',
            template: templateSelect?.selectedOptions[0]?.textContent || '',
            detailedLogging: detailedLoggingCheckbox?.checked || false,
            saveArtifacts: saveArtifactsCheckbox?.checked || false,
            timeout: parseInt(timeoutInput?.value) || 300,
            retryAttempts: parseInt(retryAttemptsInput?.value) || 1
        };
    }

    isConfigurationValid() {
        return this.validationState.scenario && this.validationState.template && this.validationState.profile;
    }

    // Keep the getExecutionModeDisplayName from quick_test_modal_enhancements.js for preview
    getExecutionModeDisplayNameEnhancements(mode) {
        const modeNames = {
            'mock': '🎭 Mock Mode',
            'real-tools': '🛠️ Real Tools Only',
            'real-llm': '🧠 Real LLM Only',
            'real-db': '🗄️ Real Database Only',
            'partial-real': '⚡ Partial Real',
            'full-real': '🚀 Full Real Mode'
        };
        return modeNames[mode] || mode;
    }

    validateConfiguration() {
        const resultsElement = this.modal.querySelector('#validation-results');
        if (!resultsElement) return;

        resultsElement.style.display = 'block';
        resultsElement.innerHTML = '<div class="loading-spinner">Validating configuration...</div>';

        // Simulate validation process
        setTimeout(() => {
            const isValid = this.isConfigurationValid();
            const config = this.getCurrentConfiguration();

            let html = `
                <h6>Validation Results</h6>
                <div class="validation-items">
            `;

            // Check each required field
            html += this.getValidationItem('Scenario Selection', this.validationState.scenario, 'A scenario must be selected');
            html += this.getValidationItem('Template Selection', this.validationState.template, 'An evaluation template must be selected');
            html += this.getValidationItem('User Profile Selection', this.validationState.profile, 'A user profile must be selected');
            html += this.getValidationItem('Runs Configuration', config.runs >= 1 && config.runs <= 5, 'Number of runs must be between 1 and 5');
            html += this.getValidationItem('Timeout Configuration', config.timeout >= 60 && config.timeout <= 1800, 'Timeout must be between 60 and 1800 seconds');

            html += '</div>';

            if (isValid) {
                html += '<div class="validation-summary success">✅ Configuration is valid and ready to save!</div>';
                resultsElement.className = 'validation-results success';
            } else {
                html += '<div class="validation-summary error">❌ Please fix the issues above before saving.</div>';
                resultsElement.className = 'validation-results error';
            }

            resultsElement.innerHTML = html;
        }, 1000);
    }

    getValidationItem(label, isValid, message) {
        const icon = isValid ? '✅' : '❌';
        const status = isValid ? 'valid' : 'invalid';
        return `
            <div class="validation-item ${status}">
                <span class="validation-icon">${icon}</span>
                <span class="validation-label">${label}</span>
                ${!isValid ? `<span class="validation-message">${message}</span>` : ''}
            </div>
        `;
    }

    testConnection() {
        const resultsElement = this.modal.querySelector('#validation-results');
        if (!resultsElement) return;

        resultsElement.style.display = 'block';
        resultsElement.className = 'validation-results';
        resultsElement.innerHTML = '<div class="loading-spinner">Testing connection...</div>';

        // Simulate connection test
        setTimeout(() => {
            const html = `
                <h6>Connection Test Results</h6>
                <div class="connection-tests">
                    <div class="test-item success">
                        <span class="test-icon">✅</span>
                        <span class="test-label">API Endpoint</span>
                        <span class="test-status">Connected</span>
                    </div>
                    <div class="test-item success">
                        <span class="test-icon">✅</span>
                        <span class="test-label">Database</span>
                        <span class="test-status">Available</span>
                    </div>
                    <div class="test-item warning">
                        <span class="test-icon">⚠️</span>
                        <span class="test-label">LLM Service</span>
                        <span class="test-status">Limited quota</span>
                    </div>
                </div>
                <div class="test-summary success">🟢 System is ready for testing</div>
            `;

            resultsElement.className = 'validation-results success';
            resultsElement.innerHTML = html;
        }, 1500);
    }

    resetConfiguration() {
        if (confirm('Are you sure you want to reset all configuration to defaults?')) {
            // Reset all form fields to defaults
            const form = this.modal.querySelector('#quick-test-config-form');
            if (form) {
                form.reset();

                // Reset specific fields to their default values
                const runsInput = this.modal.querySelector('#quick-runs-input');
                if (runsInput) runsInput.value = 1;

                const semanticEvalCheckbox = this.modal.querySelector('#quick-semantic-eval');
                if (semanticEvalCheckbox) semanticEvalCheckbox.checked = true;

                const executionModeSelect = this.modal.querySelector('#quick-execution-mode-select');
                if (executionModeSelect) executionModeSelect.value = 'mock';

                const timeoutInput = this.modal.querySelector('#quick-timeout-input');
                if (timeoutInput) timeoutInput.value = 300;

                const retryAttemptsInput = this.modal.querySelector('#quick-retry-attempts');
                if (retryAttemptsInput) retryAttemptsInput.value = 1;
            }

            // Reset validation state
            this.validationState = {
                scenario: false,
                template: false,
                profile: false
            };

            // Clear status indicators
            const statusElements = this.modal.querySelectorAll('.field-status');
            statusElements.forEach(element => {
                element.style.display = 'none';
            });

            // Hide execution mode info
            const executionModeInfo = this.modal.querySelector('#execution-mode-info');
            if (executionModeInfo) {
                executionModeInfo.style.display = 'none';
            }

            // Hide template preview
            const templatePreview = this.modal.querySelector('#quick-template-preview');
            if (templatePreview) {
                templatePreview.style.display = 'none';
            }

            // Update preview
            this.updateConfigPreview();

            // Switch back to basic tab
            const basicTabButton = this.modal.querySelector('[data-tab="basic"]');
            if (basicTabButton) {
                basicTabButton.click();
            }
        }
    }

    setupKeyboardNavigation() {
        // Set up keyboard shortcuts
        this.keyboardShortcuts.set('Escape', () => this.hideConfig()); // Use hideConfig
        this.keyboardShortcuts.set('F1', () => this.showHelp());
        this.keyboardShortcuts.set('Control+Enter', () => this.saveConfiguration());
        this.keyboardShortcuts.set('Control+r', () => this.resetConfiguration());

        document.addEventListener('keydown', (e) => {
            if (!this.modal || this.modal.style.display === 'none') return;

            const key = e.key;
            const modifiers = [];
            if (e.ctrlKey) modifiers.push('Control');
            if (e.shiftKey) modifiers.push('Shift');
            if (e.altKey) modifiers.push('Alt');

            const shortcut = modifiers.length > 0 ? `${modifiers.join('+')}+${key}` : key;

            if (this.keyboardShortcuts.has(shortcut)) {
                e.preventDefault();
                this.keyboardShortcuts.get(shortcut)();
            }

            // Tab navigation between tabs
            if (e.key === 'Tab' && e.ctrlKey) {
                e.preventDefault();
                this.switchToNextTab();
            }
        });

        // Add keyboard navigation hints
        this.addKeyboardHints();
    }

    setupDragFunctionality() {
        const header = this.modal.querySelector('.quick-test-header');
        if (!header) return;

        header.style.cursor = 'move';

        header.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            const rect = this.modal.querySelector('.modal-content').getBoundingClientRect();
            this.dragOffset.x = e.clientX - rect.left;
            this.dragOffset.y = e.clientY - rect.top;

            document.addEventListener('mousemove', this.handleDrag.bind(this));
            document.addEventListener('mouseup', this.handleDragEnd.bind(this));

            e.preventDefault();
        });
    }

    handleDrag(e) {
        if (!this.isDragging) return;

        const modalContent = this.modal.querySelector('.modal-content');
        const newX = e.clientX - this.dragOffset.x;
        const newY = e.clientY - this.dragOffset.y;

        // Constrain to viewport
        const maxX = window.innerWidth - modalContent.offsetWidth;
        const maxY = window.innerHeight - modalContent.offsetHeight;

        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));

        modalContent.style.position = 'fixed';
        modalContent.style.left = `${constrainedX}px`;
        modalContent.style.top = `${constrainedY}px`;
        modalContent.style.margin = '0';
    }

    handleDragEnd() {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.handleDrag.bind(this));
        document.removeEventListener('mouseup', this.handleDragEnd.bind(this));
    }

    setupAutoSave() {
        const form = this.modal.querySelector('#quick-test-config-form');
        if (!form) return;

        form.addEventListener('input', () => {
            clearTimeout(this.autoSaveTimer);
            this.autoSaveTimer = setTimeout(() => {
                this.autoSaveFormState();
            }, 2000); // Auto-save after 2 seconds of inactivity
        });

        // Load saved state on initialization
        this.loadSavedFormState();
    }

    autoSaveFormState() {
        const formData = new FormData(this.modal.querySelector('#quick-test-config-form'));
        const state = {};

        for (let [key, value] of formData.entries()) {
            state[key] = value;
        }

        // Also save checkbox states
        this.modal.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            state[checkbox.id] = checkbox.checked;
        });

        localStorage.setItem('quickTestModalAutoSave', JSON.stringify(state));
        this.showAutoSaveIndicator();
    }

    loadSavedFormState() {
        const saved = localStorage.getItem('quickTestModalAutoSave');
        if (!saved) return;

        try {
            const state = JSON.parse(saved);

            Object.entries(state).forEach(([key, value]) => {
                const element = this.modal.querySelector(`#${key}`);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = value;
                    } else {
                        element.value = value;
                    }
                }
            });
        } catch (e) {
            console.warn('QuickTest: Failed to load auto-saved form state:', e); // Added log
        }
    }

    showAutoSaveIndicator() {
        // Create or update auto-save indicator
        let indicator = this.modal.querySelector('.auto-save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'auto-save-indicator';
            indicator.innerHTML = '<i class="fas fa-check-circle"></i> Auto-saved';
            indicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: 50px;
                background: #28a745;
                color: white;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s;
                z-index: 1001;
            `;
            this.modal.querySelector('.modal-content').appendChild(indicator);
        }

        indicator.style.opacity = '1';
        setTimeout(() => {
            indicator.style.opacity = '0';
        }, 2000);
    }

    setupAccessibilityFeatures() {
        // Add ARIA labels and roles
        if (this.modal) { // Ensure modal exists
            this.modal.setAttribute('role', 'dialog');
            this.modal.setAttribute('aria-labelledby', 'modal-title');
            this.modal.setAttribute('aria-modal', 'true');

            // Add focus management
            const firstFocusable = this.modal.querySelector('input, select, button, textarea');
            if (firstFocusable) {
                firstFocusable.focus();
            }

            // Trap focus within modal
            this.modal.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    this.trapFocus(e);
                }
            });
        }
         // Close modal when clicking outside (moved from bindEvents)
        const configModal = document.getElementById('quick-test-config-modal');
        if (configModal) {
            configModal.addEventListener('click', (e) => {
                if (e.target === configModal) {
                    this.hideConfig();
                }
            });
        }

        // Close modal with X button (moved from bindEvents)
        const closeBtn = configModal ? configModal.querySelector('.close') : null;
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.hideConfig());
        }
    }

    trapFocus(e) {
        const focusableElements = this.modal.querySelectorAll(
            'input, select, button, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }

    // Removed closeModal as hideConfig exists

    showHelp() {
        const helpContent = `
            <div class="help-modal">
                <h4>Quick Test Modal Help</h4>
                <div class="help-section">
                    <h5>Keyboard Shortcuts:</h5>
                    <ul>
                        <li><kbd>Esc</kbd> - Close modal</li>
                        <li><kbd>Ctrl+Enter</kbd> - Save configuration</li>
                        <li><kbd>Ctrl+R</kbd> - Reset configuration</li>
                        <li><kbd>Ctrl+Tab</kbd> - Switch between tabs</li>
                        <li><kbd>F1</kbd> - Show this help</li>
                    </ul>
                </div>
                <div class="help-section">
                    <h5>Features:</h5>
                    <ul>
                        <li>Drag the header to reposition the modal</li>
                        <li>Auto-save functionality saves your progress</li>
                        <li>Collapsible sections to organize content</li>
                        <li>Real-time validation and preview</li>
                    </ul>
                </div>
                <button onclick="this.closest('.help-modal').remove()" class="btn btn-primary">Close Help</button>
            </div>
        `;

        const helpOverlay = document.createElement('div');
        helpOverlay.className = 'help-overlay';
        helpOverlay.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        helpOverlay.innerHTML = helpContent;
        document.body.appendChild(helpOverlay);

        helpOverlay.addEventListener('click', (e) => {
            if (e.target === helpOverlay) {
                helpOverlay.remove();
            }
        });
    }

    switchToNextTab() {
        const tabs = ['basic', 'advanced', 'preview'];
        const currentIndex = tabs.indexOf(this.currentTab);
        const nextIndex = (currentIndex + 1) % tabs.length;
        this.switchTab(tabs[nextIndex], this.modal.querySelectorAll('.tab-button'), this.modal.querySelectorAll('.tab-content')); // Pass elements
    }

    addKeyboardHints() {
        // Add keyboard hints to the modal header
        const header = this.modal.querySelector('.quick-test-header');
        if (header) {
            const hintsElement = document.createElement('div');
            hintsElement.className = 'keyboard-hints';
            hintsElement.innerHTML = `
                <small style="opacity: 0.8; font-size: 11px;">
                    Press <kbd>F1</kbd> for help • <kbd>Esc</kbd> to close • <kbd>Ctrl+Tab</kbd> to switch tabs
                </small>
            `;
            hintsElement.style.cssText = `
                position: absolute;
                bottom: 5px;
                left: 20px;
                font-size: 11px;
                opacity: 0.8;
            `;
            header.appendChild(hintsElement);
        }
    }

    saveConfiguration() {
        // Trigger the form submission
        const form = this.modal.querySelector('#quick-test-config-form');
        if (form) {
            form.dispatchEvent(new Event('submit'));
        }
    }

    exportConfiguration() {
        const config = this.getCurrentConfiguration();
        const dataStr = JSON.stringify(config, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `quick-test-config-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        this.showNotification('Configuration exported successfully!', 'success');
    }

    importConfiguration() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const config = JSON.parse(e.target.result);
                    this.populateConfigForm(config);
                    this.showNotification('Configuration imported successfully!', 'success');
                } catch (error) {
                    console.warn('QuickTest: Failed to import configuration: Invalid JSON', error); // Added log
                    this.showNotification('Failed to import configuration: Invalid JSON', 'error');
                }
            };
            reader.readAsText(file);
        };
        input.click();
    }

    // populateConfigForm already exists in QuickTest, will use that one

    setupHealthCheckMonitoring() {
        // Monitor form changes and update health indicators
        const form = this.modal.querySelector('#quick-test-config-form');
        if (!form) return;

        form.addEventListener('change', () => {
            this.updateHealthIndicators();
        });

        // Initial health check
        setTimeout(() => {
            this.updateHealthIndicators();
        }, 500);
    }

    updateHealthIndicators() {
        const indicators = this.modal.querySelectorAll('.health-item');

        indicators.forEach(indicator => {
            const checkType = indicator.dataset.check;
            const result = this.performHealthCheck(checkType);

            const icon = indicator.querySelector('.health-icon');
            const status = indicator.querySelector('.health-status');

            // Remove existing classes
            indicator.classList.remove('healthy', 'warning', 'error');

            // Apply new status
            indicator.classList.add(result.status);
            icon.textContent = result.icon;
            status.textContent = result.message;
        });
    }

    performHealthCheck(checkType) {
        switch (checkType) {
            case 'scenario':
                const scenarioSelect = this.modal.querySelector('#quick-scenario-select');
                if (scenarioSelect && scenarioSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Scenario selected' };
                }
                return { status: 'error', icon: '❌', message: 'No scenario selected' };

            case 'template':
                const templateSelect = this.modal.querySelector('#quick-template-select');
                if (templateSelect && templateSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Template configured' };
                }
                return { status: 'warning', icon: '⚠️', message: 'Template not selected' };

            case 'profile':
                const profileSelect = this.modal.querySelector('#quick-user-profile-select');
                if (profileSelect && profileSelect.value) {
                    return { status: 'healthy', icon: '✅', message: 'Profile selected' };
                }
                return { status: 'warning', icon: '⚠️', message: 'No profile selected' };

            case 'execution':
                const executionSelect = this.modal.querySelector('#quick-execution-mode-select');
                if (executionSelect && executionSelect.value) {
                    const mode = executionSelect.value;
                    if (mode === 'mock') {
                        return { status: 'healthy', icon: '✅', message: 'Safe mock mode' };
                    } else if (mode.includes('real')) {
                        return { status: 'warning', icon: '⚠️', message: 'Real mode - costs may apply' };
                    }
                }
                return { status: 'error', icon: '❌', message: 'No execution mode selected' };

            default:
                return { status: 'error', icon: '❓', message: 'Unknown check' };
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            ${message}
        `;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 2000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('QuickTest: DOM loaded, initializing QuickTest...'); // Added log
    // Check if QuickTest is already initialized (shouldn't be with this structure, but good practice)
    if (!window.quickTestInstance) {
        const quickTestConfig = {
            scenariosApiUrl: window.BENCHMARK_SCENARIOS_API_URL,
            benchmarkRunApiUrl: '/admin/benchmarks/api/run/',
            taskStatusApiUrl: '/admin/benchmarks/api/task/',  // Correct URL pattern
            templatesApiUrl: '/admin/benchmarks/api/templates/',
            showSuccessMessage: function(message) {
                // Create a simple success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success';
                alertDiv.textContent = message;
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; background: #d4edda; color: #155724; border: 1px solid #c3e6cb; border-radius: 4px;';
                document.body.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            },
            showErrorMessage: function(message) {
                // Create a simple error message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-danger';
                alertDiv.textContent = message;
                alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; padding: 15px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px;';
                document.body.appendChild(alertDiv);
                setTimeout(() => alertDiv.remove(), 5000);
            },
            storageKey: 'quickTestConfig_history' // Use different storage key for history page
        };

        window.quickTestInstance = new QuickTest(quickTestConfig);
        console.log('QuickTest: QuickTest instance created:', window.quickTestInstance); // Added log
    } else {
        console.log('QuickTest: QuickTest instance already exists.'); // Added log
    }
});

// Global function for modal close button
function hideQuickTestModal() {
    const configModal = document.getElementById('quick-test-config-modal');
    if (configModal) {
        configModal.style.display = 'none';
    }
}
