// Shared Modal Utilities for Benchmark History

// Helper function to render semantic evaluation details
function renderSemanticEvaluationDetails(container, data) {
    container.innerHTML = '';

    if (data.semantic_evaluation_details && Object.keys(data.semantic_evaluation_details).length > 0) {
        const primaryDetailsDiv = document.createElement('div');
        primaryDetailsDiv.innerHTML = `
            <h4>Primary Evaluator Details</h4>
            <pre class="json-viewer">${JSON.stringify(data.semantic_evaluation_details, null, 2)}</pre>
        `;
        container.appendChild(primaryDetailsDiv);
    }

    if (data.semantic_evaluations && typeof data.semantic_evaluations === 'object' && Object.keys(data.semantic_evaluations).length > 0) {
        const multiModelDiv = document.createElement('div');
        multiModelDiv.innerHTML = '<h4>Multi-Model Dimensional Evaluation</h4>';

        for (const modelName in data.semantic_evaluations) {
            const evaluation = data.semantic_evaluations[modelName];
            const modelSection = document.createElement('div');
            modelSection.style.marginBottom = '15px';
            modelSection.style.paddingLeft = '10px';
            modelSection.style.borderLeft = '2px solid #eee';

            let dimensionsHtml = '<h5>Dimensions:</h5><ul>';
            if (evaluation && evaluation.dimensions && typeof evaluation.dimensions === 'object') {
                for (const dimName in evaluation.dimensions) {
                    const dimData = evaluation.dimensions[dimName] || {};
                    const score = dimData.score !== null && dimData.score !== undefined ? parseFloat(dimData.score).toFixed(2) : 'N/A';
                    const reasoning = dimData.reasoning ? dimData.reasoning : 'No reasoning provided.';
                    dimensionsHtml += `<li><strong>${dimName}</strong> (Score: ${score}): ${reasoning}</li>`;
                }
            } else {
                dimensionsHtml += '<li>No dimensional data available.</li>';
            }
            dimensionsHtml += '</ul>';

            const overallScore = evaluation && evaluation.overall_score !== null && evaluation.overall_score !== undefined ? parseFloat(evaluation.overall_score).toFixed(2) : 'N/A';
            const overallReasoning = evaluation && evaluation.overall_reasoning ? evaluation.overall_reasoning : 'No overall reasoning provided.';
            const errorStatus = evaluation && evaluation.error ? '<span style="color: red;"> (Error during evaluation)</span>' : '';

            modelSection.innerHTML = `
                <h5 style="margin-bottom: 5px;">Evaluator: ${modelName}${errorStatus}</h5>
                <p><strong>Overall Score:</strong> ${overallScore}</p>
                <p><strong>Overall Reasoning:</strong> ${overallReasoning}</p>
                ${dimensionsHtml}
            `;
            multiModelDiv.appendChild(modelSection);
        }
        container.appendChild(multiModelDiv);
    }
}

// Helper function to render enhanced context package details with ConversationDispatcher v2.0.0 support
function renderContextPackageDetails(container, contextPackage) {
    container.innerHTML = '';

    // Check for enhanced architecture features
    const hasEnhancedArchitecture = contextPackage.system_metadata?.enhanced_architecture || false;
    const dispatcherVersion = contextPackage.system_metadata?.dispatcher_version || '1.0.0';
    const hasMentorContext = contextPackage.mentor_context && Object.keys(contextPackage.mentor_context).length > 0;
    const hasWorkflowMetadata = contextPackage.workflow_metadata && Object.keys(contextPackage.workflow_metadata).length > 0;

    // Create enhanced context explorer
    const contextExplorer = document.createElement('div');
    contextExplorer.className = 'enhanced-context-explorer';
    contextExplorer.innerHTML = `
        <div class="context-explorer-header">
            <h4>📦 Enhanced Context Package Explorer</h4>
            <div class="architecture-info">
                <span class="version-badge ${hasEnhancedArchitecture ? 'enhanced' : 'standard'}">
                    ${hasEnhancedArchitecture ? '🚀' : '📋'} Dispatcher v${dispatcherVersion}
                </span>
                ${hasMentorContext ? '<span class="feature-badge mentor">🧠 MentorService</span>' : ''}
                ${hasWorkflowMetadata ? '<span class="feature-badge workflow">🔄 Workflow Metadata</span>' : ''}
            </div>
        </div>

        <div class="context-tabs-enhanced">
            <div class="tab-buttons-enhanced">
                <button class="tab-button-enhanced active" data-tab="overview">📊 Overview</button>
                ${hasMentorContext ? '<button class="tab-button-enhanced" data-tab="mentor">🧠 Mentor Context</button>' : ''}
                ${hasWorkflowMetadata ? '<button class="tab-button-enhanced" data-tab="workflow">🔄 Workflow Metadata</button>' : ''}
                <button class="tab-button-enhanced" data-tab="system">⚙️ System Metadata</button>
                <button class="tab-button-enhanced" data-tab="raw">📋 Raw Data</button>
            </div>

            <div class="tab-content-enhanced">
                <div id="overview-tab-enhanced" class="tab-pane-enhanced active">
                    ${renderContextOverview(contextPackage)}
                </div>

                ${hasMentorContext ? `
                <div id="mentor-tab-enhanced" class="tab-pane-enhanced">
                    ${renderMentorContextDetails(contextPackage.mentor_context)}
                </div>
                ` : ''}

                ${hasWorkflowMetadata ? `
                <div id="workflow-tab-enhanced" class="tab-pane-enhanced">
                    ${renderWorkflowMetadataDetails(contextPackage.workflow_metadata)}
                </div>
                ` : ''}

                <div id="system-tab-enhanced" class="tab-pane-enhanced">
                    ${renderSystemMetadataDetails(contextPackage.system_metadata || {})}
                </div>

                <div id="raw-tab-enhanced" class="tab-pane-enhanced">
                    <div class="json-viewer">
                        <pre>${JSON.stringify(contextPackage, null, 2)}</pre>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.appendChild(contextExplorer);

    // Add enhanced tab switching functionality
    const tabButtons = container.querySelectorAll('.tab-button-enhanced');
    const tabPanes = container.querySelectorAll('.tab-pane-enhanced');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            const targetPane = container.querySelector(`#${targetTab}-tab-enhanced`);
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

// Helper function to render workflow agent communications
function renderWorkflowAgentCommunications(container, agentComms) {
    container.innerHTML = '';

    // Create summary cards first
    if (agentComms.summary) {
        const summaryContainer = document.createElement('div');
        summaryContainer.className = 'agent-communications-summary';
        summaryContainer.innerHTML = renderAgentCommunicationsSummary(agentComms.summary, agentComms.workflow_id);
        container.appendChild(summaryContainer);
    }

    // Create tabbed interface for agent communications
    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'context-tabs';
    tabsContainer.innerHTML = `
        <div class="tab-buttons">
            <button class="tab-button active" data-tab="overview">Overview</button>
            <button class="tab-button" data-tab="agents">Agent Timeline</button>
            <button class="tab-button" data-tab="transitions">State Transitions</button>
            <button class="tab-button" data-tab="raw">Raw Data</button>
        </div>
        <div class="tab-content">
            <div id="overview-tab" class="tab-pane active">
                <!-- Overview will be rendered here -->
            </div>
            <div id="agents-tab" class="tab-pane">
                <!-- Agent interactions will be rendered here -->
            </div>
            <div id="transitions-tab" class="tab-pane">
                <!-- State transitions will be rendered here -->
            </div>
            <div id="raw-tab" class="tab-pane">
                <!-- Raw data will be rendered here -->
            </div>
        </div>
    `;

    container.appendChild(tabsContainer);

    // Render overview tab
    const overviewTab = container.querySelector('#overview-tab');
    overviewTab.innerHTML = renderAgentCommunicationsOverview(agentComms);

    // Render agent interactions tab with enhanced timeline
    const agentsTab = container.querySelector('#agents-tab');
    if (agentComms.agents && agentComms.agents.length > 0) {
        agentsTab.innerHTML = renderEnhancedAgentTimeline(agentComms.agents);
    } else {
        agentsTab.innerHTML = '<p>No agent interactions recorded.</p>';
    }

    // Render state transitions tab
    const transitionsTab = container.querySelector('#transitions-tab');
    if (agentComms.state_transitions && agentComms.state_transitions.length > 0) {
        transitionsTab.innerHTML = renderStateTransitions(agentComms.state_transitions);
    } else {
        transitionsTab.innerHTML = '<p>No state transitions recorded.</p>';
    }

    // Render raw data tab
    const rawTab = container.querySelector('#raw-tab');
    rawTab.innerHTML = `
        <div class="expandable-section">
            <div class="section-header" onclick="toggleSection(this)">
                <h4>📋 Complete Agent Communications Data</h4>
                <span class="toggle-icon">▼</span>
            </div>
            <div class="section-content">
                <div class="json-viewer">
                    <pre>${JSON.stringify(agentComms, null, 2)}</pre>
                </div>
            </div>
        </div>
    `;

    // Add tab switching functionality
    const tabButtons = container.querySelectorAll('.tab-button');
    const tabPanes = container.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            const targetPane = container.querySelector(`#${targetTab}-tab`);
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

// Helper functions for agent communications rendering
function renderAgentCommunicationsSummary(summary, workflowId) {
    return `
        <div class="summary-cards-grid">
            <div class="summary-card workflow-info">
                <div class="card-icon">🔄</div>
                <div class="card-content">
                    <h4>Workflow ID</h4>
                    <p class="card-value" title="${workflowId || 'N/A'}">${workflowId ? workflowId.substring(0, 8) + '...' : 'N/A'}</p>
                </div>
            </div>
            <div class="summary-card total-communications">
                <div class="card-icon">💬</div>
                <div class="card-content">
                    <h4>Total Communications</h4>
                    <p class="card-value">${summary.total_communications || 0}</p>
                </div>
            </div>
            <div class="summary-card success-rate">
                <div class="card-icon">✅</div>
                <div class="card-content">
                    <h4>Success Rate</h4>
                    <p class="card-value">${summary.total_communications > 0 ? Math.round((summary.successful_communications / summary.total_communications) * 100) : 0}%</p>
                </div>
            </div>
            <div class="summary-card total-duration">
                <div class="card-icon">⏱️</div>
                <div class="card-content">
                    <h4>Total Duration</h4>
                    <p class="card-value">${(summary.total_duration_ms || 0).toFixed(2)}ms</p>
                </div>
            </div>
            <div class="summary-card agents-involved">
                <div class="card-icon">🤖</div>
                <div class="card-content">
                    <h4>Agents Involved</h4>
                    <p class="card-value">${(summary.agents_involved || []).length}</p>
                </div>
            </div>
            <div class="summary-card state-transitions">
                <div class="card-icon">🔄</div>
                <div class="card-content">
                    <h4>State Transitions</h4>
                    <p class="card-value">${summary.state_transitions || 0}</p>
                </div>
            </div>
        </div>
    `;
}

function renderAgentCommunicationsOverview(agentComms) {
    const summary = agentComms.summary || {};
    const agents = agentComms.agents || [];

    return `
        <div class="overview-content">
            <div class="workflow-overview">
                <h4>Workflow Overview</h4>
                <div class="overview-grid">
                    <div class="overview-item">
                        <span class="overview-label">Workflow ID:</span>
                        <span class="overview-value" title="${agentComms.workflow_id || 'N/A'}">${agentComms.workflow_id ? agentComms.workflow_id.substring(0, 16) + '...' : 'N/A'}</span>
                    </div>
                    <div class="overview-item">
                        <span class="overview-label">Total Communications:</span>
                        <span class="overview-value">${summary.total_communications || 0}</span>
                    </div>
                    <div class="overview-item">
                        <span class="overview-label">Successful:</span>
                        <span class="overview-value">${summary.successful_communications || 0}</span>
                    </div>
                    <div class="overview-item">
                        <span class="overview-label">Failed:</span>
                        <span class="overview-value">${summary.failed_communications || 0}</span>
                    </div>
                    <div class="overview-item">
                        <span class="overview-label">Total Duration:</span>
                        <span class="overview-value">${(summary.total_duration_ms || 0).toFixed(2)}ms</span>
                    </div>
                    <div class="overview-item">
                        <span class="overview-label">Average Duration:</span>
                        <span class="overview-value">${summary.total_communications > 0 ? ((summary.total_duration_ms || 0) / summary.total_communications).toFixed(2) : 0}ms</span>
                    </div>
                </div>
            </div>

            <div class="agents-overview">
                <h4>Agents Involved</h4>
                <div class="agents-list">
                    ${(summary.agents_involved || []).map(agent => `
                        <span class="agent-badge">${agent}</span>
                    `).join('')}
                </div>
            </div>

            ${agents.length > 0 ? `
            <div class="execution-timeline">
                <h4>Execution Timeline</h4>
                <div class="timeline">
                    ${agents.slice(0, 5).map((agent, index) => `
                        <div class="timeline-event">
                            <div class="timeline-marker">${index + 1}</div>
                            <div class="timeline-content">
                                <div class="timeline-agent">${agent.agent}</div>
                                <div class="timeline-stage">${agent.stage}</div>
                                <div class="timeline-time">${new Date(agent.timestamp).toLocaleTimeString()}</div>
                            </div>
                        </div>
                    `).join('')}
                    ${agents.length > 5 ? `<div class="timeline-more">... and ${agents.length - 5} more events</div>` : ''}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

// Helper functions for enhanced context rendering
function renderContextOverview(contextPackage) {
    const hasEnhanced = contextPackage.system_metadata?.enhanced_architecture || false;
    const mentorAvailable = contextPackage.system_metadata?.mentor_service_available || false;
    const llmAvailable = contextPackage.system_metadata?.llm_client_available || false;

    return `
        <div class="context-overview-grid">
            <div class="overview-card user-info">
                <div class="card-icon">👤</div>
                <div class="card-content">
                    <h5>User Information</h5>
                    <p><strong>User ID:</strong> ${contextPackage.user_id || 'N/A'}</p>
                    <p><strong>Session:</strong> ${contextPackage.session_timestamp || 'N/A'}</p>
                    <p><strong>Environment:</strong> ${contextPackage.reported_environment || 'N/A'}</p>
                    <p><strong>Mood:</strong> ${contextPackage.reported_mood || 'N/A'}</p>
                </div>
            </div>

            <div class="overview-card architecture-status">
                <div class="card-icon">${hasEnhanced ? '🚀' : '📋'}</div>
                <div class="card-content">
                    <h5>Architecture Status</h5>
                    <p><strong>Enhanced:</strong> ${hasEnhanced ? '✅ Yes' : '❌ No'}</p>
                    <p><strong>MentorService:</strong> ${mentorAvailable ? '✅ Available' : '❌ Unavailable'}</p>
                    <p><strong>LLM Client:</strong> ${llmAvailable ? '✅ Available' : '❌ Unavailable'}</p>
                    <p><strong>Version:</strong> ${contextPackage.system_metadata?.dispatcher_version || '1.0.0'}</p>
                </div>
            </div>

            ${contextPackage.mentor_context ? `
            <div class="overview-card mentor-summary">
                <div class="card-icon">🧠</div>
                <div class="card-content">
                    <h5>Mentor Context</h5>
                    <p><strong>Trust Level:</strong> ${(contextPackage.mentor_context.trust_level * 100).toFixed(0)}%</p>
                    <p><strong>Emotional Tone:</strong> ${contextPackage.mentor_context.mentor_assessment?.emotional_tone || 'N/A'}</p>
                    <p><strong>Support Needs:</strong> ${contextPackage.mentor_context.mentor_assessment?.support_needs?.join(', ') || 'N/A'}</p>
                </div>
            </div>
            ` : ''}

            ${contextPackage.workflow_metadata ? `
            <div class="overview-card workflow-summary">
                <div class="card-icon">🔄</div>
                <div class="card-content">
                    <h5>Workflow Classification</h5>
                    <p><strong>Type:</strong> ${contextPackage.workflow_metadata.intended_workflow || 'N/A'}</p>
                    <p><strong>Confidence:</strong> ${(contextPackage.workflow_metadata.classification_confidence * 100).toFixed(0)}%</p>
                    <p><strong>Reason:</strong> ${contextPackage.workflow_metadata.classification_reason || 'N/A'}</p>
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderMentorContextDetails(mentorContext) {
    return `
        <div class="mentor-context-details">
            <div class="mentor-section trust-level">
                <h5>🎯 Trust Level & Communication</h5>
                <div class="trust-level-display">
                    <div class="trust-meter">
                        <div class="trust-fill" style="width: ${(mentorContext.trust_level * 100)}%"></div>
                        <span class="trust-value">${(mentorContext.trust_level * 100).toFixed(0)}%</span>
                    </div>
                </div>
                <div class="communication-prefs">
                    <h6>Communication Preferences</h6>
                    ${mentorContext.communication_preferences ? Object.entries(mentorContext.communication_preferences).map(([key, value]) =>
                        `<p><strong>${key.replace('_', ' ')}:</strong> ${value}</p>`
                    ).join('') : '<p>No preferences available</p>'}
                </div>
            </div>

            <div class="mentor-section conversation-context">
                <h5>💬 Conversation Context</h5>
                ${mentorContext.conversation_context ? `
                    <p><strong>Last Message:</strong> ${mentorContext.conversation_context.last_message || 'N/A'}</p>
                    <p><strong>Last Message Time:</strong> ${mentorContext.conversation_context.last_message_time || 'N/A'}</p>
                ` : '<p>No conversation context available</p>'}
            </div>

            <div class="mentor-section assessment">
                <h5>🔍 Mentor Assessment</h5>
                ${mentorContext.mentor_assessment ? `
                    <div class="assessment-grid">
                        <div class="assessment-item">
                            <span class="assessment-label">Emotional Tone:</span>
                            <span class="assessment-value">${mentorContext.mentor_assessment.emotional_tone || 'N/A'}</span>
                        </div>
                        <div class="assessment-item">
                            <span class="assessment-label">Urgency Level:</span>
                            <span class="assessment-value">${mentorContext.mentor_assessment.urgency_level || 'N/A'}</span>
                        </div>
                        <div class="assessment-item">
                            <span class="assessment-label">Support Needs:</span>
                            <span class="assessment-value">${mentorContext.mentor_assessment.support_needs?.join(', ') || 'N/A'}</span>
                        </div>
                    </div>
                ` : '<p>No assessment available</p>'}
            </div>
        </div>
    `;
}

function renderWorkflowMetadataDetails(workflowMetadata) {
    return `
        <div class="workflow-metadata-details">
            <div class="metadata-section classification">
                <h5>🎯 Workflow Classification</h5>
                <div class="classification-display">
                    <div class="classification-item">
                        <span class="classification-label">Intended Workflow:</span>
                        <span class="classification-value workflow-type">${workflowMetadata.intended_workflow || 'N/A'}</span>
                    </div>
                    <div class="classification-item">
                        <span class="classification-label">Classification Confidence:</span>
                        <div class="confidence-display">
                            <div class="confidence-meter">
                                <div class="confidence-fill" style="width: ${(workflowMetadata.classification_confidence * 100)}%"></div>
                                <span class="confidence-value">${(workflowMetadata.classification_confidence * 100).toFixed(0)}%</span>
                            </div>
                        </div>
                    </div>
                    <div class="classification-item">
                        <span class="classification-label">Classification Reason:</span>
                        <span class="classification-value">${workflowMetadata.classification_reason || 'N/A'}</span>
                    </div>
                    <div class="classification-item">
                        <span class="classification-label">LLM Classification Used:</span>
                        <span class="classification-value">${workflowMetadata.llm_classification_used ? '✅ Yes' : '❌ No'}</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderSystemMetadataDetails(systemMetadata) {
    return `
        <div class="system-metadata-details">
            <div class="system-section architecture">
                <h5>🏗️ Architecture Information</h5>
                <div class="system-grid">
                    <div class="system-item">
                        <span class="system-label">Dispatcher Version:</span>
                        <span class="system-value version">${systemMetadata.dispatcher_version || '1.0.0'}</span>
                    </div>
                    <div class="system-item">
                        <span class="system-label">Enhanced Architecture:</span>
                        <span class="system-value">${systemMetadata.enhanced_architecture ? '✅ Enabled' : '❌ Disabled'}</span>
                    </div>
                    <div class="system-item">
                        <span class="system-label">MentorService Available:</span>
                        <span class="system-value">${systemMetadata.mentor_service_available ? '✅ Available' : '❌ Unavailable'}</span>
                    </div>
                    <div class="system-item">
                        <span class="system-label">LLM Client Available:</span>
                        <span class="system-value">${systemMetadata.llm_client_available ? '✅ Available' : '❌ Unavailable'}</span>
                    </div>
                    <div class="system-item">
                        <span class="system-label">Processing Timestamp:</span>
                        <span class="system-value">${systemMetadata.processing_timestamp || 'N/A'}</span>
                    </div>
                    ${systemMetadata.llm_initialization_error ? `
                    <div class="system-item error">
                        <span class="system-label">LLM Initialization Error:</span>
                        <span class="system-value error">${systemMetadata.llm_initialization_error}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Utility function for toggling sections
function toggleSection(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.toggle-icon');

    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        icon.textContent = '▲';
    } else {
        content.style.display = 'none';
        icon.textContent = '▼';
    }
}
