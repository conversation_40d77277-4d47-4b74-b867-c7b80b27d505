<!-- Wheel Generation Evaluation Modal -->
<div id="workflow-details-modal" class="modal">
    <div class="modal-content wheel-generation-modal">
        <span class="close">&times;</span>
        <div class="modal-header wheel-generation-header">
            <h2>🎡 Wheel Generation Workflow Analysis</h2>
            <p class="modal-description">
                Deep analysis of the wheel generation process: agent coordination, tool calls, activity tailoring, and decision-making flow. Debug inappropriate items by tracing their creation process.
            </p>
            <div class="architecture-badges">
                
            </div>
        </div>
        <div id="workflow-modal-body" class="workflow-modal-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading wheel generation analysis...</p>
            </div>
        </div>

        <!-- Timeline View Toggle -->
        <div class="timeline-view-toggle">
            <button id="show-timeline-btn" class="timeline-toggle-btn">
                🕒 Show Execution Timeline
            </button>
        </div>
    </div>
</div>

<!-- Vertical Timeline Modal -->
<div id="vertical-timeline-modal" class="modal">
    <div class="modal-content vertical-timeline-modal">
        <span class="close">&times;</span>
        <div class="modal-header vertical-timeline-header">
            <h2>⏱️ Workflow Execution Timeline</h2>
            <p class="modal-description">
                Visual timeline showing the execution flow with time-proportional spacing. Longer operations take more vertical space to help identify performance bottlenecks.
            </p>
            <div class="timeline-controls">
                <button id="timeline-zoom-in" class="timeline-control-btn">🔍 Zoom In</button>
                <button id="timeline-zoom-out" class="timeline-control-btn">🔍 Zoom Out</button>
                <button id="timeline-reset" class="timeline-control-btn">🔄 Reset</button>
                <span class="timeline-scale-indicator">Scale: <span id="timeline-scale">1x</span></span>
            </div>
        </div>
        <div id="vertical-timeline-body" class="vertical-timeline-body">
            <!-- Timeline content will be loaded here by JS -->
            <div class="timeline-loading">
                <div class="loader"></div>
                <p>Loading execution timeline...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced Wheel Generation Modal Styles */
.wheel-generation-modal .modal-content {
    max-width: 1600px;
    width: 98%;
}

.wheel-generation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.wheel-generation-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.architecture-badges {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.architecture-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: help;
}

.architecture-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.architecture-badge.wheel-generation {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.architecture-badge.multi-agent {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.architecture-badge.debugging {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.architecture-badge.phase2 {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.architecture-badge.real-time {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.architecture-badge.correlation {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

/* Enhanced Context Explorer Styles */
.enhanced-context-explorer {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.context-explorer-header {
    margin-bottom: 20px;
}

.context-explorer-header h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1.2em;
}

.architecture-info {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.version-badge, .feature-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    color: white;
}

.version-badge.enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.version-badge.standard {
    background: #6c757d;
}

.feature-badge.mentor {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.feature-badge.workflow {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Enhanced Tab System */
.context-tabs-enhanced {
    margin-top: 15px;
}

.tab-buttons-enhanced {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tab-button-enhanced {
    background: #e9ecef;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.tab-button-enhanced:hover {
    background: #dee2e6;
    transform: translateY(-1px);
}

.tab-button-enhanced.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.tab-content-enhanced {
    min-height: 200px;
}

.tab-pane-enhanced {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-pane-enhanced.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Context Overview Grid */
.context-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.overview-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

.overview-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.overview-card .card-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.overview-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.overview-card p {
    margin: 5px 0;
    font-size: 12px;
    color: #6c757d;
}

/* Trust Level Meter */
.trust-level-display {
    margin: 15px 0;
}

.trust-meter {
    position: relative;
    background: #e9ecef;
    height: 20px;
    border-radius: 10px;
    overflow: hidden;
}

.trust-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.3s ease;
}

.trust-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Confidence Meter */
.confidence-meter {
    position: relative;
    background: #e9ecef;
    height: 16px;
    border-radius: 8px;
    overflow: hidden;
    margin: 5px 0;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.confidence-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.workflow-modal-body {
    max-height: 75vh;
    overflow-y: auto;
}

.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.modal-loading .loader {
    margin-bottom: 15px;
}

/* Enhanced scrollbar for workflow modal body */
.workflow-modal-body::-webkit-scrollbar {
    width: 8px;
}

.workflow-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.workflow-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.workflow-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Phase 2 Advanced Monitoring Styles */
.phase2-monitoring-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.phase2-monitoring-panel h3 {
    margin: 0 0 15px 0;
    font-size: 1.4em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.monitoring-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.monitoring-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.monitoring-card h4 {
    margin: 0 0 10px 0;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.monitoring-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.monitoring-metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.metric-value {
    font-weight: 600;
    font-size: 1em;
}

.metric-value.good {
    color: #4caf50;
}

.metric-value.warning {
    color: #ff9800;
}

.metric-value.error {
    color: #f44336;
}

/* State Consistency Visualization */
.state-consistency-timeline {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.state-snapshot {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    margin: 5px 0;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.state-snapshot:hover {
    background: #e9ecef;
}

.state-snapshot.valid {
    border-left: 4px solid #28a745;
}

.state-snapshot.warning {
    border-left: 4px solid #ffc107;
}

.state-snapshot.inconsistent {
    border-left: 4px solid #dc3545;
}

.snapshot-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.snapshot-indicator.valid {
    background: #28a745;
}

.snapshot-indicator.warning {
    background: #ffc107;
}

.snapshot-indicator.inconsistent {
    background: #dc3545;
}

.snapshot-info {
    flex: 1;
}

.snapshot-agent {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.snapshot-stage {
    font-size: 0.9em;
    color: #6c757d;
}

.snapshot-timestamp {
    font-size: 0.8em;
    color: #6c757d;
    font-family: monospace;
}

/* Tool Call Sequence Analysis */
.tool-sequence-analysis {
    background: #f3e5f5;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e1bee7;
}

.tool-sequence {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e1bee7;
}

.sequence-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.sequence-type-badge.sequential {
    background: #d4edda;
    color: #155724;
}

.sequence-type-badge.parallel {
    background: #fff3cd;
    color: #856404;
}

.sequence-type-badge.dependent {
    background: #f8d7da;
    color: #721c24;
}

.sequence-tools {
    flex: 1;
    font-size: 0.9em;
    color: #6c757d;
}

.sequence-performance {
    text-align: right;
    font-size: 0.9em;
}

.sequence-duration {
    font-weight: 600;
    color: #495057;
}

.sequence-efficiency {
    font-size: 0.8em;
    color: #6c757d;
}

/* Performance Correlation Matrix */
.correlation-matrix {
    background: #e7f3ff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #bee5eb;
}

.correlation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: white;
    border-radius: 6px;
    border: 1px solid #bee5eb;
}

.correlation-components {
    font-weight: 600;
    color: #0c5460;
}

.correlation-coefficient {
    display: flex;
    align-items: center;
    gap: 8px;
}

.correlation-value {
    font-weight: 600;
    font-family: monospace;
}

.correlation-value.positive {
    color: #28a745;
}

.correlation-value.negative {
    color: #dc3545;
}

.correlation-value.none {
    color: #6c757d;
}

.correlation-bar {
    width: 60px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.correlation-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.correlation-fill.positive {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.correlation-fill.negative {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

/* Performance Insights Panel */
.performance-insights {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.insight-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.insight-card h5 {
    margin: 0 0 10px 0;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-metric {
    font-size: 1.5em;
    font-weight: 700;
    margin: 5px 0;
}

.insight-description {
    font-size: 0.9em;
    opacity: 0.9;
    line-height: 1.4;
}

/* Debugging Recommendations */
.debugging-recommendations {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.debugging-recommendations h4 {
    margin: 0 0 15px 0;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendation-item {
    background: white;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
    border-left: 4px solid transparent;
}

.recommendation-item.high {
    border-left-color: #dc3545;
}

.recommendation-item.medium {
    border-left-color: #ffc107;
}

.recommendation-item.low {
    border-left-color: #28a745;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.recommendation-title {
    font-weight: 600;
    color: #495057;
}

.recommendation-priority {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: #f8d7da;
    color: #721c24;
}

.recommendation-priority.medium {
    background: #fff3cd;
    color: #856404;
}

.recommendation-priority.low {
    background: #d4edda;
    color: #155724;
}

.recommendation-description {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 8px;
    line-height: 1.4;
}

.recommendation-suggestion {
    color: #495057;
    font-size: 0.9em;
    font-style: italic;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

/* Interactive Event Tree Styles */
.workflow-event-tree {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.event-tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.tree-controls {
    display: flex;
    gap: 10px;
}

.tree-control-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.tree-control-btn:hover {
    background: #0056b3;
}

.tree-control-btn.secondary {
    background: #6c757d;
}

.tree-control-btn.secondary:hover {
    background: #545b62;
}

/* Event Tree Structure */
.event-tree-container {
    position: relative;
    padding-left: 30px;
}

.event-tree-line {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #007bff, #6c757d);
}

.workflow-event {
    position: relative;
    margin-bottom: 15px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.workflow-event:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.workflow-event.has-issues {
    border-left: 4px solid #dc3545;
}

.workflow-event.has-mocks {
    border-left: 4px solid #ffc107;
}

.workflow-event.has-fallbacks {
    border-left: 4px solid #17a2b8;
}

.event-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s;
}

.event-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.event-header.expanded {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.event-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.event-sequence {
    background: #007bff;
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
}

.event-agent {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.event-header.expanded .event-agent {
    color: white;
}

.event-stage {
    background: #6c757d;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.event-timestamp {
    color: #6c757d;
    font-size: 12px;
    font-family: monospace;
}

.event-header.expanded .event-timestamp {
    color: rgba(255,255,255,0.8);
}

.event-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    font-size: 16px;
}

.status-indicator.success { color: #28a745; }
.status-indicator.error { color: #dc3545; }
.status-indicator.warning { color: #ffc107; }

.event-duration {
    background: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.event-header.expanded .event-duration {
    background: rgba(255,255,255,0.2);
    color: white;
}

.event-toggle {
    font-size: 14px;
    transition: transform 0.3s ease;
    color: #6c757d;
}

.event-header.expanded .event-toggle {
    transform: rotate(180deg);
    color: white;
}

/* Event Details */
.event-details {
    display: none;
    padding: 20px;
    background: #fdfdfd;
}

.event-details.expanded {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

.event-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.detail-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.detail-panel-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-panel-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.tool-calls-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tool-call-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.tool-call-icon {
    font-size: 16px;
}

.tool-call-name {
    font-weight: 600;
    color: #007bff;
}

.tool-call-params {
    font-size: 12px;
    color: #6c757d;
    margin-left: auto;
}

/* Issue Indicators */
.issue-indicators {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.issue-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.issue-badge.mock {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.issue-badge.fallback {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.issue-badge.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.issue-badge.slow {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Enhanced Tool Calls Display */
.tool-calls-enhanced {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.tool-call-enhanced {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.tool-call-enhanced:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tool-call-header {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.tool-call-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-call-name {
    font-weight: 600;
    color: #1a73e8;
    font-size: 14px;
}

.tool-call-index {
    background: #1a73e8;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
}

.tool-call-meta {
    display: flex;
    align-items: center;
    gap: 6px;
}

.tool-duration {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.mock-badge {
    background: #fff3cd;
    color: #856404;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #ffeaa7;
}

.error-badge {
    background: #f8d7da;
    color: #721c24;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    border: 1px solid #f5c6cb;
}

.tool-call-details {
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tool-calls-count {
    background: #1a73e8;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    margin-left: auto;
}

/* Enhanced LLM Call Display */
.llm-call-enhanced {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border: 1px solid #ce93d8;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.llm-call-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.llm-model-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.llm-model-name {
    font-weight: 600;
    color: #7b1fa2;
    font-size: 14px;
}

.llm-temperature {
    background: #7b1fa2;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.llm-tokens {
    display: flex;
    gap: 8px;
}

.token-count {
    background: rgba(123, 31, 162, 0.1);
    color: #7b1fa2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid rgba(123, 31, 162, 0.3);
}

.llm-cost {
    background: #4caf50;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* JSON Data Display */
.json-data-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 4px 0;
}

.json-data-container.error {
    border-color: #dc3545;
    background: #fff5f5;
}

.json-data-header {
    background: #e9ecef;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.json-data-header:hover {
    background: #dee2e6;
}

.json-data-container.error .json-data-header {
    background: #f8d7da;
    color: #721c24;
}

.json-data-content {
    display: none;
    padding: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.json-data-content.expanded {
    display: block;
}

.json-data-content pre {
    margin: 0;
    font-size: 11px;
    line-height: 1.4;
    color: #495057;
    background: white;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.toggle-icon {
    transition: transform 0.2s ease;
}

/* Enhanced Insight Cards */
.insight-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.insight-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.insight-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.insight-card.performance {
    border-left: 4px solid #28a745;
}

.insight-card.agents {
    border-left: 4px solid #007bff;
}

.insight-card.output-quality {
    border-left: 4px solid #6f42c1;
}

.insight-card.cost-analysis {
    border-left: 4px solid #fd7e14;
}

.card-icon {
    font-size: 2em;
    margin-bottom: 10px;
    opacity: 0.8;
}

.card-content h5 {
    margin: 0 0 15px 0;
    color: #495057;
    font-weight: 600;
}

.card-content p {
    margin: 8px 0;
    color: #6c757d;
    font-size: 14px;
}

.optimization-tip {
    background: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
    padding: 8px 12px;
    margin-top: 15px;
    border-radius: 4px;
    font-size: 13px;
    color: #495057 !important;
}

/* Performance Breakdown */
.performance-breakdown {
    margin: 30px 0;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.performance-breakdown h4 {
    margin: 0 0 20px 0;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 10px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.metric-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.metric-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-header {
    background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.metric-icon {
    font-size: 1.2em;
}

.metric-title {
    font-weight: 600;
    font-size: 14px;
}

.metric-details {
    padding: 20px;
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-value {
    font-weight: 600;
    font-size: 14px;
}

.metric-value.success {
    color: #28a745;
}

.metric-value.warning {
    color: #ffc107;
}

.metric-value.error {
    color: #dc3545;
}

.metric-value.info {
    color: #17a2b8;
}

/* Workflow Summary Cards */
.workflow-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.workflow-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
}

.workflow-summary-card h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    opacity: 0.9;
}

.workflow-summary-card .value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.workflow-summary-card .label {
    font-size: 12px;
    opacity: 0.8;
}

/* New Section Styles */

/* Wheel Output Section */
.wheel-output-section .wheel-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.wheel-meta {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 20px;
}

.wheel-info h4 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1.3em;
}

.trust-phase {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.trust-phase.foundation {
    background: #e3f2fd;
    color: #1976d2;
}

.wheel-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.activity-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.activity-header h5 {
    margin: 0;
    color: #495057;
}

.domain-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    background: #e9ecef;
    color: #495057;
}

.domain-badge.creativity { background: #ffe6e6; color: #d63384; }
.domain-badge.wellness { background: #e6f7ff; color: #0969da; }
.domain-badge.physical { background: #e6ffe6; color: #28a745; }
.domain-badge.learning { background: #fff3cd; color: #856404; }
.domain-badge.personal_growth { background: #f3e5f5; color: #6f42c1; }

.activity-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.activity-meta {
    display: flex;
    gap: 15px;
    margin: 10px 0;
    font-size: 12px;
    color: #6c757d;
}

.value-proposition {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
}

.value-proposition h6 {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: #495057;
}

.value-proposition p {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
    color: #6c757d;
}

/* Psychological Section */
.psychological-section .psych-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.trust-analysis {
    margin-bottom: 25px;
}

.trust-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.trust-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.state-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.state-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
}

.beliefs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.belief-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.belief-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.belief-strength {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.strength-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.traits-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.trait-badge {
    padding: 4px 12px;
    background: #e7f3ff;
    color: #0969da;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Ethical Section */
.ethical-section .ethical-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.validation-status {
    margin-bottom: 25px;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.approved {
    background: #d4edda;
    color: #155724;
}

.validation-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.validation-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.principle-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.principle-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.validation-stats {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.stat {
    text-align: center;
}

.stat .stat-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
    color: #495057;
}

.stat .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

.vulnerability-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.vulnerability-badge {
    padding: 4px 12px;
    background: #fff3cd;
    color: #856404;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.boundaries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.boundary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 14px;
}

/* Strategy Section */
.strategy-section .strategy-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.gap-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.gap-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.domain-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.domain-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.domain-percentage {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;
}

.percentage-bar {
    flex: 1;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.percentage-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.domain-reason {
    font-size: 12px;
    color: #6c757d;
    margin: 8px 0 0 0;
}

.distribution-summary {
    margin-top: 20px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.criteria-sections {
    margin-top: 15px;
}

.criteria-section {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.criteria-section h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
}

.criteria-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.criteria-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.criteria-item:last-child {
    border-bottom: none;
}

/* Semantic Quality Section */
.semantic-section .semantic-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.quality-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.quality-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.dimensions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.dimension-badge {
    padding: 4px 12px;
    background: #e7f3ff;
    color: #0969da;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.model-evaluation {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.model-evaluation h5 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
}

.model-score {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dimension-score {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.dimension-name {
    font-weight: 600;
    color: #495057;
}

.dimension-reasoning {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
}

.tone-analysis {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.tone-analysis h6 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
}

.tone-score {
    margin-bottom: 8px;
    font-weight: 600;
}

.overall-reasoning {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.overall-reasoning h6 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
}

/* Score Classes */
.score-excellent { color: #28a745; font-weight: 600; }
.score-good { color: #20c997; font-weight: 600; }
.score-fair { color: #ffc107; font-weight: 600; }
.score-poor { color: #dc3545; font-weight: 600; }
.score-unknown { color: #6c757d; }

/* Resource Agent Summary Styles */
.resource-agent-summary {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #e9ecef;
}

.resource-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
    margin-bottom: 12px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 12px;
}

.metric-label {
    font-weight: 600;
    color: #495057;
}

.metric-value {
    font-weight: 600;
    color: #007bff;
}

.resource-insights {
    font-size: 11px;
    line-height: 1.4;
}

.resource-insights > div {
    margin-bottom: 4px;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.constraints strong {
    color: #dc3545;
}

.opportunities strong {
    color: #28a745;
}

.recommendations strong {
    color: #007bff;
}

/* Execution Modes Section */
.execution-modes-section .execution-modes {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.mode-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.mode-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.mode-value.enabled { color: #28a745; font-weight: 600; }
.mode-value.disabled { color: #dc3545; font-weight: 600; }

.agents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.agent-mode-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.agent-mode-card h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
}

.agent-mode-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mode-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.mode-detail .enabled { color: #28a745; font-weight: 600; }
.mode-detail .disabled { color: #dc3545; font-weight: 600; }

.comparison-table {
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.comparison-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-row:first-child {
    background: #f8f9fa;
    font-weight: 600;
}

.comparison-label {
    font-weight: 600;
    color: #495057;
}

.comparison-requested.enabled,
.comparison-actual.enabled { color: #28a745; font-weight: 600; }

.comparison-requested.disabled,
.comparison-actual.disabled { color: #dc3545; font-weight: 600; }

.comparison-match.match { color: #28a745; }
.comparison-match.mismatch { color: #ffc107; }

/* Timeline Visualization Styles */
.timeline-visualization {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

.timeline-controls {
    display: flex;
    gap: 10px;
}

.timeline-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.timeline-btn:hover {
    background: #138496;
}

.timeline-chart {
    position: relative;
    height: 200px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow-x: auto;
    overflow-y: hidden;
}

.timeline-track {
    position: relative;
    height: 100%;
    min-width: 800px;
    padding: 20px;
}

.timeline-event-marker {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    cursor: pointer;
    transition: all 0.3s ease;
}

.timeline-event-marker:hover {
    transform: translateY(-50%) scale(1.3);
    z-index: 10;
}

.timeline-event-marker.success {
    background: #28a745;
}

.timeline-event-marker.error {
    background: #dc3545;
}

.timeline-event-marker.warning {
    background: #ffc107;
}

.timeline-event-label {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.timeline-event-marker:hover .timeline-event-label {
    opacity: 1;
}

.timeline-axis {
    position: absolute;
    bottom: 10px;
    left: 20px;
    right: 20px;
    height: 2px;
    background: #dee2e6;
}

.timeline-axis-label {
    position: absolute;
    bottom: -20px;
    font-size: 10px;
    color: #6c757d;
    transform: translateX(-50%);
}

/* Error Analysis Styles */
.error-analysis {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.error-analysis h5 {
    color: #c53030;
    margin: 0 0 10px 0;
}

.error-item {
    color: #742a2a;
    background: #fed7d7;
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 4px;
    border-left: 3px solid #e53e3e;
}

/* Tool Call Analysis Styles */
.tool-call-analysis {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.tool-call-analysis h5 {
    color: #2d3748;
    margin: 0 0 10px 0;
}

.tool-call-json {
    background: #1a202c;
    color: #e2e8f0;
    padding: 12px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
}

/* Technical Mode Styles */
.technical-mode .workflow-event {
    border-left: 4px solid #6c757d;
}

.technical-mode .event-header {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
}

.technical-mode .event-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.technical-details-panel {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 10px 0;
    overflow: hidden;
}

.technical-details-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.technical-details-content {
    padding: 15px;
    display: none;
}

.technical-details-content.expanded {
    display: block;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.performance-metric {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.metric-value {
    font-size: 18px;
    font-weight: bold;
    color: #495057;
    display: block;
}

.metric-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

/* Raw Data Section */
.raw-data-section {
    margin-top: 30px;
}

.raw-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.copy-raw-data-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.copy-raw-data-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.raw-data-controls {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 10px;
}

.copy-tab-data-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: background-color 0.2s;
}

.copy-tab-data-btn:hover {
    background: #5a6268;
}

.raw-data-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    color: #495057;
    background: #f8f9fa;
}

.tab-btn.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: #f8f9fa;
}

.raw-data-content {
    position: relative;
}

.raw-data-tab {
    display: none;
}

.raw-data-tab.active {
    display: block;
}

.raw-data-tab .json-viewer {
    max-height: 500px;
    overflow-y: auto;
}

/* Timeline View Toggle */
.timeline-view-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
}

.timeline-toggle-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.timeline-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Vertical Timeline Modal Styles */
.vertical-timeline-modal .modal-content {
    max-width: 1200px;
    width: 95%;
    height: 90vh;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.vertical-timeline-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 0;
    text-align: center;
    flex-shrink: 0;
}

.vertical-timeline-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.timeline-controls {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.timeline-control-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.timeline-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.timeline-scale-indicator {
    color: rgba(255, 255, 255, 0.9);
    font-size: 12px;
    font-weight: 600;
}

.vertical-timeline-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.timeline-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

/* Vertical Timeline Container */
.vertical-timeline-container {
    position: relative;
    padding-left: 60px;
    min-height: 100%;
}

.timeline-axis {
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
    border-radius: 2px;
}

.timeline-event {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #e9ecef;
}

.timeline-event:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    transform: translateX(5px);
}

.timeline-event.success {
    border-left-color: #28a745;
}

.timeline-event.warning {
    border-left-color: #ffc107;
}

.timeline-event.error {
    border-left-color: #dc3545;
}

.timeline-event.slow {
    border-left-color: #fd7e14;
}

/* Timeline Event Marker */
.timeline-marker {
    position: absolute;
    left: -47px;
    top: 20px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    border: 3px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: white;
    font-weight: bold;
}

.timeline-marker.success {
    background: #28a745;
}

.timeline-marker.warning {
    background: #ffc107;
}

.timeline-marker.error {
    background: #dc3545;
}

.timeline-marker.slow {
    background: #fd7e14;
}

/* Timeline Event Content */
.timeline-event-content {
    padding: 20px;
}

.timeline-event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.timeline-agent-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.timeline-agent-name {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
}

.timeline-stage-badge {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timeline-performance {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.timeline-duration {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.timeline-duration.slow {
    background: #fff3cd;
    color: #856404;
}

.timeline-duration.fast {
    background: #d4edda;
    color: #155724;
}

.timeline-timestamp {
    color: #6c757d;
    font-size: 11px;
    font-family: monospace;
}

/* Timeline Event Details */
.timeline-event-details {
    margin-top: 15px;
}

.timeline-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.timeline-detail-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
}

.timeline-detail-card h6 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timeline-detail-content {
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

/* Timeline Metrics */
.timeline-metrics {
    display: flex;
    gap: 15px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.timeline-metric {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    text-align: center;
    min-width: 80px;
}

.timeline-metric-value {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #495057;
}

.timeline-metric-label {
    display: block;
    font-size: 10px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 2px;
}

/* Timeline Issues */
.timeline-issues {
    margin-top: 15px;
}

.timeline-issue {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 5px 0;
    font-size: 12px;
    color: #c53030;
}

.timeline-issue.warning {
    background: #fffbf0;
    border-color: #ffeaa7;
    color: #b45309;
}

/* Timeline Summary */
.timeline-summary {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.timeline-summary h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.2em;
}

.timeline-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.timeline-summary-item {
    text-align: center;
}

.timeline-summary-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
}

.timeline-summary-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    margin-top: 4px;
}

/* Wheel Generation Specific Styles */
.wheel-generation-flow {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.agent-flow-visualization {
    margin-top: 15px;
}

.agent-flow-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.agent-flow-step {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    min-width: 150px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.agent-flow-step:hover {
    border-color: #667eea;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.agent-flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.agent-sequence {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.agent-name {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.agent-status {
    font-size: 16px;
}

.agent-duration {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.agent-flow-stage {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.agent-flow-arrow {
    font-size: 20px;
    color: #6c757d;
    margin: 0 5px;
}

.agent-flow-arrow:last-child {
    display: none;
}

/* Wheel Items Analysis */
.wheel-items-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.wheel-items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.wheel-item-analysis {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.wheel-item-analysis:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.wheel-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.wheel-item-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.domain-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.percentage-badge {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.wheel-item-details {
    margin-bottom: 15px;
}

.wheel-item-details p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.show-tailoring-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-tailoring-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Agent Timeline Styles */
.agent-timeline-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.agent-timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 15px;
}

.agent-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-event {
    position: relative;
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.timeline-event:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.timeline-event.success {
    border-left: 4px solid #28a745;
}

.timeline-event.error {
    border-left: 4px solid #dc3545;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.timeline-event.success .timeline-marker {
    background: #28a745;
}

.timeline-event.error .timeline-marker {
    background: #dc3545;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.timeline-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.timeline-time {
    background: #e9ecef;
    color: #6c757d;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.timeline-duration {
    background: #667eea;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

.timeline-stage {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 10px;
}

.timeline-metrics {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.metric-item {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    color: #495057;
    border: 1px solid #e9ecef;
}

.metric-item[title*="Used real LLM"] {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.metric-item[title*="Used mock"] {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Tool call details styling */
.tool-calls-section {
    margin: 20px 0;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.tool-call-item {
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    overflow: hidden;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f1f3f4;
    border-bottom: 1px solid #dee2e6;
}

.tool-name {
    font-weight: bold;
    color: #495057;
}

.tool-status.success {
    color: #28a745;
}

.tool-status.error {
    color: #dc3545;
}

.tool-duration {
    font-size: 0.9em;
    color: #6c757d;
}

.tool-details {
    padding: 15px;
}

.tool-input, .tool-output {
    margin-bottom: 10px;
}

.tool-input pre, .tool-output pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    font-size: 0.85em;
    max-height: 200px;
    overflow-y: auto;
}

.tool-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 8px;
    margin-top: 10px;
}

/* Agent details modal styling */
.agent-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
}

.agent-details-modal .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
}

.agent-details-modal .modal-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.execution-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    background: #f8f9fa;
}

.metric-card h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.data-section {
    margin: 20px 0;
}

.data-section h4 {
    margin-bottom: 10px;
    color: #495057;
}

.json-display {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-size: 0.9em;
    max-height: 300px;
    overflow-y: auto;
}

.show-details-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-details-btn:hover {
    background: #138496;
    transform: translateY(-1px);
}

/* Tool Calls Analysis Styles */
.tool-calls-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e9ecef;
}

.tool-calls-summary {
    display: flex;
    gap: 30px;
    margin: 15px 0 25px 0;
    justify-content: center;
}

.summary-stat {
    text-align: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-width: 120px;
}

.summary-stat .stat-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.summary-stat .stat-label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-calls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.tool-analysis-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.tool-analysis-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.tool-header h5 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

.tool-count {
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.tool-percentage {
    background: #e9ecef;
    color: #6c757d;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.show-tool-calls-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.show-tool-calls-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* Section descriptions */
.section-description {
    color: #6c757d;
    font-style: italic;
    margin-bottom: 15px;
    font-size: 14px;
}

/* No data states */
.no-data, .no-wheel-data, .no-timeline-data, .no-tool-data {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.no-data-with-mock {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
}

.no-data-message {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ffeaa7;
}

.no-data-message p {
    margin: 8px 0;
    color: #856404;
}

.no-data-message ul {
    text-align: left;
    margin: 10px 0;
    padding-left: 20px;
}

.no-data-message li {
    color: #6c757d;
    margin: 5px 0;
}

.mock-agent-flow .agent-flow-step {
    opacity: 0.7;
    border-style: dashed;
}

.mock-agent-flow .agent-flow-step:hover {
    opacity: 1;
    border-style: solid;
}

.mock-step .agent-duration {
    background: #ffeaa7;
    color: #856404;
}

.mock-flow::before {
    content: '⚠️ Mock Data - Typical Workflow Structure';
    display: block;
    text-align: center;
    font-weight: 600;
    color: #856404;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #ffeaa7;
}

/* Agent details modal styles */
.agent-details-modal, .wheel-crafting-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.agent-details-modal .modal-backdrop, .wheel-crafting-modal .modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.agent-details-modal .modal-content, .wheel-crafting-modal .modal-content {
    position: relative;
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Crafting sequence styles */
.crafting-timeline {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.crafting-step {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;
}

.step-marker {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.step-number {
    background: #007cba;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
}

.step-status.success {
    color: #28a745;
}

.step-status.failure {
    color: #dc3545;
}

.step-content {
    flex: 1;
}

.step-header {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.agent-name {
    background: #007cba;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.tool-name {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.step-duration {
    background: #ffc107;
    color: #212529;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.execution-mode {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.execution-mode.real {
    background: #28a745;
    color: white;
}

.execution-mode.mock {
    background: #dc3545;
    color: white;
}

.step-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.step-input, .step-output {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.step-input pre, .step-output pre {
    margin: 5px 0 0 0;
    font-size: 11px;
    max-height: 150px;
    overflow-y: auto;
}

/* Agent contributions grid */
.agent-contributions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.agent-contribution {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.agent-header h5 {
    margin: 0;
    color: #007cba;
}

.agent-stage {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.agent-status.success {
    color: #28a745;
}

.agent-status.failure {
    color: #dc3545;
}

.agent-metrics {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 10px;
}

.metric {
    text-align: center;
}

.metric .label {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 2px;
}

.metric .value {
    display: block;
    font-weight: bold;
    color: #212529;
}

.agent-tools {
    font-size: 12px;
    color: #495057;
}

/* Enhanced agent details modal styles */
.agent-details-content {
    padding: 20px;
}

.agent-overview {
    margin-bottom: 20px;
}

/* Rich agent output styles */
.rich-agent-output {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.rich-data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
}

.success-badge {
    background: #d4edda;
    color: #155724;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.warning-badge {
    background: #fff3cd;
    color: #856404;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.rich-fields-count {
    background: #e9ecef;
    color: #495057;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.rich-field-section {
    margin: 15px 0;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.rich-field-title {
    background: #e9ecef;
    color: #495057;
    margin: 0;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: bold;
}

.rich-field-content {
    padding: 15px;
}

.context-item, .analysis-item, .assessment-item, .strategy-item, .validation-item {
    margin: 8px 0;
    padding: 8px;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.wheel-data {
    background: white;
    border-radius: 6px;
    padding: 15px;
}

.wheel-summary {
    font-weight: bold;
    margin-bottom: 15px;
    color: #495057;
}

.wheel-items {
    display: grid;
    gap: 10px;
}

.wheel-item {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    background: #f8f9fa;
}

.item-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.item-number {
    background: #007bff;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.item-name {
    font-weight: bold;
    color: #495057;
}

.item-details {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.item-domain, .item-duration, .item-challenge {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.fallback-output {
    background: #fff3cd;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.fallback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fallback-note {
    font-size: 12px;
    color: #856404;
    font-style: italic;
}

.raw-data-details, .context-details, .analysis-details, .assessment-details, .strategy-details, .wheel-details, .validation-details {
    margin-top: 15px;
}

.raw-data-details summary, .context-details summary, .analysis-details summary, .assessment-details summary, .strategy-details summary, .wheel-details summary, .validation-details summary {
    cursor: pointer;
    font-weight: bold;
    color: #007bff;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 10px;
}

.raw-data-details summary:hover, .context-details summary:hover, .analysis-details summary:hover, .assessment-details summary:hover, .strategy-details summary:hover, .wheel-details summary:hover, .validation-details summary:hover {
    background: #e9ecef;
}

.agent-meta {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

.badge-primary {
    background: #007cba;
    color: white;
}

.badge-info {
    background: #17a2b8;
    color: white;
}

.badge-success {
    background: #28a745;
    color: white;
}

.badge-danger {
    background: #dc3545;
    color: white;
}

.agent-sections {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.section h4 {
    margin: 0 0 10px 0;
    color: #007cba;
}

.json-viewer {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
}

.tool-call-detail {
    border: 1px solid #ddd;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f1f3f4;
    border-bottom: 1px solid #ddd;
}

.tool-name {
    font-weight: bold;
    color: #007cba;
}

.tool-status.success {
    color: #28a745;
}

.tool-status.error {
    color: #dc3545;
}

.tool-duration {
    background: #ffc107;
    color: #212529;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.tool-mode {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
}

.tool-details {
    padding: 10px;
}

.tool-input, .tool-output {
    margin-bottom: 10px;
}

.tool-input pre, .tool-output pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
    font-size: 10px;
    max-height: 150px;
    overflow-y: auto;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.metric {
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.metric .label {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 5px;
}

.metric .value {
    display: block;
    font-weight: bold;
    font-size: 16px;
    color: #007cba;
}

.error-section {
    border-color: #dc3545;
    background: #f8d7da;
}

.error-content {
    color: #721c24;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}
</style>

<script>
// Helper functions defined first
function renderAgentFlowVisualization(agentCommunications, actualModes) {
    // Handle multiple data structure formats for agent communications
    let agents = [];

    if (Array.isArray(agentCommunications)) {
        agents = agentCommunications;
    } else if (agentCommunications.agents) {
        agents = agentCommunications.agents;
    } else if (window.currentWorkflowModalData?.agent_communications) {
        const comms = window.currentWorkflowModalData.agent_communications;
        if (Array.isArray(comms)) {
            agents = comms;
        } else if (comms.agents) {
            agents = comms.agents;
        }
    }

    console.log('🔍 Agent flow visualization - agents found:', agents.length);

    // If no agent communications, create a mock flow based on typical wheel generation workflow
    if (agents.length === 0) {
        return `
            <div class="no-data-with-mock">
                <div class="no-data-message">
                    <p>⚠️ No detailed agent communication data available in this benchmark run.</p>
                    <p>Showing typical wheel generation workflow structure:</p>
                </div>
                <div class="mock-agent-flow">
                    ${renderMockAgentFlow()}
                </div>
            </div>
        `;
    }

    const agentFlow = agents.map((comm, index) => {
        const statusIcon = comm.success !== false ? '✅' : '❌';
        const duration = comm.duration_ms ? `${comm.duration_ms.toFixed(2)}ms` : 'N/A';

        return `
            <div class="agent-flow-step clickable" data-agent="${comm.agent}" data-index="${index}"
                 onclick="showAgentDetails('${comm.agent}', ${index})"
                 title="Click to view detailed execution information">
                <div class="agent-flow-header">
                    <span class="agent-sequence">${index + 1}</span>
                    <span class="agent-name">${comm.agent}</span>
                    <span class="agent-status">${statusIcon}</span>
                    <span class="agent-duration">${duration}</span>
                </div>
                <div class="agent-flow-stage">${comm.stage || 'Unknown Stage'}</div>
                <div class="agent-flow-arrow">→</div>
            </div>
        `;
    }).join('');

    return `
        <div class="agent-flow-container">
            ${agentFlow}
        </div>
    `;
}

function renderWheelItemsAnalysis(wheel) {
    const activities = wheel.activities || [];
    const items = wheel.items || [];

    // If no activities, try to extract from items or create mock data
    if (activities.length === 0 && items.length === 0) {
        return `
            <div class="no-wheel-data">
                <div class="no-data-message">
                    <p>⚠️ No wheel activities data available in this benchmark run.</p>
                    <p>This could mean:</p>
                    <ul>
                        <li>The benchmark was run with mocked data</li>
                        <li>The wheel generation failed</li>
                        <li>The data structure has changed</li>
                    </ul>
                    <p>Check the raw results tab for more details.</p>
                </div>
            </div>
        `;
    }

    // If we have items but no activities, try to use items data
    const dataToUse = activities.length > 0 ? activities : items;

    const itemsHtml = dataToUse.map((activity, index) => {
        const item = items[index] || activity; // Use activity as fallback if no separate items
        const domain = activity.domain || item.domain || 'Unknown';
        const percentage = item.percentage || activity.percentage || (100 / dataToUse.length);

        return `
            <div class="wheel-item-analysis" data-activity-id="${activity.id}">
                <div class="wheel-item-header">
                    <h5>${activity.name || `Activity ${index + 1}`}</h5>
                    <span class="domain-badge ${domain.toLowerCase()}">${domain}</span>
                    <span class="percentage-badge">${percentage.toFixed(1)}%</span>
                </div>
                <div class="wheel-item-details">
                    <p><strong>Description:</strong> ${activity.description || 'No description available'}</p>
                    <p><strong>Instructions:</strong> ${activity.instructions || 'No instructions available'}</p>
                    ${activity.value_proposition ? `<p><strong>Value:</strong> ${activity.value_proposition}</p>` : ''}
                </div>
                <div class="tailoring-process">
                    <button class="show-tailoring-btn" onclick="showTailoringProcess('${activity.id}')">
                        🔍 Show Tailoring Process
                    </button>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section wheel-items-section">
            <h3>🎯 Wheel Items Deep Dive</h3>
            <p class="section-description">Click on any item to see how it was tailored from generic activities</p>
            <div class="wheel-items-grid">
                ${itemsHtml}
            </div>
        </div>
    `;
}

function renderAgentCommunicationTimeline(agentCommunications) {
    console.log('🔍 DEBUG: renderAgentCommunicationTimeline called with:', agentCommunications);
    console.log('🔍 DEBUG: window.currentWorkflowModalData:', window.currentWorkflowModalData);

    // Try multiple data sources for agent communications with improved logic
    let agents = [];

    // Handle case where agentCommunications is a direct array
    if (Array.isArray(agentCommunications)) {
        agents = agentCommunications;
        console.log('🔍 DEBUG: Using direct array format, found', agents.length, 'agents');
    } else if (agentCommunications.agents) {
        agents = agentCommunications.agents;
        console.log('🔍 DEBUG: Using .agents property, found', agents.length, 'agents');
    }

    // If no agents found, try alternative data sources
    if (agents.length === 0 && window.currentWorkflowModalData?.agent_communications) {
        const directComms = window.currentWorkflowModalData.agent_communications;
        if (Array.isArray(directComms)) {
            agents = directComms;
            console.log('🔍 DEBUG: Using window.currentWorkflowModalData.agent_communications array, found', agents.length, 'agents');
        } else if (directComms.agents) {
            agents = directComms.agents;
            console.log('🔍 DEBUG: Using window.currentWorkflowModalData.agent_communications.agents, found', agents.length, 'agents');
        }
    }

    // Check in raw_results for agent communications
    if (agents.length === 0 && window.currentWorkflowModalData?.raw_results?.last_output?.agent_communications) {
        const rawComms = window.currentWorkflowModalData.raw_results.last_output.agent_communications;
        if (Array.isArray(rawComms)) {
            agents = rawComms;
            console.log('🔍 DEBUG: Using raw_results.last_output.agent_communications, found', agents.length, 'agents');
        }
    }

    console.log('🔍 Agent timeline - final agents found:', agents.length);
    console.log('🔍 Agent timeline - first agent sample:', agents[0]);

    if (agents.length === 0) {
        return `
            <div class="no-timeline-data">
                <h4>⏱️ Workflow Execution Timeline</h4>
                <div class="timeline-empty-message">
                    <p>⚠️ No agent communication timeline data available.</p>
                    <p>This could mean:</p>
                    <ul>
                        <li>The workflow was run with limited debugging data</li>
                        <li>The data structure has changed</li>
                        <li>Agent communications were not properly captured</li>
                    </ul>
                    <p>Check the raw results tab for more details.</p>
                </div>
            </div>
        `;
    }

    const timelineHtml = agents.map((comm, index) => {
        const timestamp = comm.timestamp ? new Date(comm.timestamp).toLocaleTimeString() : 'N/A';
        const duration = comm.duration_ms ? `${comm.duration_ms.toFixed(2)}ms` : 'N/A';
        const statusClass = comm.success !== false ? 'success' : 'error';
        const statusIcon = comm.success !== false ? '✅' : '❌';

        // Get performance metrics
        const tokenUsage = comm.performance_metrics?.token_usage || { input: 0, output: 0 };
        const toolCallCount = comm.performance_metrics?.tool_call_count || 0;
        const totalTokens = tokenUsage.input + tokenUsage.output;

        // Determine if this agent used real LLM (has token usage > 0)
        const usedRealLLM = totalTokens > 0;
        const llmIndicator = usedRealLLM ? '🧠' : '🎭';
        const llmTooltip = usedRealLLM ? 'Used real LLM' : 'Used mock/tools only';

        return `
            <div class="timeline-event ${statusClass}" data-agent="${comm.agent}">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <h5>${comm.agent} ${statusIcon}</h5>
                        <span class="timeline-time">${timestamp}</span>
                        <span class="timeline-duration">${duration}</span>
                    </div>
                    <div class="timeline-stage">${comm.stage || 'Unknown Stage'}</div>
                    <div class="timeline-metrics">
                        <span class="metric-item" title="${llmTooltip}">
                            ${llmIndicator} ${totalTokens > 0 ? `${totalTokens} tokens` : 'No LLM calls'}
                        </span>
                        <span class="metric-item" title="Tool calls made">
                            🔧 ${toolCallCount} tools
                        </span>
                    </div>
                    <div class="timeline-details">
                        <button class="show-details-btn" onclick="showAgentDetails('${comm.agent}', ${index})">
                            📋 Show Input/Output
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section agent-timeline-section">
            <h3>⏱️ Agent Communication Timeline</h3>
            <p class="section-description">Chronological view of agent execution with input/output details</p>
            <div class="agent-timeline">
                ${timelineHtml}
            </div>
        </div>
    `;
}

function renderToolCallAnalysis(operations, agentCommunications) {
    const toolCalls = operations.tool_breakdown || {};
    const totalCalls = operations.total_tool_calls || 0;

    if (totalCalls === 0) {
        return '<div class="no-tool-data">No tool call data available</div>';
    }

    const toolsHtml = Object.entries(toolCalls).map(([toolName, count]) => {
        const percentage = ((count / totalCalls) * 100).toFixed(1);

        return `
            <div class="tool-analysis-item">
                <div class="tool-header">
                    <h5>🔧 ${toolName}</h5>
                    <span class="tool-count">${count} calls</span>
                    <span class="tool-percentage">${percentage}%</span>
                </div>
                <div class="tool-details">
                    <button class="show-tool-calls-btn" onclick="showToolCallDetails('${toolName}')">
                        📋 Show All Calls
                    </button>
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="modal-section tool-calls-section">
            <h3>🔧 Tool Call Analysis</h3>
            <p class="section-description">Detailed breakdown of tool usage during wheel generation</p>
            <div class="tool-calls-summary">
                <div class="summary-stat">
                    <span class="stat-value">${totalCalls}</span>
                    <span class="stat-label">Total Tool Calls</span>
                </div>
                <div class="summary-stat">
                    <span class="stat-value">${Object.keys(toolCalls).length}</span>
                    <span class="stat-label">Unique Tools</span>
                </div>
            </div>
            <div class="tool-calls-grid">
                ${toolsHtml}
            </div>
        </div>
    `;
}

// Wheel Generation Details Rendering Function - Make it globally accessible
window.renderWorkflowDetails = async function(workflowModalBody, data, runId) {
    // The modal body element is now passed as an argument
    if (!workflowModalBody) {
        console.error('Workflow modal body element not provided to renderWorkflowDetails');
        return;
    }

    console.log('🔍 DEBUG: renderWorkflowDetails called with data:', JSON.stringify(data, null, 2));
    console.log('🔍 DEBUG: Agent communications structure:', data.agent_communications);

    // Store data globally for access by other functions (like technical analysis)
    window.currentWorkflowModalData = data;
    window.currentWorkflowRawResults = data.raw_results || {};

    // Extract data from the benchmark result structure with multiple fallback paths
    const rawResults = data.raw_results || {};
    const lastOutput = rawResults.last_output || {};
    const outputData = lastOutput.output_data || {};

    // Try multiple paths for wheel data
    let wheel = outputData.wheel || {};
    if (!wheel.activities && !wheel.items) {
        // Try alternative paths
        wheel = rawResults.wheel || data.wheel || {};
    }

    const ethicalValidation = outputData.ethical_validation || {};
    const strategyFramework = outputData.strategy_framework || {};
    const psychAssessment = outputData.psychological_assessment || {};
    const semanticQuality = rawResults.semantic_quality || {};
    const performance = rawResults.performance || {};
    const operations = rawResults.operations || {};
    const actualModes = lastOutput.actual_execution_modes || {};

    // Try multiple paths for agent communications
    let agentCommunications = data.agent_communications || {};

    // Handle different data structures for agent communications
    if (Array.isArray(agentCommunications)) {
        // If agent_communications is a direct array, wrap it in the expected structure
        agentCommunications = { agents: agentCommunications };
    } else if (!agentCommunications.agents) {
        // Try alternative paths
        let altComms = rawResults.agent_communications || lastOutput.agent_communications || {};
        if (Array.isArray(altComms)) {
            agentCommunications = { agents: altComms };
        } else {
            agentCommunications = altComms;
        }
    }

    // Phase 2 Advanced Monitoring Data
    const stateSnapshots = agentCommunications.state_snapshots || [];
    const toolCallSequences = agentCommunications.tool_call_sequences || [];
    const performanceCorrelations = agentCommunications.performance_correlations || [];
    const advancedMonitoring = agentCommunications.advanced_monitoring || false;
    const performanceInsights = agentCommunications.performance_insights || {};
    const debuggingRecommendations = agentCommunications.debugging_recommendations || [];
    const dataModelVersion = agentCommunications.data_model_version || '1.0.0';
    const enhancedDebugging = agentCommunications.enhanced_debugging || false;

    // Debug logging to understand the actual data structure
    console.log('🔍 Debug - Raw data structure:', {
        hasRawResults: !!data.raw_results,
        hasLastOutput: !!lastOutput,
        hasOutputData: !!outputData,
        hasWheel: !!wheel,
        wheelKeys: Object.keys(wheel),
        hasAgentComms: !!agentCommunications,
        agentCommsKeys: Object.keys(agentCommunications),
        agentCommsAgents: agentCommunications.agents?.length || 0
    });

    // Calculate metrics
    const meanDuration = performance.mean_duration_s ? (performance.mean_duration_s * 1000).toFixed(2) : 'N/A';
    const successRate = performance.success_rate !== null ? (performance.success_rate * 100).toFixed(1) + '%' : 'N/A';
    const semanticScore = semanticQuality.overall_score ? semanticQuality.overall_score.toFixed(2) : 'N/A';
    const totalTokens = (operations.total_input_tokens || 0) + (operations.total_output_tokens || 0);
    const agentCount = Object.keys(actualModes).length;
    const wheelItems = wheel.items?.length || 0;
    const trustPhase = psychAssessment.trust_phase?.phase || 'Unknown';
    const ethicalStatus = ethicalValidation.wheel_validation?.status || 'Unknown';

    workflowModalBody.innerHTML = `
        <div class="wheel-generation-evaluation-header">
            <div class="evaluation-type-badge wheel-generation">WHEEL GENERATION</div>
            <div class="evaluation-title">
                <h3>🎡 ${data.workflow_type || 'Wheel Generation'} Workflow Analysis</h3>
                <p class="scenario-info">${data.scenario || 'N/A'}</p>
                <div class="evaluation-focus">
                    🔍 Focus: Activity Tailoring Process & Agent Coordination
                    <span class="debugging-indicator enabled">
                        ✅ Debug Mode Active
                    </span>
                </div>
                <div class="evaluation-meta">
                    <span class="run-id">Run ID: ${runId}</span>
                    <span class="execution-date">${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</span>
                    <span class="workflow-version">Version: ${data.agent_version || 'N/A'}</span>
                </div>
            </div>
            <!-- Wheel Generation Agent Flow -->
            <div class="wheel-generation-flow">
                <h4>🔄 Agent Coordination Flow</h4>
                <div class="agent-flow-visualization">
                    ${renderAgentFlowVisualization(agentCommunications, actualModes)}
                </div>
            </div>

            <div class="wheel-generation-insights">
                <h4>🎡 Wheel Generation Analysis</h4>
                <div class="insight-cards">
                    <div class="insight-card wheel-output">
                        <div class="card-icon">🎡</div>
                        <div class="card-content">
                            <h5>Wheel Output</h5>
                            <p>Items Generated: ${wheelItems}</p>
                            <p>Trust Phase: ${trustPhase}</p>
                            <p>Ethics Status: ${ethicalStatus}</p>
                            <p class="optimization-tip">💡 ${wheelItems >= 6 && wheelItems <= 8 ? 'Optimal item count' : 'Review item count strategy'}</p>
                        </div>
                    </div>
                    <div class="insight-card tailoring-process">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <h5>Activity Tailoring</h5>
                            <p>Generic → Tailored: ${wheelItems}</p>
                            <p>Domains Covered: ${wheel.activities ? [...new Set(wheel.activities.map(a => a.domain))].length : 'N/A'}</p>
                            <p>Customization Level: High</p>
                            <p class="optimization-tip">💡 Click wheel items below to see tailoring process</p>
                        </div>
                    </div>
                    <div class="insight-card agent-performance">
                        <div class="card-icon">🤖</div>
                        <div class="card-content">
                            <h5>Agent Performance</h5>
                            <p>Agents Used: ${agentCount}</p>
                            <p>Duration: ${meanDuration}ms</p>
                            <p>Success Rate: ${successRate}</p>
                            <p class="optimization-tip">💡 ${performance.success_rate === 1 ? 'All agents executed successfully' : 'Check agent failures below'}</p>
                        </div>
                    </div>
                    <div class="insight-card tool-usage">
                        <div class="card-icon">🔧</div>
                        <div class="card-content">
                            <h5>Tool Usage</h5>
                            <p>Tool Calls: ${operations.total_tool_calls || 0}</p>
                            <p>Real Mode: ${lastOutput.real_llm_used ? '✅' : '❌'}</p>
                            <p>Tokens: ${totalTokens.toLocaleString()}</p>
                            <p class="optimization-tip">💡 ${operations.total_tool_calls > 10 ? 'High tool usage - check efficiency' : 'Efficient tool usage'}</p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Performance Metrics -->
                <div class="performance-breakdown">
                    <h4>📊 Detailed Performance Breakdown</h4>
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">⏱️</span>
                                <span class="metric-title">Execution Timing</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Mean Duration:</span>
                                    <span class="metric-value">${meanDuration}ms</span>
                                </div>
                                <div class="metric-row">
                                    <span>Success Rate:</span>
                                    <span class="metric-value ${performance.success_rate === 1 ? 'success' : 'warning'}">${successRate}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Agent Count:</span>
                                    <span class="metric-value">${agentCount}</span>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">🧠</span>
                                <span class="metric-title">LLM Usage</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Total Tokens:</span>
                                    <span class="metric-value">${totalTokens.toLocaleString()}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Input/Output:</span>
                                    <span class="metric-value">${(operations.total_input_tokens || 0).toLocaleString()}/${(operations.total_output_tokens || 0).toLocaleString()}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Real LLM:</span>
                                    <span class="metric-value ${lastOutput.real_llm_used ? 'success' : 'info'}">${lastOutput.real_llm_used ? 'Yes' : 'Mock'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <span class="metric-icon">📈</span>
                                <span class="metric-title">Quality Metrics</span>
                            </div>
                            <div class="metric-details">
                                <div class="metric-row">
                                    <span>Semantic Score:</span>
                                    <span class="metric-value ${semanticScore !== 'N/A' && parseFloat(semanticScore) > 0.7 ? 'success' : semanticScore !== 'N/A' && parseFloat(semanticScore) > 0.5 ? 'warning' : 'error'}">${semanticScore}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Wheel Items:</span>
                                    <span class="metric-value">${wheelItems}</span>
                                </div>
                                <div class="metric-row">
                                    <span>Ethics Status:</span>
                                    <span class="metric-value ${ethicalStatus === 'Approved' ? 'success' : 'warning'}">${ethicalStatus}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="workflow-summary-cards">
            <div class="workflow-summary-card">
                <h4>⏱️ Execution Time</h4>
                <div class="value">${meanDuration}</div>
                <div class="label">milliseconds</div>
            </div>
            <div class="workflow-summary-card">
                <h4>🎯 Semantic Quality</h4>
                <div class="value">${semanticScore}</div>
                <div class="label">overall score</div>
            </div>
            <div class="workflow-summary-card">
                <h4>🧠 Token Usage</h4>
                <div class="value">${totalTokens.toLocaleString()}</div>
                <div class="label">total tokens</div>
            </div>
            <div class="workflow-summary-card">
                <h4>✅ Success Rate</h4>
                <div class="value">${successRate}</div>
                <div class="label">completion rate</div>
            </div>
        </div>

        <!-- Phase 2 Advanced Monitoring Panel -->
        ${advancedMonitoring ? `
        <div class="phase2-monitoring-panel">
            <h3>🚀 Phase 2 Advanced Monitoring</h3>
            <p>Real-time state tracking, tool call sequence analysis, and performance correlation insights</p>

            <div class="monitoring-grid">
                <div class="monitoring-card">
                    <h4>📊 Performance Insights</h4>
                    <div class="monitoring-metric">
                        <span class="metric-label">Efficiency Score:</span>
                        <span class="metric-value ${performanceInsights.workflow_efficiency?.efficiency_score > 0.8 ? 'good' : performanceInsights.workflow_efficiency?.efficiency_score > 0.6 ? 'warning' : 'error'}">${performanceInsights.workflow_efficiency?.efficiency_score ? (performanceInsights.workflow_efficiency.efficiency_score * 100).toFixed(1) + '%' : 'N/A'}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Total Execution:</span>
                        <span class="metric-value">${performanceInsights.workflow_efficiency?.total_execution_time_ms ? (performanceInsights.workflow_efficiency.total_execution_time_ms / 1000).toFixed(2) + 's' : 'N/A'}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Bottleneck Agent:</span>
                        <span class="metric-value">${performanceInsights.bottleneck_analysis?.slowest_agent || 'N/A'}</span>
                    </div>
                </div>

                <div class="monitoring-card">
                    <h4>🔄 State Consistency</h4>
                    <div class="monitoring-metric">
                        <span class="metric-label">State Snapshots:</span>
                        <span class="metric-value">${stateSnapshots.length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Valid States:</span>
                        <span class="metric-value good">${stateSnapshots.filter(s => s.validation_status === 'valid').length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Inconsistencies:</span>
                        <span class="metric-value ${stateSnapshots.filter(s => s.validation_status === 'inconsistent').length > 0 ? 'error' : 'good'}">${stateSnapshots.filter(s => s.validation_status === 'inconsistent').length}</span>
                    </div>
                </div>

                <div class="monitoring-card">
                    <h4>🔧 Tool Sequences</h4>
                    <div class="monitoring-metric">
                        <span class="metric-label">Sequences Analyzed:</span>
                        <span class="metric-value">${toolCallSequences.length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Parallel Calls:</span>
                        <span class="metric-value">${toolCallSequences.filter(s => s.sequence_type === 'parallel').length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Dependencies:</span>
                        <span class="metric-value">${toolCallSequences.reduce((sum, s) => sum + s.dependencies.length, 0)}</span>
                    </div>
                </div>

                <div class="monitoring-card">
                    <h4>📈 Correlations</h4>
                    <div class="monitoring-metric">
                        <span class="metric-label">Correlations Found:</span>
                        <span class="metric-value">${performanceCorrelations.length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Strong Positive:</span>
                        <span class="metric-value good">${performanceCorrelations.filter(c => c.correlation_type === 'positive' && Math.abs(c.correlation_coefficient) > 0.7).length}</span>
                    </div>
                    <div class="monitoring-metric">
                        <span class="metric-label">Strong Negative:</span>
                        <span class="metric-value warning">${performanceCorrelations.filter(c => c.correlation_type === 'negative' && Math.abs(c.correlation_coefficient) > 0.7).length}</span>
                    </div>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- State Consistency Timeline -->
        ${stateSnapshots.length > 0 ? `
        <div class="state-consistency-timeline">
            <h4>🔄 State Consistency Timeline</h4>
            <p>Real-time validation of workflow state transitions</p>
            ${stateSnapshots.map(snapshot => `
                <div class="state-snapshot ${snapshot.validation_status}">
                    <div class="snapshot-indicator ${snapshot.validation_status}"></div>
                    <div class="snapshot-info">
                        <div class="snapshot-agent">${snapshot.agent}</div>
                        <div class="snapshot-stage">${snapshot.stage}</div>
                    </div>
                    <div class="snapshot-timestamp">${new Date(snapshot.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Tool Call Sequence Analysis -->
        ${toolCallSequences.length > 0 ? `
        <div class="tool-sequence-analysis">
            <h4>🔧 Tool Call Sequence Analysis</h4>
            <p>Performance patterns and dependencies in tool execution</p>
            ${toolCallSequences.map(sequence => `
                <div class="tool-sequence">
                    <span class="sequence-type-badge ${sequence.sequence_type}">${sequence.sequence_type}</span>
                    <div class="sequence-tools">${sequence.tool_calls.length} tools: ${sequence.dependencies.join(', ') || 'No dependencies'}</div>
                    <div class="sequence-performance">
                        <div class="sequence-duration">${sequence.total_duration_ms.toFixed(2)}ms</div>
                        <div class="sequence-efficiency">Efficiency: ${sequence.performance_impact.efficiency_score?.toFixed(2) || 'N/A'}</div>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Performance Correlation Matrix -->
        ${performanceCorrelations.length > 0 ? `
        <div class="correlation-matrix">
            <h4>📊 Performance Correlation Matrix</h4>
            <p>Statistical relationships between system components</p>
            ${performanceCorrelations.map(correlation => `
                <div class="correlation-item">
                    <div class="correlation-components">${correlation.component_a} ↔ ${correlation.component_b}</div>
                    <div class="correlation-coefficient">
                        <span class="correlation-value ${correlation.correlation_type}">${correlation.correlation_coefficient.toFixed(3)}</span>
                        <div class="correlation-bar">
                            <div class="correlation-fill ${correlation.correlation_type}" style="width: ${Math.abs(correlation.correlation_coefficient) * 100}%"></div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Performance Insights Panel -->
        ${Object.keys(performanceInsights).length > 0 ? `
        <div class="performance-insights">
            <h3>💡 Performance Insights</h3>
            <div class="insights-grid">
                ${performanceInsights.workflow_efficiency ? `
                <div class="insight-card">
                    <h5>⚡ Workflow Efficiency</h5>
                    <div class="insight-metric">${(performanceInsights.workflow_efficiency.efficiency_score * 100).toFixed(1)}%</div>
                    <div class="insight-description">Overall workflow execution efficiency based on success rate, performance, and error metrics</div>
                </div>
                ` : ''}

                ${performanceInsights.bottleneck_analysis ? `
                <div class="insight-card">
                    <h5>🚧 Bottleneck Analysis</h5>
                    <div class="insight-metric">${performanceInsights.bottleneck_analysis.bottleneck_percentage?.toFixed(1) || 'N/A'}%</div>
                    <div class="insight-description">Slowest agent: ${performanceInsights.bottleneck_analysis.slowest_agent} (${performanceInsights.bottleneck_analysis.slowest_duration_ms?.toFixed(2) || 'N/A'}ms)</div>
                </div>
                ` : ''}

                ${performanceInsights.resource_utilization ? `
                <div class="insight-card">
                    <h5>🧠 Resource Utilization</h5>
                    <div class="insight-metric">${performanceInsights.resource_utilization.total_tokens?.toLocaleString() || 'N/A'}</div>
                    <div class="insight-description">${performanceInsights.resource_utilization.total_llm_calls || 'N/A'} LLM calls, avg ${performanceInsights.resource_utilization.avg_tokens_per_call?.toFixed(0) || 'N/A'} tokens/call</div>
                </div>
                ` : ''}

                ${performanceInsights.quality_metrics ? `
                <div class="insight-card">
                    <h5>✅ Quality Metrics</h5>
                    <div class="insight-metric">${(performanceInsights.quality_metrics.success_rate * 100).toFixed(1)}%</div>
                    <div class="insight-description">${performanceInsights.quality_metrics.error_count || 0} errors, ${performanceInsights.quality_metrics.state_consistency_issues || 0} consistency issues</div>
                </div>
                ` : ''}
            </div>
        </div>
        ` : ''}

        <!-- Debugging Recommendations -->
        ${debuggingRecommendations.length > 0 ? `
        <div class="debugging-recommendations">
            <h4>🔍 Debugging Recommendations</h4>
            ${debuggingRecommendations.map(rec => `
                <div class="recommendation-item ${rec.priority}">
                    <div class="recommendation-header">
                        <div class="recommendation-title">${rec.title}</div>
                        <span class="recommendation-priority ${rec.priority}">${rec.priority}</span>
                    </div>
                    <div class="recommendation-description">${rec.description}</div>
                    <div class="recommendation-suggestion">${rec.suggestion}</div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Wheel Items Deep Dive -->
        ${wheelItems > 0 ? renderWheelItemsAnalysis(wheel) : ''}

        <!-- Agent Communication Timeline -->
        ${renderAgentCommunicationTimeline(agentCommunications)}

        <!-- Tool Call Analysis -->
        ${renderToolCallAnalysis(operations, agentCommunications)}

        <!-- Wheel Generation Output -->
        ${wheelItems > 0 ? `
        <div class="modal-section wheel-output-section">
            <h3>🎡 Generated Wheel Output</h3>
            <div class="wheel-summary">
                <div class="wheel-meta">
                    <div class="wheel-info">
                        <h4>${wheel.metadata?.name || 'Activity Wheel'}</h4>
                        <p>Trust Phase: <span class="trust-phase ${trustPhase.toLowerCase()}">${trustPhase}</span></p>
                        <p>User ID: <code>${wheel.user_id || 'N/A'}</code></p>
                        <p>Strategy: <code>${wheel.metadata?.strategy_id || 'N/A'}</code></p>
                    </div>
                    <div class="wheel-stats">
                        <div class="stat-item">
                            <span class="stat-value">${wheelItems}</span>
                            <span class="stat-label">Activities</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${new Set(wheel.items?.map(item => item.domain) || []).size}</span>
                            <span class="stat-label">Domains</span>
                        </div>
                    </div>
                </div>
                <div class="wheel-activities">
                    ${renderWheelActivities(wheel)}
                </div>
            </div>
        </div>
        ` : ''}

        <!-- Psychological Assessment -->
        ${Object.keys(psychAssessment).length > 0 ? `
        <div class="modal-section psychological-section">
            <h3>🧠 Psychological Assessment</h3>
            <div class="psych-analysis">
                <div class="trust-analysis">
                    <h4>🤝 Trust Phase Analysis</h4>
                    <div class="trust-metrics">
                        <div class="trust-metric">
                            <span class="metric-label">Current Phase:</span>
                            <span class="metric-value trust-phase ${trustPhase.toLowerCase()}">${trustPhase}</span>
                        </div>
                        <div class="trust-metric">
                            <span class="metric-label">Trust Level:</span>
                            <span class="metric-value">${psychAssessment.trust_phase?.trust_level || 'N/A'}</span>
                        </div>
                        <div class="trust-metric">
                            <span class="metric-label">Confidence:</span>
                            <span class="metric-value">${psychAssessment.trust_phase?.confidence ? (psychAssessment.trust_phase.confidence * 100).toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                    </div>
                </div>
                ${renderPsychologicalDetails(psychAssessment)}
            </div>
        </div>
        ` : ''}

        <!-- Ethical Validation -->
        ${Object.keys(ethicalValidation).length > 0 ? `
        <div class="modal-section ethical-section">
            <h3>⚖️ Ethical Validation</h3>
            <div class="ethical-analysis">
                <div class="validation-status">
                    <div class="status-header">
                        <h4>🛡️ Validation Status</h4>
                        <span class="status-badge ${ethicalStatus.toLowerCase()}">${ethicalStatus}</span>
                    </div>
                    <div class="validation-metrics">
                        <div class="validation-metric">
                            <span class="metric-label">Confidence:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.confidence ? (ethicalValidation.wheel_validation.confidence * 100).toFixed(1) + '%' : 'N/A'}</span>
                        </div>
                        <div class="validation-metric">
                            <span class="metric-label">Domain Balance:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.domain_balance || 'N/A'}</span>
                        </div>
                        <div class="validation-metric">
                            <span class="metric-label">Challenge Calibration:</span>
                            <span class="metric-value">${ethicalValidation.wheel_validation?.challenge_calibration || 'N/A'}</span>
                        </div>
                    </div>
                </div>
                ${renderEthicalDetails(ethicalValidation)}
            </div>
        </div>
        ` : ''}

        <!-- Strategy Framework -->
        ${Object.keys(strategyFramework).length > 0 ? `
        <div class="modal-section strategy-section">
            <h3>🎯 Strategy Framework</h3>
            <div class="strategy-analysis">
                ${renderStrategyDetails(strategyFramework)}
            </div>
        </div>
        ` : ''}

        <!-- Semantic Quality Evaluation -->
        ${Object.keys(semanticQuality).length > 0 ? `
        <div class="modal-section semantic-section">
            <h3>📊 Semantic Quality Evaluation</h3>
            <div class="semantic-analysis">
                ${renderSemanticQualityDetails(semanticQuality)}
            </div>
        </div>
        ` : ''}

        <!-- Execution Mode Analysis -->
        ${Object.keys(actualModes).length > 0 ? `
        <div class="modal-section execution-modes-section">
            <h3>🔧 Execution Mode Analysis</h3>
            <div class="execution-modes">
                ${renderExecutionModes(actualModes, lastOutput)}
            </div>
        </div>
        ` : ''}

        <!-- Enhanced Chronological Event Analysis -->
        ${data.agent_communications && data.agent_communications.enabled ? `
        <div class="workflow-event-tree">
            <div class="event-tree-header">
                <h3>🌳 Interactive Workflow Event Tree</h3>
                <div class="tree-controls">
                    <button class="tree-control-btn" onclick="expandAllEvents()">📖 Expand All</button>
                    <button class="tree-control-btn secondary" onclick="collapseAllEvents()">📕 Collapse All</button>
                    <button class="tree-control-btn secondary" onclick="showOnlyIssues()">⚠️ Issues Only</button>
                    <button class="tree-control-btn secondary" onclick="showAllEvents()">👁️ Show All</button>
                    <button class="tree-control-btn secondary" onclick="toggleTimelineView()">📊 Timeline View</button>
                    <button class="tree-control-btn secondary" onclick="toggleTechnicalMode()">🔧 Technical Mode</button>
                </div>
            </div>

            <!-- Timeline Visualization -->
            <div id="timeline-visualization" class="timeline-visualization" style="display: none;">
                <div class="timeline-header">
                    <h4>📈 Execution Timeline</h4>
                    <div class="timeline-controls">
                        <button class="timeline-btn" onclick="zoomTimeline('in')">🔍 Zoom In</button>
                        <button class="timeline-btn" onclick="zoomTimeline('out')">🔍 Zoom Out</button>
                        <button class="timeline-btn" onclick="resetTimelineZoom()">🔄 Reset</button>
                    </div>
                </div>
                <div id="timeline-chart" class="timeline-chart">
                    <!-- Timeline chart will be rendered here -->
                </div>
            </div>

            <div class="event-tree-container">
                <div class="event-tree-line"></div>
                <div id="workflow-events-container">
                    <!-- Events will be rendered here -->
                </div>
            </div>
        </div>
        ` : ''}

        <!-- Raw Data Section -->
        <div class="modal-section raw-data-section">
            <div class="raw-data-header">
                <h3>📄 Raw Benchmark Data</h3>
                <button class="copy-raw-data-btn" onclick="copyFullRawData()" title="Copy full raw result to clipboard">
                    📋 Copy Full Result
                </button>
            </div>
            <div class="raw-data-tabs">
                <button class="tab-btn active" onclick="showRawDataTab('performance')">Performance</button>
                <button class="tab-btn" onclick="showRawDataTab('operations')">Operations</button>
                <button class="tab-btn" onclick="showRawDataTab('full')">Full Results</button>
            </div>
            <div class="raw-data-content">
                <div id="raw-performance" class="raw-data-tab active">
                    <div class="raw-data-controls">
                        <button class="copy-tab-data-btn" onclick="copyTabData('performance')" title="Copy performance data">📋 Copy</button>
                    </div>
                    <pre class="json-viewer">${JSON.stringify(performance, null, 2)}</pre>
                </div>
                <div id="raw-operations" class="raw-data-tab">
                    <div class="raw-data-controls">
                        <button class="copy-tab-data-btn" onclick="copyTabData('operations')" title="Copy operations data">📋 Copy</button>
                    </div>
                    <pre class="json-viewer">${JSON.stringify(operations, null, 2)}</pre>
                </div>
                <div id="raw-full" class="raw-data-tab">
                    <div class="raw-data-controls">
                        <button class="copy-tab-data-btn" onclick="copyTabData('full')" title="Copy full results">📋 Copy</button>
                    </div>
                    <pre class="json-viewer">${JSON.stringify(rawResults, null, 2)}</pre>
                </div>
            </div>
        </div>
    `;

    // Store timeline data for vertical timeline modal
    currentTimelineData = {
        agent_communications: agentCommunications,
        performance: performance,
        operations: operations,
        execution_date: data.execution_date,
        run_id: runId
    };

    // Enable timeline button
    const timelineToggleBtn = document.getElementById('show-timeline-btn');
    if (timelineToggleBtn) {
        timelineToggleBtn.style.display = 'block';
        timelineToggleBtn.disabled = false;
    }

    // Render the interactive event tree
    if (data.agent_communications && data.agent_communications.agents) {
        renderInteractiveEventTree(data.agent_communications.agents);
        renderTimelineVisualization(data.agent_communications.agents);
    }
}

// Helper Functions for Rendering Different Sections

function renderWheelActivities(wheel) {
    if (!wheel.items || wheel.items.length === 0) {
        return '<p>No activities generated</p>';
    }

    return `
        <div class="activities-grid">
            ${wheel.items.map((item, index) => `
                <div class="activity-card" style="border-left: 4px solid ${item.color}">
                    <div class="activity-header">
                        <h5>${item.title}</h5>
                        <span class="domain-badge ${item.domain}">${item.domain}</span>
                    </div>
                    <div class="activity-details">
                        <p><strong>Description:</strong> ${wheel.activities?.[index]?.description || 'N/A'}</p>
                        <div class="activity-meta">
                            <span>Duration: ${wheel.activities?.[index]?.duration || 'N/A'}min</span>
                            <span>Challenge: ${wheel.activities?.[index]?.challenge_level || 'N/A'}/100</span>
                            <span>Percentage: ${item.percentage?.toFixed(1) || 'N/A'}%</span>
                        </div>
                    </div>
                    <div class="value-proposition">
                        <h6>💡 Value Proposition</h6>
                        <p>${wheel.value_propositions?.[item.id]?.growth_value || 'No value proposition available'}</p>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function renderPsychologicalDetails(psychAssessment) {
    const currentState = psychAssessment.current_state || {};
    const traitAnalysis = psychAssessment.trait_analysis || {};
    const beliefAnalysis = psychAssessment.belief_analysis || {};

    return `
        <div class="psych-details">
            <div class="current-state">
                <h4>🌟 Current State</h4>
                <div class="state-metrics">
                    <div class="state-metric">
                        <span class="metric-label">Energy Level:</span>
                        <span class="metric-value">${currentState.energy_level || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Stress Level:</span>
                        <span class="metric-value">${currentState.stress_level || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Emotional Balance:</span>
                        <span class="metric-value">${currentState.emotional_balance || 'N/A'}</span>
                    </div>
                    <div class="state-metric">
                        <span class="metric-label">Cognitive Load:</span>
                        <span class="metric-value">${currentState.cognitive_load || 'N/A'}</span>
                    </div>
                </div>
            </div>

            ${Object.keys(beliefAnalysis.core_beliefs || {}).length > 0 ? `
            <div class="belief-analysis">
                <h4>💭 Core Beliefs</h4>
                <div class="beliefs-grid">
                    ${Object.entries(beliefAnalysis.core_beliefs).map(([key, belief]) => `
                        <div class="belief-card">
                            <h5>${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</h5>
                            <div class="belief-strength">
                                <div class="strength-bar">
                                    <div class="strength-fill" style="width: ${(belief.strength * 100)}%"></div>
                                </div>
                                <span>${(belief.strength * 100).toFixed(0)}%</span>
                            </div>
                            <p>${belief.description}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${traitAnalysis.dominant_traits?.length > 0 ? `
            <div class="trait-analysis">
                <h4>🎭 Dominant Traits</h4>
                <div class="traits-list">
                    ${traitAnalysis.dominant_traits.map(trait => `
                        <span class="trait-badge">${trait}</span>
                    `).join('')}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderEthicalDetails(ethicalValidation) {
    const principles = ethicalValidation.ethical_rationales?.ethical_principles || {};
    const activityValidations = ethicalValidation.activity_validations || [];
    const safetyConsiderations = ethicalValidation.safety_considerations || {};

    return `
        <div class="ethical-details">
            ${Object.keys(principles).length > 0 ? `
            <div class="ethical-principles">
                <h4>📜 Ethical Principles</h4>
                <div class="principles-grid">
                    ${Object.entries(principles).map(([principle, description]) => `
                        <div class="principle-card">
                            <h5>${principle.charAt(0).toUpperCase() + principle.slice(1)}</h5>
                            <p>${description}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${activityValidations.length > 0 ? `
            <div class="activity-validations">
                <h4>✅ Activity Validations</h4>
                <div class="validations-summary">
                    <div class="validation-stats">
                        <div class="stat">
                            <span class="stat-value">${activityValidations.filter(v => v.status === 'Approved').length}</span>
                            <span class="stat-label">Approved</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${activityValidations.filter(v => v.concerns?.length > 0).length}</span>
                            <span class="stat-label">With Concerns</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">${(activityValidations.reduce((sum, v) => sum + (v.confidence || 0), 0) / activityValidations.length * 100).toFixed(0)}%</span>
                            <span class="stat-label">Avg Confidence</span>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}

            ${Object.keys(safetyConsiderations).length > 0 ? `
            <div class="safety-considerations">
                <h4>🛡️ Safety Considerations</h4>
                <div class="safety-details">
                    ${safetyConsiderations.vulnerability_areas?.length > 0 ? `
                    <div class="vulnerability-areas">
                        <h5>⚠️ Vulnerability Areas</h5>
                        <div class="vulnerability-list">
                            ${safetyConsiderations.vulnerability_areas.map(area => `
                                <span class="vulnerability-badge">${area}</span>
                            `).join('')}
                        </div>
                    </div>
                    ` : ''}

                    ${safetyConsiderations.challenge_boundaries ? `
                    <div class="challenge-boundaries">
                        <h5>🎯 Challenge Boundaries</h5>
                        <div class="boundaries-grid">
                            <div class="boundary-item">
                                <span class="boundary-label">Max Difficulty:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.max_difficulty_level || 'N/A'}</span>
                            </div>
                            <div class="boundary-item">
                                <span class="boundary-label">Max Duration:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.max_duration_minutes || 'N/A'} min</span>
                            </div>
                            <div class="boundary-item">
                                <span class="boundary-label">Exit Strategy:</span>
                                <span class="boundary-value">${safetyConsiderations.challenge_boundaries.exit_strategy_required ? '✅' : '❌'}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderStrategyDetails(strategyFramework) {
    const gapAnalysis = strategyFramework.gap_analysis || {};
    const growthAlignment = strategyFramework.growth_alignment || {};
    const domainDistribution = strategyFramework.domain_distribution || {};
    const selectionCriteria = strategyFramework.selection_criteria || {};

    return `
        <div class="strategy-details">
            ${Object.keys(gapAnalysis).length > 0 ? `
            <div class="gap-analysis">
                <h4>📊 Gap Analysis</h4>
                <div class="gap-metrics">
                    <div class="gap-metric">
                        <span class="metric-label">Primary Focus:</span>
                        <span class="metric-value">${gapAnalysis.overall_assessment?.primary_focus || 'N/A'}</span>
                    </div>
                    <div class="gap-metric">
                        <span class="metric-label">Average Challenge:</span>
                        <span class="metric-value">${gapAnalysis.overall_assessment?.average_challenge || 'N/A'}</span>
                    </div>
                    <div class="gap-metric">
                        <span class="metric-label">Trust Phase Impact:</span>
                        <span class="metric-value">${gapAnalysis.trust_phase_impact || 'N/A'}</span>
                    </div>
                </div>
            </div>
            ` : ''}

            ${domainDistribution.domains ? `
            <div class="domain-distribution">
                <h4>🎯 Domain Distribution</h4>
                <div class="domains-grid">
                    ${Object.entries(domainDistribution.domains).map(([key, domain]) => `
                        <div class="domain-card">
                            <h5>${domain.name}</h5>
                            <div class="domain-percentage">
                                <div class="percentage-bar">
                                    <div class="percentage-fill" style="width: ${domain.percentage}%"></div>
                                </div>
                                <span>${domain.percentage?.toFixed(1)}%</span>
                            </div>
                            <p class="domain-reason">${domain.reason}</p>
                        </div>
                    `).join('')}
                </div>
                <div class="distribution-summary">
                    <p><strong>Primary Domain:</strong> ${domainDistribution.summary?.primary_domain || 'N/A'}</p>
                    <p><strong>Rationale:</strong> ${domainDistribution.summary?.distribution_rationale || 'N/A'}</p>
                </div>
            </div>
            ` : ''}

            ${Object.keys(selectionCriteria).length > 0 ? `
            <div class="selection-criteria">
                <h4>⚙️ Selection Criteria</h4>
                <div class="criteria-sections">
                    ${selectionCriteria.time_criteria ? `
                    <div class="criteria-section">
                        <h5>⏰ Time Criteria</h5>
                        <div class="criteria-items">
                            <div class="criteria-item">
                                <span>Duration Range:</span>
                                <span>${selectionCriteria.time_criteria.min_duration}-${selectionCriteria.time_criteria.max_duration} min</span>
                            </div>
                            <div class="criteria-item">
                                <span>Preferred Time:</span>
                                <span>${selectionCriteria.time_criteria.preferred_time_of_day}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}

                    ${selectionCriteria.content_criteria ? `
                    <div class="criteria-section">
                        <h5>📝 Content Criteria</h5>
                        <div class="criteria-items">
                            <div class="criteria-item">
                                <span>Guidance Level:</span>
                                <span>${selectionCriteria.content_criteria.guidance_level}</span>
                            </div>
                            <div class="criteria-item">
                                <span>Complexity:</span>
                                <span>${selectionCriteria.content_criteria.complexity_level}</span>
                            </div>
                            <div class="criteria-item">
                                <span>Abstraction:</span>
                                <span>${selectionCriteria.content_criteria.abstraction_level}</span>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function renderSemanticQualityDetails(semanticQuality) {
    const evaluations = semanticQuality.evaluations || {};
    const meta = evaluations._meta || {};
    const overallScore = semanticQuality.overall_score;

    return `
        <div class="semantic-details">
            <div class="semantic-overview">
                <h4>📊 Quality Overview</h4>
                <div class="quality-metrics">
                    <div class="quality-metric">
                        <span class="metric-label">Overall Score:</span>
                        <span class="metric-value score-${getScoreClass(overallScore)}">${overallScore?.toFixed(2) || 'N/A'}</span>
                    </div>
                    <div class="quality-metric">
                        <span class="metric-label">Primary Model:</span>
                        <span class="metric-value">${meta.primary_model || 'N/A'}</span>
                    </div>
                    <div class="quality-metric">
                        <span class="metric-label">Trust Phase:</span>
                        <span class="metric-value">${meta.phase || 'N/A'}</span>
                    </div>
                </div>
            </div>

            ${meta.criteria_dimensions?.length > 0 ? `
            <div class="evaluation-dimensions">
                <h4>📏 Evaluation Dimensions</h4>
                <div class="dimensions-list">
                    ${meta.criteria_dimensions.map(dimension => `
                        <span class="dimension-badge">${dimension}</span>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            ${Object.keys(evaluations).filter(key => key !== '_meta').length > 0 ? `
            <div class="model-evaluations">
                <h4>🤖 Model Evaluations</h4>
                ${Object.entries(evaluations).filter(([key]) => key !== '_meta').map(([model, evaluation]) => `
                    <div class="model-evaluation">
                        <h5>${model}</h5>
                        <div class="model-score">
                            <span class="score-label">Overall Score:</span>
                            <span class="score-value score-${getScoreClass(evaluation.overall_score)}">${evaluation.overall_score?.toFixed(2) || 'N/A'}</span>
                        </div>

                        ${evaluation.dimensions ? `
                        <div class="dimension-scores">
                            ${Object.entries(evaluation.dimensions).map(([dimension, data]) => `
                                <div class="dimension-score">
                                    <div class="dimension-header">
                                        <span class="dimension-name">${dimension}</span>
                                        <span class="dimension-value score-${getScoreClass(data.score)}">${data.score?.toFixed(2) || 'N/A'}</span>
                                    </div>
                                    <p class="dimension-reasoning">${data.reasoning || 'No reasoning provided'}</p>
                                </div>
                            `).join('')}
                        </div>
                        ` : ''}

                        ${evaluation.tone_analysis ? `
                        <div class="tone-analysis">
                            <h6>🎭 Tone Analysis</h6>
                            <div class="tone-score">
                                <span>Score: ${evaluation.tone_analysis.score?.toFixed(2) || 'N/A'}</span>
                            </div>
                            <p>${evaluation.tone_analysis.reasoning || 'No tone analysis available'}</p>
                        </div>
                        ` : ''}

                        <div class="overall-reasoning">
                            <h6>💭 Overall Assessment</h6>
                            <p>${evaluation.overall_reasoning || 'No overall reasoning provided'}</p>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : ''}
        </div>
    `;
}

function renderExecutionModes(actualModes, lastOutput) {
    const requestedMode = lastOutput.requested_execution_mode || {};

    return `
        <div class="execution-modes-details">
            <div class="modes-overview">
                <h4>🔧 Execution Mode Summary</h4>
                <div class="mode-summary">
                    <div class="mode-item">
                        <span class="mode-label">Real LLM:</span>
                        <span class="mode-value ${lastOutput.real_llm_used ? 'enabled' : 'disabled'}">${lastOutput.real_llm_used ? '✅' : '❌'}</span>
                    </div>
                    <div class="mode-item">
                        <span class="mode-label">Real Tools:</span>
                        <span class="mode-value ${lastOutput.real_tools_used ? 'enabled' : 'disabled'}">${lastOutput.real_tools_used ? '✅' : '❌'}</span>
                    </div>
                    <div class="mode-item">
                        <span class="mode-label">Real DB:</span>
                        <span class="mode-value ${lastOutput.real_db_used ? 'enabled' : 'disabled'}">${lastOutput.real_db_used ? '✅' : '❌'}</span>
                    </div>
                </div>
            </div>

            <div class="agent-modes">
                <h4>🤖 Agent-Specific Modes</h4>
                <div class="agents-grid">
                    ${Object.entries(actualModes).map(([agent, modes]) => `
                        <div class="agent-mode-card">
                            <h5>${agent}</h5>
                            <div class="agent-mode-details">
                                <div class="mode-detail">
                                    <span>LLM:</span>
                                    <span class="${modes.real_llm ? 'enabled' : 'disabled'}">${modes.real_llm ? '✅' : '❌'}</span>
                                </div>
                                <div class="mode-detail">
                                    <span>Tools:</span>
                                    <span class="${modes.real_tools ? 'enabled' : 'disabled'}">${modes.real_tools ? '✅' : '❌'}</span>
                                </div>
                                <div class="mode-detail">
                                    <span>DB:</span>
                                    <span class="${modes.real_db ? 'enabled' : 'disabled'}">${modes.real_db ? '✅' : '❌'}</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            ${Object.keys(requestedMode).length > 0 ? `
            <div class="requested-vs-actual">
                <h4>🎯 Requested vs Actual</h4>
                <div class="comparison-table">
                    <div class="comparison-row">
                        <span class="comparison-label">LLM</span>
                        <span class="comparison-requested ${requestedMode.use_real_llm ? 'enabled' : 'disabled'}">${requestedMode.use_real_llm ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_llm_used ? 'enabled' : 'disabled'}">${lastOutput.real_llm_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_llm === lastOutput.real_llm_used ? 'match' : 'mismatch'}">${requestedMode.use_real_llm === lastOutput.real_llm_used ? '✅' : '⚠️'}</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Tools</span>
                        <span class="comparison-requested ${requestedMode.use_real_tools ? 'enabled' : 'disabled'}">${requestedMode.use_real_tools ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_tools_used ? 'enabled' : 'disabled'}">${lastOutput.real_tools_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_tools === lastOutput.real_tools_used ? 'match' : 'mismatch'}">${requestedMode.use_real_tools === lastOutput.real_tools_used ? '✅' : '⚠️'}</span>
                    </div>
                    <div class="comparison-row">
                        <span class="comparison-label">Database</span>
                        <span class="comparison-requested ${requestedMode.use_real_db ? 'enabled' : 'disabled'}">${requestedMode.use_real_db ? '✅' : '❌'}</span>
                        <span class="comparison-actual ${lastOutput.real_db_used ? 'enabled' : 'disabled'}">${lastOutput.real_db_used ? '✅' : '❌'}</span>
                        <span class="comparison-match ${requestedMode.use_real_db === lastOutput.real_db_used ? 'match' : 'mismatch'}">${requestedMode.use_real_db === lastOutput.real_db_used ? '✅' : '⚠️'}</span>
                    </div>
                </div>
            </div>
            ` : ''}
        </div>
    `;
}

function getScoreClass(score) {
    if (!score) return 'unknown';
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'fair';
    return 'poor';
}

// Raw Data Tab Functions
function showRawDataTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.raw-data-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    const selectedTab = document.getElementById(`raw-${tabName}`);
    const selectedBtn = event.target;

    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    if (selectedBtn) {
        selectedBtn.classList.add('active');
    }
}

// Copy Functions
function copyFullRawData() {
    const rawData = window.currentWorkflowRawResults || {};
    const jsonString = JSON.stringify(rawData, null, 2);

    navigator.clipboard.writeText(jsonString).then(() => {
        showCopyNotification('Full raw result copied to clipboard!');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = jsonString;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopyNotification('Full raw result copied to clipboard!');
    });
}

function copyTabData(tabName) {
    let data = {};
    const rawData = window.currentWorkflowRawResults || {};

    switch(tabName) {
        case 'performance':
            data = rawData.performance || {};
            break;
        case 'operations':
            data = rawData.operations || {};
            break;
        case 'full':
            data = rawData;
            break;
        default:
            data = {};
    }

    const jsonString = JSON.stringify(data, null, 2);

    navigator.clipboard.writeText(jsonString).then(() => {
        showCopyNotification(`${tabName.charAt(0).toUpperCase() + tabName.slice(1)} data copied to clipboard!`);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = jsonString;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showCopyNotification(`${tabName.charAt(0).toUpperCase() + tabName.slice(1)} data copied to clipboard!`);
    });
}

function showCopyNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        font-weight: 600;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    // Add animation keyframes if not already added
    if (!document.getElementById('copy-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'copy-notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Interactive Event Tree Functions
function renderInteractiveEventTree(agents) {
    const container = document.getElementById('workflow-events-container');
    if (!container) return;

    // Sort agents by timestamp for chronological order
    const sortedAgents = [...agents].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    container.innerHTML = sortedAgents.map((agent, index) => {
        const issues = detectEventIssues(agent);
        const hasIssues = issues.length > 0;
        const issueClasses = issues.map(issue => issue.type).join(' ');

        return `
            <div class="workflow-event ${hasIssues ? 'has-issues' : ''} ${issueClasses}" data-event-index="${index}">
                <div class="event-header" onclick="toggleEventDetails(${index})">
                    <div class="event-info">
                        <div class="event-sequence">${index + 1}</div>
                        <div class="event-agent">🤖 ${agent.agent}</div>
                        <div class="event-stage">${agent.stage}</div>
                        <div class="event-timestamp">${formatTimestamp(agent.timestamp)}</div>
                    </div>
                    <div class="event-status">
                        <div class="status-indicator ${agent.success ? 'success' : 'error'}">
                            ${agent.success ? '✅' : '❌'}
                        </div>
                        <div class="event-duration">${agent.duration_ms ? agent.duration_ms.toFixed(0) + 'ms' : 'N/A'}</div>
                        <div class="event-toggle">▼</div>
                    </div>
                </div>
                <div class="event-details" id="event-details-${index}">
                    ${renderEventDetails(agent, issues)}
                </div>
            </div>
        `;
    }).join('');
}

function renderEventDetails(agent, issues) {
    const inputData = extractAgentInputData(agent);
    const outputData = extractAgentOutputData(agent, window.currentWorkflowModalData);
    const toolCalls = extractToolCalls(inputData);
    const mockDetected = detectMockUsage(agent);
    const fallbackDetected = detectFallbackUsage(agent);

    return `
        <div class="event-details-grid">
            <div class="detail-panel">
                <div class="detail-panel-header">
                    <span>📥</span> Input Data
                </div>
                <div class="detail-panel-content">
                    ${renderInputSummary(inputData)}
                    ${renderEnhancedToolCalls(agent)}
                    ${renderLLMCallInfo(agent)}
                </div>
            </div>

            <div class="detail-panel">
                <div class="detail-panel-header">
                    <span>📤</span> Output Data
                </div>
                <div class="detail-panel-content">
                    ${renderOutputSummary(outputData)}
                </div>
            </div>
        </div>

        ${issues.length > 0 ? `
            <div class="issue-indicators">
                ${issues.map(issue => `
                    <div class="issue-badge ${issue.type}">
                        <span>${issue.icon}</span>
                        <span>${issue.message}</span>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        <div class="json-data-container">
            <div class="json-data-header" onclick="toggleJsonData('input-${agent.timestamp}')">
                <span>📋 Full Input Data</span>
                <span class="toggle-icon">▶</span>
            </div>
            <div class="json-data-content" id="input-${agent.timestamp}">
                <pre>${JSON.stringify(extractAgentInputData(agent), null, 2)}</pre>
            </div>
        </div>

        <div class="json-data-container">
            <div class="json-data-header" onclick="toggleJsonData('output-${agent.timestamp}')">
                <span>📋 Full Output Data</span>
                <span class="toggle-icon">▶</span>
            </div>
            <div class="json-data-content" id="output-${agent.timestamp}">
                <pre>${JSON.stringify(extractAgentOutputData(agent, window.currentWorkflowModalData), null, 2)}</pre>
            </div>
        </div>

        <div class="event-metadata">
            <h5>⚙️ Execution Metadata</h5>
            <div class="metadata-grid">
                <div class="metadata-item">
                    <span class="metadata-label">Agent:</span>
                    <span class="metadata-value">${agent.agent}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Stage:</span>
                    <span class="metadata-value">${agent.stage}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Duration:</span>
                    <span class="metadata-value">${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Success:</span>
                    <span class="metadata-value ${agent.success ? 'success' : 'failure'}">${agent.success ? 'Yes' : 'No'}</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Timestamp:</span>
                    <span class="metadata-value">${agent.timestamp}</span>
                </div>
                ${agent.error ? `
                <div class="metadata-item error">
                    <span class="metadata-label">Error:</span>
                    <span class="metadata-value">${agent.error}</span>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

function detectEventIssues(agent) {
    const issues = [];

    // Check for mocking
    if (detectMockUsage(agent)) {
        issues.push({
            type: 'mock',
            icon: '🎭',
            message: 'Mock tools detected'
        });
    }

    // Check for fallbacks
    if (detectFallbackUsage(agent)) {
        issues.push({
            type: 'fallback',
            icon: '🔄',
            message: 'Fallback strategy used'
        });
    }

    // Check for errors
    if (!agent.success || agent.error) {
        issues.push({
            type: 'error',
            icon: '❌',
            message: agent.error || 'Execution failed'
        });
    }

    // Check for slow execution
    if (agent.duration_ms && agent.duration_ms > 5000) {
        issues.push({
            type: 'slow',
            icon: '⏱️',
            message: `Slow execution (${agent.duration_ms.toFixed(0)}ms)`
        });
    }

    return issues;
}

function detectMockUsage(agent) {
    // Check execution context for mock mode
    if (agent.execution_context?.execution_mode) {
        const mode = agent.execution_context.execution_mode;
        return !mode.real_llm || !mode.real_tools || !mode.real_db;
    }

    // Fallback to string detection
    const inputStr = JSON.stringify(extractAgentInputData(agent));
    const outputStr = JSON.stringify(extractAgentOutputData(agent, window.currentWorkflowModalData));
    return inputStr.toLowerCase().includes('mock') ||
           outputStr.toLowerCase().includes('mock') ||
           inputStr.toLowerCase().includes('test') ||
           outputStr.toLowerCase().includes('dummy');
}

function detectFallbackUsage(agent) {
    // Check for fallback indicators in execution context
    if (agent.execution_context?.fallback_used) {
        return true;
    }

    // Fallback to string detection
    const outputStr = JSON.stringify(extractAgentOutputData(agent, window.currentWorkflowModalData));
    return outputStr.toLowerCase().includes('fallback') ||
           outputStr.toLowerCase().includes('default') ||
           outputStr.toLowerCase().includes('backup');
}

function extractToolCalls(input) {
    const tools = [];
    if (!input || typeof input !== 'object') return tools;

    // Look for common tool call patterns
    if (input.tool_calls) {
        return input.tool_calls.map(call => ({
            name: call.function?.name || call.name || 'Unknown',
            paramCount: Object.keys(call.function?.arguments || call.arguments || {}).length
        }));
    }

    // Look for other tool patterns
    Object.keys(input).forEach(key => {
        if (key.includes('tool') || key.includes('function')) {
            tools.push({
                name: key,
                paramCount: typeof input[key] === 'object' ? Object.keys(input[key]).length : 1
            });
        }
    });

    return tools;
}

function extractDetailedToolCalls(input, output) {
    const toolCalls = [];
    if (!input || typeof input !== 'object') return toolCalls;

    // Look for tool calls in input
    if (input.tool_calls && Array.isArray(input.tool_calls)) {
        input.tool_calls.forEach((call, index) => {
            const toolCall = {
                name: call.function?.name || call.name || 'Unknown Tool',
                parameters: call.function?.arguments || call.arguments || call.parameters,
                is_mocked: detectMockInData(call),
                success: true, // Default to true unless we find error indicators
                duration: call.duration || null,
                result: null,
                error: null
            };

            // Try to find corresponding result in output
            if (output && output.tool_results && Array.isArray(output.tool_results)) {
                const result = output.tool_results[index];
                if (result) {
                    toolCall.result = result.content || result.result || result;
                    toolCall.success = !result.error && !result.is_error;
                    toolCall.error = result.error;
                }
            }

            toolCalls.push(toolCall);
        });
    }

    // Look for other tool patterns in input/output
    ['tools', 'functions', 'actions'].forEach(key => {
        if (input[key] && Array.isArray(input[key])) {
            input[key].forEach(tool => {
                toolCalls.push({
                    name: tool.name || tool.type || 'Unknown Tool',
                    parameters: tool.parameters || tool.args || tool.input,
                    is_mocked: detectMockInData(tool),
                    success: tool.success !== false,
                    duration: tool.duration || null,
                    result: tool.result || tool.output,
                    error: tool.error
                });
            });
        }
    });

    return toolCalls;
}

function extractLLMInfo(agent) {
    const llmInfo = {
        hasLLMCall: false,
        model: null,
        temperature: null,
        inputTokens: null,
        outputTokens: null,
        cost: null,
        prompt: null,
        response: null
    };

    // Check performance metrics for token usage
    if (agent.performance_metrics?.token_usage) {
        const tokenUsage = agent.performance_metrics.token_usage;
        llmInfo.inputTokens = tokenUsage.input || tokenUsage.input_tokens || 0;
        llmInfo.outputTokens = tokenUsage.output || tokenUsage.output_tokens || 0;
        if (llmInfo.inputTokens > 0 || llmInfo.outputTokens > 0) {
            llmInfo.hasLLMCall = true;
        }
    }

    // Check execution context for LLM interactions
    if (agent.execution_context?.llm_interactions) {
        llmInfo.hasLLMCall = true;
        // For now, just indicate that LLM calls were made
        llmInfo.model = "LLM Model Used";
    }

    // Check input data for LLM call information
    const inputData = extractAgentInputData(agent);
    if (inputData && typeof inputData === 'object') {
        // Look for model information
        llmInfo.model = llmInfo.model || inputData.model || inputData.llm_model || inputData.engine;
        llmInfo.temperature = inputData.temperature;
        llmInfo.prompt = inputData.messages || inputData.prompt || inputData.text;

        // Look for token information
        llmInfo.inputTokens = llmInfo.inputTokens || inputData.input_tokens || inputData.prompt_tokens;

        if (llmInfo.model || llmInfo.prompt) {
            llmInfo.hasLLMCall = true;
        }
    }

    // Check output data for LLM response information
    const outputData = extractAgentOutputData(agent, window.currentWorkflowModalData);
    if (outputData && typeof outputData === 'object') {
        llmInfo.response = outputData.content || outputData.text || outputData.message;
        llmInfo.outputTokens = llmInfo.outputTokens || outputData.output_tokens || outputData.completion_tokens;
        llmInfo.cost = outputData.cost || outputData.estimated_cost;

        // Look for usage information
        if (outputData.usage) {
            llmInfo.inputTokens = llmInfo.inputTokens || outputData.usage.prompt_tokens;
            llmInfo.outputTokens = llmInfo.outputTokens || outputData.usage.completion_tokens;
        }

        if (llmInfo.response) {
            llmInfo.hasLLMCall = true;
        }
    }

    return llmInfo;
}

function detectMockInData(data) {
    if (!data) return false;
    const dataStr = JSON.stringify(data).toLowerCase();
    return dataStr.includes('mock') ||
           dataStr.includes('test') ||
           dataStr.includes('dummy') ||
           dataStr.includes('fake');
}

function getToolIcon(toolName) {
    const iconMap = {
        'web_search': '🔍',
        'file_read': '📖',
        'file_write': '✏️',
        'database': '🗄️',
        'api_call': '🌐',
        'email': '📧',
        'calendar': '📅',
        'calculator': '🧮',
        'default': '🛠️'
    };

    const lowerName = toolName.toLowerCase();
    for (const [key, icon] of Object.entries(iconMap)) {
        if (lowerName.includes(key)) return icon;
    }
    return iconMap.default;
}

function renderInputSummary(input) {
    if (!input || typeof input !== 'object') {
        return '<p>No input data available</p>';
    }

    const keys = Object.keys(input);
    const summary = keys.slice(0, 3).join(', ') + (keys.length > 3 ? '...' : '');

    return `
        <div class="data-summary">
            <span class="data-count">${keys.length} fields</span>
            <span class="data-keys">${summary}</span>
        </div>
    `;
}

function renderOutputSummary(output) {
    if (!output || typeof output !== 'object') {
        return '<p>No output data available</p>';
    }

    // Check if this is Resource Agent output with combined_resource_context
    if (output.combined_resource_context) {
        return renderResourceAgentSummary(output.combined_resource_context);
    }

    const keys = Object.keys(output);
    const summary = keys.slice(0, 3).join(', ') + (keys.length > 3 ? '...' : '');

    return `
        <div class="data-summary">
            <span class="data-count">${keys.length} fields</span>
            <span class="data-keys">${summary}</span>
        </div>
    `;
}

function renderResourceAgentSummary(resourceContext) {
    if (!resourceContext || typeof resourceContext !== 'object') {
        return '<p>No resource context available</p>';
    }

    const analysis = resourceContext.analysis_summary || {};
    const environment = resourceContext.environment || {};
    const time = resourceContext.time || {};
    const resources = resourceContext.resources || {};

    return `
        <div class="resource-agent-summary">
            <div class="resource-metrics">
                <div class="metric-item">
                    <span class="metric-label">Feasibility Score:</span>
                    <span class="metric-value ${getFeasibilityClass(analysis.overall_feasibility_score)}">${analysis.overall_feasibility_score || 'N/A'}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Environment:</span>
                    <span class="metric-value">${environment.current_environment?.name || 'Unknown'}</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Time Available:</span>
                    <span class="metric-value">${time.duration_minutes || 'N/A'} min</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Inventory Items:</span>
                    <span class="metric-value">${resources.inventory_count || 0}</span>
                </div>
            </div>
            <div class="resource-insights">
                ${analysis.primary_constraints?.length ? `
                    <div class="constraints">
                        <strong>Key Constraints:</strong> ${analysis.primary_constraints.join(', ')}
                    </div>
                ` : ''}
                ${analysis.key_opportunities?.length ? `
                    <div class="opportunities">
                        <strong>Opportunities:</strong> ${analysis.key_opportunities.join(', ')}
                    </div>
                ` : ''}
                ${analysis.recommended_activity_types?.length ? `
                    <div class="recommendations">
                        <strong>Recommended Types:</strong> ${analysis.recommended_activity_types.join(', ')}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

function getFeasibilityClass(score) {
    if (!score) return '';
    if (score >= 0.8) return 'score-excellent';
    if (score >= 0.6) return 'score-good';
    if (score >= 0.4) return 'score-fair';
    return 'score-poor';
}

function renderEnhancedToolCalls(agent) {
    // Use the enhanced tool calls from execution context if available
    let toolCalls = [];

    if (agent.execution_context?.tool_calls) {
        toolCalls = agent.execution_context.tool_calls.map((toolCall, index) => {
            if (typeof toolCall === 'string') {
                return {
                    name: `Tool Call ${index + 1}`,
                    parameters: {},
                    is_mocked: false,
                    success: true,
                    duration: null,
                    result: null,
                    error: null,
                    uuid: toolCall
                };
            } else if (typeof toolCall === 'object') {
                return {
                    name: toolCall.tool_name || `Tool Call ${index + 1}`,
                    parameters: toolCall.tool_input || {},
                    is_mocked: toolCall.execution_mode === 'mock',
                    success: toolCall.success !== false,
                    duration: toolCall.duration_ms,
                    result: toolCall.tool_output,
                    error: toolCall.error,
                    uuid: toolCall.id
                };
            }
            return toolCall;
        });
    } else {
        // Fallback to old method
        const inputData = extractAgentInputData(agent);
        const outputData = extractAgentOutputData(agent, window.currentWorkflowModalData);
        toolCalls = extractDetailedToolCalls(inputData, outputData);
    }

    if (toolCalls.length === 0) {
        return '<p style="color: #6c757d; font-style: italic; margin: 10px 0;">No tool calls detected</p>';
    }

    return `
        <div style="margin: 15px 0;">
            <h5 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center; gap: 8px;">
                🛠️ Tool Calls
                <span class="tool-calls-count">${toolCalls.length}</span>
            </h5>
            <div class="tool-calls-enhanced">
                ${toolCalls.map((call, index) => `
                    <div class="tool-call-enhanced">
                        <div class="tool-call-header">
                            <div class="tool-call-info">
                                <span class="tool-call-icon">${call.is_mocked ? '🎭' : getToolIcon(call.name)}</span>
                                <span class="tool-call-name">${call.name}</span>
                                <span class="tool-call-index">#${index + 1}</span>
                            </div>
                            <div class="tool-call-meta">
                                ${call.duration ? `<span class="tool-duration">${call.duration}ms</span>` : ''}
                                ${call.is_mocked ? '<span class="mock-badge">MOCKED</span>' : ''}
                                ${call.success === false ? '<span class="error-badge">FAILED</span>' : ''}
                            </div>
                        </div>
                        <div class="tool-call-details">
                            ${call.parameters ? `
                                <div class="json-data-container">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        📥 Input Parameters
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.parameters, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                            ${call.result ? `
                                <div class="json-data-container">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        📤 Output Result
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.result, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                            ${call.error ? `
                                <div class="json-data-container error">
                                    <div class="json-data-header" onclick="toggleJsonData(this)">
                                        ❌ Error Details
                                        <span class="toggle-icon">▼</span>
                                    </div>
                                    <div class="json-data-content">
                                        <pre>${JSON.stringify(call.error, null, 2)}</pre>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function renderLLMCallInfo(agent) {
    const llmInfo = extractLLMInfo(agent);

    if (!llmInfo.hasLLMCall) {
        return '';
    }

    return `
        <div style="margin: 15px 0;">
            <h5 style="margin: 0 0 10px 0; color: #495057; display: flex; align-items: center; gap: 8px;">
                🧠 LLM Call Information
            </h5>
            <div class="llm-call-enhanced">
                <div class="llm-call-header">
                    <div class="llm-model-info">
                        <span class="llm-model-name">${llmInfo.model || 'Unknown Model'}</span>
                        ${llmInfo.temperature !== null ? `<span class="llm-temperature">T: ${llmInfo.temperature}</span>` : ''}
                    </div>
                    <div class="llm-tokens">
                        ${llmInfo.inputTokens ? `<span class="token-count">📥 ${llmInfo.inputTokens}</span>` : ''}
                        ${llmInfo.outputTokens ? `<span class="token-count">📤 ${llmInfo.outputTokens}</span>` : ''}
                        ${llmInfo.cost ? `<span class="llm-cost">💰 $${llmInfo.cost}</span>` : ''}
                    </div>
                </div>
                ${llmInfo.prompt ? `
                    <div class="json-data-container">
                        <div class="json-data-header" onclick="toggleJsonData(this)">
                            💬 Prompt/Messages
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="json-data-content">
                            <pre>${JSON.stringify(llmInfo.prompt, null, 2)}</pre>
                        </div>
                    </div>
                ` : ''}
                ${llmInfo.response ? `
                    <div class="json-data-container">
                        <div class="json-data-header" onclick="toggleJsonData(this)">
                            🤖 LLM Response
                            <span class="toggle-icon">▶</span>
                        </div>
                        <div class="json-data-content">
                            <pre>${JSON.stringify(llmInfo.response, null, 2)}</pre>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// Event Tree Control Functions
function toggleEventDetails(index) {
    const header = document.querySelector(`[data-event-index="${index}"] .event-header`);
    const details = document.getElementById(`event-details-${index}`);
    const toggle = header.querySelector('.event-toggle');

    if (details.classList.contains('expanded')) {
        details.classList.remove('expanded');
        header.classList.remove('expanded');
        toggle.style.transform = 'rotate(0deg)';
    } else {
        details.classList.add('expanded');
        header.classList.add('expanded');
        toggle.style.transform = 'rotate(180deg)';
    }
}

function expandAllEvents() {
    document.querySelectorAll('.workflow-event').forEach((event, index) => {
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        const toggle = event.querySelector('.event-toggle');

        details.classList.add('expanded');
        header.classList.add('expanded');
        toggle.style.transform = 'rotate(180deg)';
    });
}

function collapseAllEvents() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        const toggle = event.querySelector('.event-toggle');

        details.classList.remove('expanded');
        header.classList.remove('expanded');
        toggle.style.transform = 'rotate(0deg)';
    });
}

function showOnlyIssues() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        if (event.classList.contains('has-issues')) {
            event.style.display = 'block';
        } else {
            event.style.display = 'none';
        }
    });
}

function showAllEvents() {
    document.querySelectorAll('.workflow-event').forEach(event => {
        event.style.display = 'block';
    });
}

function toggleJsonData(headerElement) {
    // Handle both old ID-based calls and new element-based calls
    let content, header, toggle;

    if (typeof headerElement === 'string') {
        // Old ID-based approach
        content = document.getElementById(headerElement);
        header = content.previousElementSibling;
        toggle = header.querySelector('.toggle-icon');
    } else {
        // New element-based approach
        header = headerElement;
        content = header.nextElementSibling;
        toggle = header.querySelector('.toggle-icon');
    }

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        toggle.textContent = '▶';
    } else {
        content.classList.add('expanded');
        toggle.textContent = '▼';
    }
}

// Enhanced Timeline and Technical Mode Functions
let timelineZoomLevel = 1;
let technicalModeEnabled = false;

function renderTimelineVisualization(agents) {
    const timelineChart = document.getElementById('timeline-chart');
    if (!timelineChart || !agents || agents.length === 0) return;

    // Sort agents by timestamp
    const sortedAgents = [...agents].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Calculate timeline dimensions
    const startTime = new Date(sortedAgents[0].timestamp);
    const endTime = new Date(sortedAgents[sortedAgents.length - 1].timestamp);
    const totalDuration = endTime - startTime;

    // Create timeline track
    const track = document.createElement('div');
    track.className = 'timeline-track';

    // Add timeline axis
    const axis = document.createElement('div');
    axis.className = 'timeline-axis';
    track.appendChild(axis);

    // Add time labels
    const labelCount = 5;
    for (let i = 0; i <= labelCount; i++) {
        const labelTime = new Date(startTime.getTime() + (totalDuration * i / labelCount));
        const label = document.createElement('div');
        label.className = 'timeline-axis-label';
        label.style.left = `${(i / labelCount) * 100}%`;
        label.textContent = labelTime.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        track.appendChild(label);
    }

    // Add event markers
    sortedAgents.forEach((agent, index) => {
        const eventTime = new Date(agent.timestamp);
        const position = totalDuration > 0 ? ((eventTime - startTime) / totalDuration) * 100 : (index / sortedAgents.length) * 100;

        const marker = document.createElement('div');
        marker.className = `timeline-event-marker ${agent.success ? 'success' : 'error'}`;
        marker.style.left = `${position}%`;
        marker.onclick = () => scrollToEvent(index);

        const label = document.createElement('div');
        label.className = 'timeline-event-label';
        label.textContent = `${agent.agent} (${agent.duration_ms ? agent.duration_ms.toFixed(0) + 'ms' : 'N/A'})`;
        marker.appendChild(label);

        track.appendChild(marker);
    });

    timelineChart.innerHTML = '';
    timelineChart.appendChild(track);
}

function toggleTimelineView() {
    const timeline = document.getElementById('timeline-visualization');
    const button = event.target;

    if (timeline.style.display === 'none') {
        timeline.style.display = 'block';
        button.textContent = '📊 Hide Timeline';
    } else {
        timeline.style.display = 'none';
        button.textContent = '📊 Timeline View';
    }
}

function toggleTechnicalMode() {
    const container = document.querySelector('.workflow-event-tree');
    const button = event.target;

    technicalModeEnabled = !technicalModeEnabled;

    if (technicalModeEnabled) {
        container.classList.add('technical-mode');
        button.textContent = '🔧 Exit Technical';
        enhanceEventsWithTechnicalDetails();
    } else {
        container.classList.remove('technical-mode');
        button.textContent = '🔧 Technical Mode';
        removeEnhancedTechnicalDetails();
    }
}

function enhanceEventsWithTechnicalDetails() {
    document.querySelectorAll('.workflow-event').forEach((event, index) => {
        const details = event.querySelector('.event-details');
        if (!details) return;

        // Add technical details panel if not already present
        if (!details.querySelector('.technical-details-panel')) {
            const techPanel = document.createElement('div');
            techPanel.className = 'technical-details-panel';
            techPanel.innerHTML = `
                <div class="technical-details-header" onclick="toggleTechnicalDetails(this)">
                    <span>🔧 Technical Analysis</span>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="technical-details-content">
                    ${renderTechnicalAnalysis(index)}
                </div>
            `;
            details.appendChild(techPanel);
        }
    });
}

function removeEnhancedTechnicalDetails() {
    document.querySelectorAll('.technical-details-panel').forEach(panel => {
        panel.remove();
    });
}

function renderTechnicalAnalysis(eventIndex) {
    // Get actual agent data from the current modal data
    const modalData = window.currentWorkflowModalData;
    const agents = modalData?.agent_communications?.agents || [];
    const agent = agents[eventIndex];

    if (!agent) {
        return `
            <div class="technical-insights">
                <h5>🔍 Technical Analysis</h5>
                <p>No agent data available for analysis.</p>
            </div>
        `;
    }

    // Calculate metrics from actual data
    const executionTime = agent.duration_ms || 0;
    const tokenUsage = (agent.input_tokens || 0) + (agent.output_tokens || 0);
    const estimatedCost = ((agent.input_tokens || 0) * 0.000005) + ((agent.output_tokens || 0) * 0.000015);
    const toolCalls = (agent && agent.tool_calls) ? agent.tool_calls : 0;
    const success = agent.success !== false;

    // Generate insights based on actual data
    const insights = [];
    if (executionTime > 0) {
        if (executionTime < 1000) {
            insights.push("⚡ Fast execution time");
        } else if (executionTime < 5000) {
            insights.push("✅ Normal execution time");
        } else {
            insights.push("⚠️ Slow execution time - consider optimization");
        }
    }

    if (tokenUsage > 0) {
        if (tokenUsage < 500) {
            insights.push("💚 Efficient token usage");
        } else if (tokenUsage < 1500) {
            insights.push("✅ Moderate token usage");
        } else {
            insights.push("⚠️ High token usage - review prompt efficiency");
        }
    }

    if (success) {
        insights.push("✅ Agent execution successful");
    } else {
        insights.push("❌ Agent execution failed");
    }

    if (agent.real_llm_used) {
        insights.push("🤖 Real LLM used");
    } else {
        insights.push("🎭 Mock LLM used");
    }

    if (agent.errors && agent.errors.length > 0) {
        insights.push(`⚠️ ${agent.errors.length} error(s) detected`);
    }

    return `
        <div class="performance-metrics">
            <div class="performance-metric">
                <span class="metric-value">${executionTime > 0 ? (executionTime / 1000).toFixed(2) + 's' : 'N/A'}</span>
                <span class="metric-label">Execution Time</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">${tokenUsage.toLocaleString()}</span>
                <span class="metric-label">Tokens Used</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">$${estimatedCost.toFixed(4)}</span>
                <span class="metric-label">Estimated Cost</span>
            </div>
            <div class="performance-metric">
                <span class="metric-value">${toolCalls}</span>
                <span class="metric-label">Tool Calls</span>
            </div>
        </div>
        <div class="technical-insights">
            <h5>🔍 Performance Insights</h5>
            <ul>
                ${insights.map(insight => `<li>${insight}</li>`).join('')}
            </ul>
        </div>
        ${agent.errors && agent.errors.length > 0 ? `
        <div class="error-analysis">
            <h5>❌ Error Analysis</h5>
            <ul>
                ${agent.errors.map(error => `<li class="error-item">${error}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        ${agent.tool_call_details ? `
        <div class="tool-call-analysis">
            <h5>🔧 Tool Call Details</h5>
            <pre class="tool-call-json">${JSON.stringify(agent.tool_call_details, null, 2)}</pre>
        </div>
        ` : ''}
    `;
}

function toggleTechnicalDetails(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.toggle-icon');

    if (content.classList.contains('expanded')) {
        content.classList.remove('expanded');
        icon.textContent = '▼';
    } else {
        content.classList.add('expanded');
        icon.textContent = '▲';
    }
}

function zoomTimeline(direction) {
    const track = document.querySelector('.timeline-track');
    if (!track) return;

    if (direction === 'in') {
        timelineZoomLevel *= 1.5;
    } else if (direction === 'out') {
        timelineZoomLevel /= 1.5;
    }

    timelineZoomLevel = Math.max(0.5, Math.min(5, timelineZoomLevel));
    track.style.minWidth = `${800 * timelineZoomLevel}px`;
}

function resetTimelineZoom() {
    timelineZoomLevel = 1;
    const track = document.querySelector('.timeline-track');
    if (track) {
        track.style.minWidth = '800px';
    }
}

function scrollToEvent(index) {
    const event = document.querySelector(`[data-event-index="${index}"]`);
    if (event) {
        event.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight the event briefly
        event.style.boxShadow = '0 0 20px rgba(0, 123, 255, 0.5)';
        setTimeout(() => {
            event.style.boxShadow = '';
        }, 2000);

        // Expand the event if it's not already expanded
        const details = event.querySelector('.event-details');
        const header = event.querySelector('.event-header');
        if (!details.classList.contains('expanded')) {
            toggleEventDetails(index);
        }
    }
}

function renderMockAgentFlow() {
    const mockAgents = [
        { name: 'Orchestrator', stage: 'Initial Coordination', status: '✅' },
        { name: 'Resource & Capacity', stage: 'Resource Assessment', status: '✅' },
        { name: 'Engagement & Pattern', stage: 'Pattern Analysis', status: '✅' },
        { name: 'Psychological', stage: 'Psychological Assessment', status: '✅' },
        { name: 'Strategy', stage: 'Strategy Formulation', status: '✅' },
        { name: 'Wheel/Activity', stage: 'Activity Selection', status: '✅' },
        { name: 'Ethical', stage: 'Ethical Validation', status: '✅' },
        { name: 'Orchestrator', stage: 'Final Integration', status: '✅' }
    ];

    const agentFlow = mockAgents.map((agent, index) => {
        return `
            <div class="agent-flow-step mock-step" data-agent="${agent.name}" data-index="${index}">
                <div class="agent-flow-header">
                    <span class="agent-sequence">${index + 1}</span>
                    <span class="agent-name">${agent.name}</span>
                    <span class="agent-status">${agent.status}</span>
                    <span class="agent-duration">N/A</span>
                </div>
                <div class="agent-flow-stage">${agent.stage}</div>
                <div class="agent-flow-arrow">→</div>
            </div>
        `;
    }).join('');

    return `
        <div class="agent-flow-container mock-flow">
            ${agentFlow}
        </div>
    `;
}

// Helper functions for tool call analysis
function extractToolCallsFromAgent(agentData) {
    // Handle null/undefined agentData gracefully
    if (!agentData) {
        console.warn('extractToolCallsFromAgent: agentData is null or undefined');
        return [];
    }

    // Additional safety check for object type
    if (typeof agentData !== 'object') {
        console.warn('extractToolCallsFromAgent: agentData is not an object:', typeof agentData);
        return [];
    }

    console.log('extractToolCallsFromAgent: Processing agent:', agentData.agent, 'with data:', agentData);

    // Try multiple ways to extract tool calls from the rich data structure
    if (agentData?.execution_context?.tool_calls) {
        const toolCalls = agentData.execution_context.tool_calls;
        console.log('extractToolCallsFromAgent: Found tool_calls in execution_context:', toolCalls);

        // If tool calls are already detailed objects, process them
        if (Array.isArray(toolCalls) && toolCalls.length > 0) {
            return toolCalls.map((tc, index) => {
                // Handle different tool call structures
                let toolInput = tc.tool_input || tc.input || {};
                let toolOutput = tc.tool_output || tc.output || {};

                // Handle nested tool_input structure (arg_0, arg_1 pattern)
                if (toolInput.arg_1) {
                    toolInput = toolInput.arg_1;
                }

                // Handle tool_code pattern
                if (tc.tool_input?.tool_input) {
                    toolInput = tc.tool_input.tool_input;
                }

                return {
                    id: tc.id || `tool-${index}`,
                    tool_name: tc.tool_name || tc.name || `Tool Call ${index + 1}`,
                    success: tc.success !== false,
                    duration_ms: tc.duration_ms || tc.duration || 0,
                    execution_mode: tc.execution_mode || 'unknown',
                    tool_input: toolInput,
                    tool_output: toolOutput,
                    timestamp: tc.timestamp || 'unknown',
                    error: tc.error || null
                };
            });
        }
    }

    // Check performance metrics for tool call count
    const toolCallCount = agentData?.performance_metrics?.tool_call_count || 0;
    if (toolCallCount > 0) {
        console.log(`extractToolCallsFromAgent: Found ${toolCallCount} tool calls in performance metrics`);
        // Create placeholder tool calls based on count
        const placeholderCalls = [];
        for (let i = 0; i < toolCallCount; i++) {
            placeholderCalls.push({
                id: `placeholder-${i}`,
                tool_name: `Tool Call ${i + 1}`,
                success: agentData?.success !== false,
                duration_ms: 0,
                execution_mode: 'unknown',
                tool_input: {},
                tool_output: {},
                timestamp: agentData.timestamp || 'unknown',
                error: null,
                placeholder: true
            });
        }
        return placeholderCalls;
    }

    console.log('extractToolCallsFromAgent: No tool calls found for agent:', agentData.agent);
    return [];
}

function getToolCallDetailsForAgent(agentComm, workflowData) {
    // Get tool calls from agent execution context - these should now be enhanced with details
    const toolCalls = agentComm?.execution_context?.tool_calls || [];

    // If tool calls are already detailed objects (not just UUIDs), return them directly
    const detailedToolCalls = [];

    for (const toolCall of toolCalls) {
        if (typeof toolCall === 'object' && toolCall !== null) {
            // Already a detailed tool call object
            detailedToolCalls.push(toolCall);
        } else if (typeof toolCall === 'string') {
            // Still a UUID, try to find details in the workflow data
            const allToolCalls = workflowData?.agent_communications?.tool_calls || [];
            const foundToolCall = allToolCalls.find(tc => tc.id === toolCall || tc.tool_name === toolCall);

            if (foundToolCall) {
                detailedToolCalls.push(foundToolCall);
            } else {
                // Create a placeholder for missing tool call details
                detailedToolCalls.push({
                    id: toolCall,
                    tool_name: 'Unknown Tool',
                    tool_input: {},
                    tool_output: {},
                    execution_mode: 'unknown',
                    timestamp: agentComm.timestamp || '',
                    duration_ms: 0,
                    success: true,
                    error: null,
                    placeholder: true
                });
            }
        }
    }

    return detailedToolCalls;
}

function renderToolCalls(agent) {
    // Handle undefined agent gracefully
    if (!agent) {
        return `
            <div class="tool-calls">
                <h4>🔧 Tool Calls</h4>
                <div class="tool-call">
                    <div class="tool-name">No Agent Data</div>
                    <div class="tool-params">Agent data not available for tool call analysis</div>
                </div>
            </div>
        `;
    }

    const toolCalls = extractToolCallsFromAgent(agent);

    if (toolCalls.length === 0) {
        return `
            <div class="tool-calls">
                <h4>🔧 Tool Calls (0)</h4>
                <div class="tool-call">
                    <div class="tool-name">No Tool Calls</div>
                    <div class="tool-params">This agent did not make any tool calls</div>
                </div>
            </div>
        `;
    }

    const callsHtml = toolCalls.map((call, index) => `
        <div class="tool-call">
            <div class="tool-name">${call.tool_name || call.name || `Tool Call ${index + 1}`}</div>
            <div class="tool-params">
                ${call.placeholder ?
                    'Tool call executed but details not captured' :
                    JSON.stringify(call.tool_input || call.input || {}, null, 2)
                }
                ${call.uuid ? `<br><small>ID: ${call.uuid}</small>` : ''}
                ${call.execution_mode ? `<br><small>Mode: ${call.execution_mode}</small>` : ''}
                ${call.duration_ms ? `<br><small>Duration: ${call.duration_ms.toFixed(2)}ms</small>` : ''}
            </div>
        </div>
    `).join('');

    return `
        <div class="tool-calls">
            <h4>🔧 Tool Calls (${toolCalls.length})</h4>
            ${callsHtml}
        </div>
    `;
}

function renderDetailedToolCalls(toolCallDetails) {
    if (!toolCallDetails || toolCallDetails.length === 0) {
        return `
            <div class="tool-calls-section">
                <h4>🔧 Tool Calls</h4>
                <div class="tool-call-item">
                    <div class="tool-name">No detailed tool call data available</div>
                    <div class="tool-description">Tool calls were executed but details not captured in this benchmark run</div>
                </div>
            </div>
        `;
    }

    const toolCallsHtml = toolCallDetails.map((toolCall, index) => {
        const isPlaceholder = toolCall.placeholder === true;
        const placeholderClass = isPlaceholder ? 'tool-call-placeholder' : '';

        return `
            <div class="tool-call-item ${placeholderClass}">
                <div class="tool-header">
                    <span class="tool-name">${toolCall.tool_name || `Tool Call ${index + 1}`}</span>
                    ${isPlaceholder ? '<span class="tool-placeholder-badge">📋 Placeholder</span>' : ''}
                    <span class="tool-status ${toolCall.success ? 'success' : 'error'}">
                        ${toolCall.success ? '✅' : '❌'}
                    </span>
                    <span class="tool-duration">${toolCall.duration_ms ? toolCall.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                    ${toolCall.execution_mode ? `<span class="tool-mode">${toolCall.execution_mode}</span>` : ''}
                </div>
                <div class="tool-details">
                    ${isPlaceholder ?
                        `<div class="tool-placeholder-message">
                            <p>⚠️ Tool call details not captured in this benchmark run.</p>
                            <p>Tool ID: <code>${toolCall.id}</code></p>
                        </div>` :
                        `<div class="tool-input">
                            <strong>Input:</strong>
                            <pre>${JSON.stringify(toolCall.tool_input || {}, null, 2)}</pre>
                        </div>
                        <div class="tool-output">
                            <strong>Output:</strong>
                            <pre>${JSON.stringify(toolCall.tool_output || {}, null, 2)}</pre>
                        </div>`
                    }
                    ${toolCall.error ? `<div class="tool-error"><strong>Error:</strong> ${toolCall.error}</div>` : ''}
                </div>
            </div>
        `;
    }).join('');

    return `
        <div class="tool-calls-section">
            <h4>🔧 Tool Calls (${toolCallDetails.length})</h4>
            ${toolCallsHtml}
        </div>
    `;
}

// Interactive functions for detailed views
function renderTailoringStep(activity, relevantAgents) {
    console.log('renderTailoringStep: Processing activity:', activity, 'with agents:', relevantAgents);

    // Find the wheel/activity agent with better matching logic
    const wheelAgent = relevantAgents && relevantAgents.length > 0
        ? relevantAgents.find(agent =>
            agent && (
                agent.agent === 'Wheel/Activity Agent' ||
                agent.agent === 'Activity' ||
                agent.agent === 'activity' ||
                agent.agent === 'Wheel' ||
                agent.agent === 'Strategy' ||
                agent.agent === 'Ethical' ||
                (agent.stage && agent.stage.includes('activity')) ||
                (agent.stage && agent.stage.includes('wheel')) ||
                (agent.stage && agent.stage.includes('strategy'))
            )
        )
        : null;

    console.log('renderTailoringStep: Found wheelAgent:', wheelAgent);

    // If no specific agent found, use the first agent with tool calls
    let agentForToolCalls = wheelAgent;
    if (!agentForToolCalls && relevantAgents && relevantAgents.length > 0) {
        agentForToolCalls = relevantAgents.find(agent =>
            agent && agent.execution_context && agent.execution_context.tool_calls && agent.execution_context.tool_calls.length > 0
        );
    }

    // Final safety check - ensure agentForToolCalls is not null before passing to renderToolCalls
    console.log('renderTailoringStep: Final agentForToolCalls:', agentForToolCalls);
    console.log('renderTailoringStep: relevantAgents count:', relevantAgents ? relevantAgents.length : 0);

    // Enhanced tool calls rendering with better error handling
    let toolCallsHtml;
    if (agentForToolCalls && typeof agentForToolCalls === 'object') {
        try {
            toolCallsHtml = renderToolCalls(agentForToolCalls);
        } catch (error) {
            console.error('renderTailoringStep: Error rendering tool calls:', error);
            toolCallsHtml = `
                <div class="tool-calls-error">
                    <h4>🔧 Tool Calls</h4>
                    <p>⚠️ Error rendering tool calls: ${error.message}</p>
                    <p>Agent: ${agentForToolCalls.agent || 'Unknown'}</p>
                </div>
            `;
        }
    } else {
        // Enhanced fallback with more debugging info
        const agentNames = relevantAgents ?
            relevantAgents.map(a => a && a.agent ? a.agent : 'Unknown').join(', ') :
            'None';
        const agentCount = relevantAgents ? relevantAgents.length : 0;

        toolCallsHtml = `
            <div class="no-agent-data">
                <h4>🔧 Tool Calls</h4>
                <p>No specific agent data available for tool call analysis.</p>
                <p><strong>Available agents (${agentCount}):</strong> ${agentNames}</p>
                <p><strong>Debug info:</strong> agentForToolCalls = ${agentForToolCalls ? 'object' : 'null/undefined'}</p>
                ${relevantAgents && relevantAgents.length > 0 ?
                    `<details>
                        <summary>Agent Details</summary>
                        <pre>${JSON.stringify(relevantAgents.map(a => ({
                            agent: a?.agent,
                            stage: a?.stage,
                            hasToolCalls: !!(a?.execution_context?.tool_calls),
                            toolCallCount: a?.execution_context?.tool_calls?.length || 0
                        })), null, 2)}</pre>
                    </details>` : ''
                }
            </div>
        `;
    }

    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🎯</span>
                    <h3>Activity Tailoring Process</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Tailoring</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🔧</span> Tailoring Transformations
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Personalized Name</div>
                            <div class="field-value">${activity.name || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Domain</div>
                            <div class="field-value">${activity.domain || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Duration</div>
                            <div class="field-value">${activity.duration || 'N/A'} minutes</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Level</div>
                            <div class="field-value">${activity.challenge_level || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Resource Intensity</div>
                            <div class="field-value">${activity.resource_intensity || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                ${toolCallsHtml}
            </div>
        </div>
    `;
}

function showTailoringProcess(activityId) {
    console.log('showTailoringProcess: Starting for activity:', activityId);
    console.log('showTailoringProcess: currentWorkflowModalData:', window.currentWorkflowModalData);

    // Get the current wheel data and agent communications with enhanced debugging
    const wheelData = window.currentWorkflowModalData?.raw_results?.last_output?.output_data?.wheel || {};

    // Try multiple paths for agent communications
    let agentCommunications = [];

    // Path 1: Direct agent_communications array
    if (Array.isArray(window.currentWorkflowModalData?.agent_communications)) {
        agentCommunications = window.currentWorkflowModalData.agent_communications;
        console.log('showTailoringProcess: Found agent_communications as direct array:', agentCommunications.length);
    }
    // Path 2: agent_communications.agents array
    else if (window.currentWorkflowModalData?.agent_communications?.agents) {
        agentCommunications = window.currentWorkflowModalData.agent_communications.agents;
        console.log('showTailoringProcess: Found agent_communications.agents array:', agentCommunications.length);
    }
    // Path 3: enhanced_debugging_data.agents
    else if (window.currentWorkflowModalData?.raw_results?.last_output?.enhanced_debugging_data?.agents) {
        agentCommunications = window.currentWorkflowModalData.raw_results.last_output.enhanced_debugging_data.agents;
        console.log('showTailoringProcess: Found enhanced_debugging_data.agents array:', agentCommunications.length);
    }
    else {
        console.warn('showTailoringProcess: No agent communications found in any expected location');
        agentCommunications = [];
    }

    console.log('showTailoringProcess: Final agentCommunications:', agentCommunications);

    // Show the dedicated crafting modal
    if (typeof window.showWheelItemCrafting === 'function') {
        window.showWheelItemCrafting(activityId, wheelData, agentCommunications);
    } else {
        console.error('showWheelItemCrafting function not available');
        // Fallback: show basic crafting information
        showBasicCraftingInfo(activityId, wheelData, agentCommunications);
    }
}

// Global function for showing wheel item crafting details
window.showWheelItemCrafting = function(activityId, wheelData, agentCommunications) {
    console.log('Showing wheel item crafting for activity:', activityId);

    // Find the specific activity
    const activity = wheelData?.items?.find(item => item.id === activityId || item.name === activityId);
    if (!activity) {
        alert(`Activity not found: ${activityId}`);
        return;
    }

    // Get crafting sequence from agent communications
    const craftingSequence = getCraftingSequenceForActivity(activity, agentCommunications);

    // Create modal content
    const modalContent = `
        <div class="wheel-item-crafting">
            <h3>🎯 Activity Crafting: ${activity.name}</h3>

            <div class="activity-overview">
                <div class="activity-card">
                    <h4>📋 Activity Details</h4>
                    <p><strong>Name:</strong> ${activity.name}</p>
                    <p><strong>Description:</strong> ${activity.description || 'No description'}</p>
                    <p><strong>Domain:</strong> ${activity.domain || 'N/A'}</p>
                    <p><strong>Difficulty:</strong> ${activity.difficulty || 'N/A'}</p>
                    <p><strong>Time:</strong> ${activity.estimated_time || 'N/A'}</p>
                </div>
            </div>

            <div class="crafting-sequence">
                <h4>🔧 Crafting Sequence</h4>
                ${renderCraftingSequence(craftingSequence)}
            </div>

            <div class="agent-contributions">
                <h4>🤖 Agent Contributions</h4>
                ${renderAgentContributions(agentCommunications, activity)}
            </div>
        </div>
    `;

    // Show in Bootstrap modal if available
    if (typeof bootstrap !== 'undefined') {
        const modalElement = document.createElement('div');
        modalElement.className = 'modal fade';
        modalElement.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Activity Crafting Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${modalContent}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modalElement);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    } else {
        // Fallback to custom modal
        const modal = document.createElement('div');
        modal.className = 'wheel-crafting-modal';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="this.parentElement.remove()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    ${modalContent}
                    <button class="btn btn-secondary mt-3" onclick="this.closest('.wheel-crafting-modal').remove()">Close</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
};

function showBasicCraftingInfo(activityId, wheelData, agentCommunications) {
    const activity = wheelData?.items?.find(item => item.id === activityId || item.name === activityId);
    if (!activity) {
        alert(`Activity not found: ${activityId}`);
        return;
    }

    // Add safety check for agentCommunications
    if (!agentCommunications || !Array.isArray(agentCommunications)) {
        console.warn('showBasicCraftingInfo: agentCommunications is not a valid array');
        agentCommunications = [];
    }

    const toolCallCount = agentCommunications.reduce((total, agent) => {
        if (!agent || typeof agent !== 'object') {
            return total;
        }
        return total + (agent.performance_metrics?.tool_call_count || 0);
    }, 0);

    const details = `
Activity: ${activity.name}
Description: ${activity.description || 'No description'}
Domain: ${activity.domain || 'N/A'}
Difficulty: ${activity.difficulty || 'N/A'}
Estimated Time: ${activity.estimated_time || 'N/A'}

Crafting Process:
- Total Agents Involved: ${agentCommunications.length}
- Total Tool Calls: ${toolCallCount}
- Agents: ${agentCommunications.filter(a => a && a.agent).map(a => a.agent).join(', ')}

This activity was crafted through the collaborative work of multiple agents using real database tools and LLM reasoning.
    `;

    alert(details);
}

function getCraftingSequenceForActivity(activity, agentCommunications) {
    // Extract the crafting sequence from agent communications
    const sequence = [];

    // Add safety check for agentCommunications
    if (!agentCommunications || !Array.isArray(agentCommunications)) {
        console.warn('getCraftingSequenceForActivity: agentCommunications is not a valid array');
        return sequence;
    }

    agentCommunications.forEach((agent, index) => {
        // Add safety check for agent object
        if (!agent || typeof agent !== 'object') {
            console.warn(`getCraftingSequenceForActivity: Agent at index ${index} is not a valid object:`, agent);
            return;
        }

        if (agent.execution_context?.tool_calls) {
            agent.execution_context.tool_calls.forEach(toolCall => {
                sequence.push({
                    step: sequence.length + 1,
                    agent: agent.agent,
                    stage: agent.stage,
                    tool: toolCall.tool_name,
                    timestamp: toolCall.timestamp,
                    duration: toolCall.duration_ms,
                    success: toolCall.success,
                    input: toolCall.tool_input,
                    output: toolCall.tool_output,
                    execution_mode: toolCall.execution_mode
                });
            });
        }
    });

    return sequence;
}

function renderCraftingSequence(sequence) {
    if (sequence.length === 0) {
        return '<p>No detailed crafting sequence available.</p>';
    }

    return `
        <div class="crafting-timeline">
            ${sequence.map((step, index) => `
                <div class="crafting-step">
                    <div class="step-marker">
                        <span class="step-number">${step.step}</span>
                        <span class="step-status ${step.success ? 'success' : 'failure'}">
                            ${step.success ? '✅' : '❌'}
                        </span>
                    </div>
                    <div class="step-content">
                        <div class="step-header">
                            <span class="agent-name">${step.agent}</span>
                            <span class="tool-name">${step.tool}</span>
                            <span class="step-duration">${step.duration ? step.duration.toFixed(2) + 'ms' : 'N/A'}</span>
                            <span class="execution-mode ${step.execution_mode}">${step.execution_mode || 'unknown'}</span>
                        </div>
                        <div class="step-details">
                            <div class="step-input">
                                <strong>Input:</strong>
                                <pre>${JSON.stringify(step.input || {}, null, 2)}</pre>
                            </div>
                            <div class="step-output">
                                <strong>Output:</strong>
                                <pre>${JSON.stringify(step.output || {}, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

function renderAgentContributions(agentCommunications, activity) {
    // Add safety check for agentCommunications
    if (!agentCommunications || !Array.isArray(agentCommunications)) {
        return `
            <div class="agent-contributions-grid">
                <div class="no-agent-contributions">
                    <p>No agent contribution data available</p>
                </div>
            </div>
        `;
    }

    return `
        <div class="agent-contributions-grid">
            ${agentCommunications.filter(agent => agent && typeof agent === 'object').map((agent, index) => `
                <div class="agent-contribution">
                    <div class="agent-header">
                        <h5>${agent.agent}</h5>
                        <span class="agent-stage">${agent.stage}</span>
                        <span class="agent-status ${agent.success ? 'success' : 'failure'}">
                            ${agent.success ? '✅' : '❌'}
                        </span>
                    </div>
                    <div class="agent-metrics">
                        <div class="metric">
                            <span class="label">Duration:</span>
                            <span class="value">${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Tool Calls:</span>
                            <span class="value">${agent.performance_metrics?.tool_call_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Tokens:</span>
                            <span class="value">${(agent.performance_metrics?.token_usage?.input || 0) + (agent.performance_metrics?.token_usage?.output || 0)}</span>
                        </div>
                    </div>
                    <div class="agent-tools">
                        <strong>Tools Used:</strong>
                        ${agent.execution_context?.tool_calls ?
                            agent.execution_context.tool_calls.map(tc => tc.tool_name).join(', ') :
                            'None'
                        }
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Helper functions to extract actual agent data from the new data structure
function extractAgentInputData(agentData, workflowData) {
    // FIXED: Comprehensive path checking for agent input data
    // Enhanced with workflow context support for better data extraction

    // 1. Direct input_data field (new format from AgentCommunication.to_dict())
    if (agentData?.input_data && Object.keys(agentData.input_data).length > 0) {
        return agentData.input_data;
    }

    // 2. Legacy 'input' field (some older formats)
    if (agentData?.input && Object.keys(agentData.input).length > 0) {
        return agentData.input;
    }

    // 3. Check workflow context for agent data (enhanced path)
    if (workflowData && agentData?.agent) {
        // Try to find agent data in workflow context
        const workflowAgents = workflowData.agent_communications || workflowData.agents;
        if (Array.isArray(workflowAgents)) {
            const workflowAgent = workflowAgents.find(agent => agent.agent === agentData.agent);
            if (workflowAgent?.input_data && Object.keys(workflowAgent.input_data).length > 0) {
                return workflowAgent.input_data;
            }
            if (workflowAgent?.input && Object.keys(workflowAgent.input).length > 0) {
                return workflowAgent.input;
            }
        }
    }

    // 4. Execution context input data
    if (agentData?.execution_context?.input_data) {
        return agentData.execution_context.input_data;
    }

    // 5. Check if this is wrapped in agent communications format
    if (agentData?.agent_communications?.agents) {
        const currentAgent = agentData.agent_communications.agents.find(
            agent => agent.agent === agentData.agent
        );
        if (currentAgent?.input_data) {
            return currentAgent.input_data;
        }
        if (currentAgent?.input) {
            return currentAgent.input;
        }
    }

    // 6. Check for nested data structures from BenchmarkRun
    if (agentData?.raw_results?.last_output?.agent_communications?.agents) {
        const agents = agentData.raw_results.last_output.agent_communications.agents;
        const currentAgent = agents.find(agent => agent.agent === agentData.agent);
        if (currentAgent?.input_data) {
            return currentAgent.input_data;
        }
        if (currentAgent?.input) {
            return currentAgent.input;
        }
    }

    // 7. If no rich input data found, show execution context summary
    if (agentData?.execution_context) {
        return {
            execution_mode: agentData.execution_context.execution_mode || {},
            processing_steps_count: agentData.execution_context.processing_steps?.length || 0,
            decision_points_count: agentData.execution_context.decision_points?.length || 0,
            tool_calls_count: agentData.execution_context.tool_calls?.length || 0,
            _note: "Execution context summary - rich input not found"
        };
    }

    return {
        message: "No input data available",
        _debug: {
            available_keys: Object.keys(agentData || {}),
            agent_name: agentData?.agent,
            data_structure_type: typeof agentData,
            workflow_context_available: !!workflowData
        }
    };
}

// Function to render rich agent output data in a user-friendly format
function renderRichAgentOutputSection(agentData, workflowData) {
    const outputData = extractAgentOutputData(agentData, workflowData);
    const agentName = agentData?.agent || 'Unknown';

    // Check if we have rich data
    const richFields = ['combined_resource_context', 'engagement_analysis', 'psychological_assessment', 'strategy_framework', 'wheel', 'ethical_validation'];
    const foundRichFields = richFields.filter(field => field in outputData);

    if (foundRichFields.length > 0) {
        // Render rich data in a structured format
        let richHtml = `
            <div class="rich-agent-output">
                <div class="rich-data-header">
                    <span class="success-badge">✅ Rich Agent Data Available</span>
                    <span class="rich-fields-count">${foundRichFields.length} rich field(s)</span>
                </div>
        `;

        foundRichFields.forEach(field => {
            const fieldData = outputData[field];
            const fieldTitle = formatFieldTitle(field);

            richHtml += `
                <div class="rich-field-section">
                    <h5 class="rich-field-title">${fieldTitle}</h5>
                    <div class="rich-field-content">
                        ${renderRichFieldContent(field, fieldData)}
                    </div>
                </div>
            `;
        });

        richHtml += `
                <details class="raw-data-details">
                    <summary>📋 View Raw JSON Data</summary>
                    <pre class="json-viewer">${JSON.stringify(outputData, null, 2)}</pre>
                </details>
            </div>
        `;

        return richHtml;
    } else {
        // Fallback to JSON display
        return `
            <div class="fallback-output">
                <div class="fallback-header">
                    <span class="warning-badge">⚠️ No Rich Data Available</span>
                    <span class="fallback-note">Showing raw output data</span>
                </div>
                <pre class="json-viewer">${JSON.stringify(outputData, null, 2)}</pre>
            </div>
        `;
    }
}

function formatFieldTitle(field) {
    const titles = {
        'combined_resource_context': '🌍 Resource & Environment Context',
        'engagement_analysis': '📈 Engagement Analysis',
        'psychological_assessment': '🧠 Psychological Assessment',
        'strategy_framework': '🎯 Strategy Framework',
        'wheel': '⚙️ Generated Wheel',
        'ethical_validation': '✅ Ethical Validation'
    };
    return titles[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function renderRichFieldContent(field, data) {
    if (!data || typeof data !== 'object') {
        return `<p class="no-data">No data available</p>`;
    }

    switch (field) {
        case 'combined_resource_context':
            return renderResourceContext(data);
        case 'engagement_analysis':
            return renderEngagementAnalysis(data);
        case 'psychological_assessment':
            return renderPsychologicalAssessment(data);
        case 'strategy_framework':
            return renderStrategyFramework(data);
        case 'wheel':
            return renderWheelData(data);
        case 'ethical_validation':
            return renderEthicalValidation(data);
        default:
            return `<pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>`;
    }
}

function renderResourceContext(data) {
    return `
        <div class="resource-context">
            ${data.environment ? `<div class="context-item"><strong>Environment:</strong> ${data.environment}</div>` : ''}
            ${data.time_context ? `<div class="context-item"><strong>Time Context:</strong> ${data.time_context}</div>` : ''}
            ${data.available_resources ? `<div class="context-item"><strong>Available Resources:</strong> ${Array.isArray(data.available_resources) ? data.available_resources.join(', ') : data.available_resources}</div>` : ''}
            ${data.constraints ? `<div class="context-item"><strong>Constraints:</strong> ${Array.isArray(data.constraints) ? data.constraints.join(', ') : data.constraints}</div>` : ''}
            <details class="context-details">
                <summary>View Full Context Data</summary>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
}

function renderEngagementAnalysis(data) {
    return `
        <div class="engagement-analysis">
            ${data.historical_patterns ? `<div class="analysis-item"><strong>Historical Patterns:</strong> ${data.historical_patterns}</div>` : ''}
            ${data.engagement_score ? `<div class="analysis-item"><strong>Engagement Score:</strong> ${data.engagement_score}</div>` : ''}
            ${data.recommendations ? `<div class="analysis-item"><strong>Recommendations:</strong> ${Array.isArray(data.recommendations) ? data.recommendations.join(', ') : data.recommendations}</div>` : ''}
            <details class="analysis-details">
                <summary>View Full Analysis Data</summary>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
}

function renderPsychologicalAssessment(data) {
    return `
        <div class="psychological-assessment">
            ${data.current_state ? `<div class="assessment-item"><strong>Current State:</strong> ${data.current_state}</div>` : ''}
            ${data.trust_level ? `<div class="assessment-item"><strong>Trust Level:</strong> ${data.trust_level}</div>` : ''}
            ${data.mood_indicators ? `<div class="assessment-item"><strong>Mood Indicators:</strong> ${Array.isArray(data.mood_indicators) ? data.mood_indicators.join(', ') : data.mood_indicators}</div>` : ''}
            <details class="assessment-details">
                <summary>View Full Assessment Data</summary>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
}

function renderStrategyFramework(data) {
    return `
        <div class="strategy-framework">
            ${data.approach ? `<div class="strategy-item"><strong>Approach:</strong> ${data.approach}</div>` : ''}
            ${data.key_principles ? `<div class="strategy-item"><strong>Key Principles:</strong> ${Array.isArray(data.key_principles) ? data.key_principles.join(', ') : data.key_principles}</div>` : ''}
            ${data.implementation_steps ? `<div class="strategy-item"><strong>Implementation Steps:</strong> ${Array.isArray(data.implementation_steps) ? data.implementation_steps.join(', ') : data.implementation_steps}</div>` : ''}
            <details class="strategy-details">
                <summary>View Full Strategy Data</summary>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
}

function renderWheelData(data) {
    if (data.items && Array.isArray(data.items)) {
        return `
            <div class="wheel-data">
                <div class="wheel-summary">
                    <strong>Generated ${data.items.length} wheel activities</strong>
                </div>
                <div class="wheel-items">
                    ${data.items.map((item, index) => `
                        <div class="wheel-item">
                            <div class="item-header">
                                <span class="item-number">${index + 1}</span>
                                <span class="item-name">${item.name || 'Unnamed Activity'}</span>
                            </div>
                            <div class="item-details">
                                ${item.domain ? `<span class="item-domain">${item.domain}</span>` : ''}
                                ${item.duration ? `<span class="item-duration">${item.duration} min</span>` : ''}
                                ${item.challenge_level ? `<span class="item-challenge">${item.challenge_level}</span>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
                <details class="wheel-details">
                    <summary>View Full Wheel Data</summary>
                    <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
                </details>
            </div>
        `;
    } else {
        return `
            <div class="wheel-data">
                <div class="wheel-summary">Wheel data structure:</div>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    }
}

function renderEthicalValidation(data) {
    return `
        <div class="ethical-validation">
            ${data.validation_result ? `<div class="validation-item"><strong>Validation Result:</strong> ${data.validation_result}</div>` : ''}
            ${data.safety_score ? `<div class="validation-item"><strong>Safety Score:</strong> ${data.safety_score}</div>` : ''}
            ${data.concerns ? `<div class="validation-item"><strong>Concerns:</strong> ${Array.isArray(data.concerns) ? data.concerns.join(', ') : data.concerns}</div>` : ''}
            <details class="validation-details">
                <summary>View Full Validation Data</summary>
                <pre class="json-viewer">${JSON.stringify(data, null, 2)}</pre>
            </details>
        </div>
    `;
}

function extractAgentOutputData(agentData, workflowData) {
    // FIXED: Comprehensive path checking for agent output data
    // Enhanced with workflow context support for better data extraction

    // 1. Direct output_data field (new format from AgentCommunication.to_dict())
    if (agentData?.output_data && Object.keys(agentData.output_data).length > 0) {
        return agentData.output_data;
    }

    // 2. Legacy 'output' field (some older formats)
    if (agentData?.output && Object.keys(agentData.output).length > 0) {
        return agentData.output;
    }

    // 3. Check workflow context for agent data (enhanced path)
    if (workflowData && agentData?.agent) {
        // Try to find agent data in workflow context
        const workflowAgents = workflowData.agent_communications || workflowData.agents;
        if (Array.isArray(workflowAgents)) {
            const workflowAgent = workflowAgents.find(agent => agent.agent === agentData.agent);
            if (workflowAgent?.output_data && Object.keys(workflowAgent.output_data).length > 0) {
                return workflowAgent.output_data;
            }
            if (workflowAgent?.output && Object.keys(workflowAgent.output).length > 0) {
                return workflowAgent.output;
            }
        }
    }

    // 4. Check if this is wrapped in agent communications format
    if (agentData?.agent_communications?.agents) {
        // Find the current agent's data
        const currentAgent = agentData.agent_communications.agents.find(
            agent => agent.agent === agentData.agent
        );
        if (currentAgent?.output_data) {
            return currentAgent.output_data;
        }
        if (currentAgent?.output) {
            return currentAgent.output;
        }
    }

    // 5. Check for nested data structures from BenchmarkRun
    if (agentData?.raw_results?.last_output?.agent_communications?.agents) {
        const agents = agentData.raw_results.last_output.agent_communications.agents;
        const currentAgent = agents.find(agent => agent.agent === agentData.agent);
        if (currentAgent?.output_data) {
            return currentAgent.output_data;
        }
        if (currentAgent?.output) {
            return currentAgent.output;
        }
    }

    // 6. If no rich output data found, construct from available metadata
    if (agentData?.performance_metrics || agentData?.data_summary) {
        return {
            data_summary: agentData.data_summary || "Processing completed",
            performance_metrics: agentData.performance_metrics || {},
            success: agentData.success,
            duration_ms: agentData.duration_ms,
            stage: agentData.stage,
            _note: "Fallback data - rich output not found"
        };
    }

    return {
        message: "No output data available",
        _debug: {
            available_keys: Object.keys(agentData || {}),
            agent_name: agentData?.agent,
            data_structure_type: typeof agentData,
            workflow_context_available: !!workflowData
        }
    };
}

// Helper function to get token usage from various data structures
function getTokenUsage(agentData, type) {
    // Try performance_metrics first
    if (agentData?.performance_metrics?.token_usage) {
        const tokenUsage = agentData.performance_metrics.token_usage;
        if (type === 'input') {
            return tokenUsage.input || tokenUsage.input_tokens || 0;
        } else if (type === 'output') {
            return tokenUsage.output || tokenUsage.output_tokens || 0;
        }
    }

    // Try execution_context
    if (agentData?.execution_context?.token_usage) {
        const tokenUsage = agentData.execution_context.token_usage;
        if (type === 'input') {
            return tokenUsage.input || tokenUsage.input_tokens || 0;
        } else if (type === 'output') {
            return tokenUsage.output || tokenUsage.output_tokens || 0;
        }
    }

    // If no token data found, show estimated based on agent type
    if (window.benchmarkData?.operations?.total_input_tokens && window.benchmarkData?.operations?.total_output_tokens) {
        const totalAgents = window.benchmarkData?.agent_communications?.length || 1;
        const estimatedInput = Math.floor(window.benchmarkData.operations.total_input_tokens / totalAgents);
        const estimatedOutput = Math.floor(window.benchmarkData.operations.total_output_tokens / totalAgents);

        if (type === 'input') {
            return estimatedInput;
        } else if (type === 'output') {
            return estimatedOutput;
        }
    }

    return 0;
}

// Helper function to get tool call count
function getToolCallCount(agentData) {
    if (agentData?.performance_metrics?.tool_call_count) {
        return agentData.performance_metrics.tool_call_count;
    }
    if (agentData?.execution_context?.tool_calls) {
        return agentData.execution_context.tool_calls.length;
    }
    return 0;
}

// Helper function to get execution mode
function getExecutionMode(agentData) {
    if (agentData?.execution_context?.execution_mode) {
        const mode = agentData.execution_context.execution_mode;
        const modes = [];
        if (mode.real_llm) modes.push('Real LLM');
        if (mode.real_db) modes.push('Real DB');
        if (mode.real_tools) modes.push('Real Tools');
        return modes.length > 0 ? modes.join(', ') : 'Mock Mode';
    }
    return 'Unknown';
}

function showAgentDetails(agentName, index) {
    // Show detailed agent execution modal
    console.log('Showing agent details for:', agentName, 'at index:', index);

    // Try multiple data paths for agent communications - handle both array and object structures
    let agents = [];

    // Check for direct array format (new format)
    if (Array.isArray(window.currentWorkflowModalData?.agent_communications)) {
        agents = window.currentWorkflowModalData.agent_communications;
    }
    // Check for object with agents array (legacy format)
    else if (window.currentWorkflowModalData?.agent_communications?.agents) {
        agents = window.currentWorkflowModalData.agent_communications.agents;
    }

    const agentData = agents[index];
    if (!agentData) {
        console.error('Agent data not found for index:', index, 'Available agents:', agents.length);
        alert(`Agent data not available for ${agentName} at index ${index}`);
        return;
    }

    // Create detailed modal content
    const toolCalls = extractToolCallsFromAgent(agentData);
    const modalContent = `
        <div class="agent-details-content">
            <div class="agent-overview">
                <h3>🤖 ${agentName} Agent Details</h3>
                <div class="agent-meta">
                    <span class="badge badge-primary">Stage: ${agentData.stage || 'Unknown'}</span>
                    <span class="badge badge-info">Duration: ${agentData.duration_ms ? agentData.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                    <span class="badge ${agentData.success ? 'badge-success' : 'badge-danger'}">
                        ${agentData.success ? '✅ Success' : '❌ Failed'}
                    </span>
                </div>
            </div>

            <div class="agent-sections">
                <div class="section">
                    <h4>📥 Input Data</h4>
                    <pre class="json-viewer">${JSON.stringify(extractAgentInputData(agentData, window.currentWorkflowModalData), null, 2)}</pre>
                </div>

                <div class="section">
                    <h4>📤 Output Data</h4>
                    ${renderRichAgentOutputSection(agentData, window.currentWorkflowModalData)}
                </div>

                <div class="section">
                    <h4>🔧 Tool Calls (${toolCalls.length})</h4>
                    ${toolCalls.length > 0 ?
                        toolCalls.map(tc => `
                            <div class="tool-call-detail">
                                <div class="tool-header">
                                    <span class="tool-name">${tc.tool_name}</span>
                                    <span class="tool-status ${tc.success ? 'success' : 'error'}">
                                        ${tc.success ? '✅' : '❌'}
                                    </span>
                                    <span class="tool-duration">${tc.duration_ms ? tc.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                                    <span class="tool-mode">${tc.execution_mode}</span>
                                </div>
                                <div class="tool-details">
                                    <div class="tool-input">
                                        <strong>Input:</strong>
                                        <pre>${JSON.stringify(tc.tool_input, null, 2)}</pre>
                                    </div>
                                    <div class="tool-output">
                                        <strong>Output:</strong>
                                        <pre>${JSON.stringify(tc.tool_output, null, 2)}</pre>
                                    </div>
                                </div>
                            </div>
                        `).join('') :
                        '<p>No tool calls found for this agent.</p>'
                    }
                </div>

                <div class="section">
                    <h4>⚙️ Performance Metrics</h4>
                    <div class="metrics-grid">
                        <div class="metric">
                            <span class="label">Token Usage (In):</span>
                            <span class="value">${getTokenUsage(agentData, 'input')}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Token Usage (Out):</span>
                            <span class="value">${getTokenUsage(agentData, 'output')}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Tool Call Count:</span>
                            <span class="value">${getToolCallCount(agentData)}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Duration:</span>
                            <span class="value">${agentData.duration_ms ? agentData.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="label">Execution Mode:</span>
                            <span class="value">${getExecutionMode(agentData)}</span>
                        </div>
                    </div>
                </div>

                ${agentData.error ? `
                <div class="section error-section">
                    <h4>❌ Error Details</h4>
                    <div class="error-content">${agentData.error}</div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    // Show in Bootstrap modal with proper z-index
    if (typeof bootstrap !== 'undefined') {
        const modalElement = document.createElement('div');
        modalElement.className = 'modal fade';
        modalElement.style.zIndex = '999999';
        modalElement.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${agentName} Agent Execution Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" style="max-height: 80vh; overflow-y: auto;">
                        ${modalContent}
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modalElement);
        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });
    } else {
        // Fallback to custom modal with high z-index
        const modal = document.createElement('div');
        modal.className = 'agent-details-modal';
        modal.style.zIndex = '999999';
        modal.innerHTML = `
            <div class="modal-backdrop" onclick="this.parentElement.remove()">
                <div class="modal-content" onclick="event.stopPropagation()" style="max-height: 90vh; overflow-y: auto;">
                    ${modalContent}
                    <button class="btn btn-secondary mt-3" onclick="this.closest('.agent-details-modal').remove()">Close</button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }
}

function showAgentExecutionDetails(agentName, agentIndex, workflowData) {
    console.log('Showing agent execution details for:', agentName, 'at index:', agentIndex);

    // Get agent communication data - handle multiple data structure formats
    let agentComm = null;
    let agents = [];

    // Try different data structure patterns
    if (Array.isArray(workflowData?.agent_communications)) {
        agents = workflowData.agent_communications;
        agentComm = agents[agentIndex];
    } else if (workflowData?.agent_communications?.agents) {
        agents = workflowData.agent_communications.agents;
        agentComm = agents[agentIndex];
    } else if (Array.isArray(workflowData?.raw_results?.last_output?.agent_communications)) {
        agents = workflowData.raw_results.last_output.agent_communications;
        agentComm = agents[agentIndex];
    }

    console.log('Agent communication lookup:', {
        agentIndex,
        totalAgents: agents.length,
        foundAgent: !!agentComm,
        agentName: agentComm?.agent
    });

    if (!agentComm) {
        alert(`Agent communication data not found for index ${agentIndex}.\nAvailable agents: ${agents.length}\nAgent names: ${agents.map(a => a?.agent || 'Unknown').join(', ')}`);
        return;
    }

    // Get tool call details from the agent's execution context
    const toolCallDetails = getToolCallDetailsForAgent(agentComm, workflowData);

    // Create modal content
    const modalContent = `
        <div class="agent-execution-details">
            <h3>🤖 ${agentName} Execution Details</h3>

            <div class="execution-overview">
                <div class="metric-card">
                    <h4>⏱️ Performance</h4>
                    <p><strong>Duration:</strong> ${agentComm.duration_ms ? agentComm.duration_ms.toFixed(2) + 'ms' : 'N/A'}</p>
                    <p><strong>Success:</strong> ${agentComm.success !== false ? '✅ Yes' : '❌ No'}</p>
                    <p><strong>Stage:</strong> ${agentComm.stage || 'Unknown'}</p>
                </div>

                <div class="metric-card">
                    <h4>🧠 LLM Usage</h4>
                    <p><strong>Input Tokens:</strong> ${agentComm.performance_metrics?.token_usage?.input || 0}</p>
                    <p><strong>Output Tokens:</strong> ${agentComm.performance_metrics?.token_usage?.output || 0}</p>
                    <p><strong>Total:</strong> ${(agentComm.performance_metrics?.token_usage?.input || 0) + (agentComm.performance_metrics?.token_usage?.output || 0)}</p>
                    ${(agentComm.performance_metrics?.token_usage?.input || 0) + (agentComm.performance_metrics?.token_usage?.output || 0) === 0 ?
                        '<p><em>🎭 This agent uses tools only, no LLM reasoning</em></p>' :
                        '<p><em>🧠 This agent used real LLM reasoning</em></p>'}
                </div>

                <div class="metric-card">
                    <h4>🔧 Tool Calls</h4>
                    <p><strong>Count:</strong> ${agentComm.performance_metrics?.tool_call_count || 0}</p>
                    <p><strong>Details Found:</strong> ${toolCallDetails.length}</p>
                    <p><strong>Mode:</strong> ${toolCallDetails.length > 0 ? 'Real Database' : 'None'}</p>
                </div>
            </div>

            ${renderDetailedToolCalls(toolCallDetails)}

            <div class="data-section">
                <h4>📥 Input Data</h4>
                <pre class="json-display">${JSON.stringify(extractAgentInputData(agentComm), null, 2)}</pre>
            </div>

            <div class="data-section">
                <h4>📤 Output Data</h4>
                <pre class="json-display">${JSON.stringify(extractAgentOutputData(agentComm, workflowData), null, 2)}</pre>
            </div>
        </div>
    `;

    // Show in a modal (you might want to use a proper modal library)
    const modal = document.createElement('div');
    modal.className = 'agent-details-modal';
    modal.innerHTML = `
        <div class="modal-backdrop" onclick="this.parentElement.remove()">
            <div class="modal-content" onclick="event.stopPropagation()">
                ${modalContent}
                <button class="btn btn-secondary mt-3" onclick="this.closest('.agent-details-modal').remove()">Close</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function showToolCallDetails(toolName) {
    // This would show all calls for the specific tool
    console.log('Showing tool call details for:', toolName);
    alert(`Tool call details for ${toolName} - This would show all individual calls with parameters and responses`);
}

// Missing utility functions for timeline and modal interactions
function toggleTimelineEvent(element) {
    const eventDetails = element.parentElement.querySelector('.event-details');
    const toggleIcon = element.querySelector('.toggle-icon');

    if (eventDetails.style.display === 'none' || !eventDetails.style.display) {
        eventDetails.style.display = 'block';
        toggleIcon.textContent = '▲';
    } else {
        eventDetails.style.display = 'none';
        toggleIcon.textContent = '▼';
    }
}

function switchDetailTab(button, tabName) {
    const container = button.closest('.event-details');
    const tabs = container.querySelectorAll('.detail-tab');
    const panes = container.querySelectorAll('.detail-pane');

    // Remove active class from all tabs and panes
    tabs.forEach(tab => tab.classList.remove('active'));
    panes.forEach(pane => pane.classList.remove('active'));

    // Add active class to clicked tab and corresponding pane
    button.classList.add('active');
    const targetPane = container.querySelector(`.detail-pane.${tabName}`);
    if (targetPane) {
        targetPane.classList.add('active');
    }
}

// Global function to make showAgentExecutionDetails available
window.showAgentExecutionDetails = function(agentName, agentIndex, workflowData) {
    console.log('Global showAgentExecutionDetails called for:', agentName, 'at index:', agentIndex);

    // Call the local function if it exists
    if (typeof showAgentExecutionDetails === 'function') {
        return showAgentExecutionDetails(agentName, agentIndex, workflowData);
    }

    // Fallback implementation
    alert(`Agent execution details for ${agentName} (index: ${agentIndex}) - Detailed modal not available`);
};

// Vertical Timeline Modal Functions
let timelineScale = 1;
let currentTimelineData = null;

function showVerticalTimeline() {
    const timelineModal = document.getElementById('vertical-timeline-modal');
    timelineModal.style.display = 'block';

    if (currentTimelineData) {
        renderVerticalTimeline(currentTimelineData);
    }
}

function hideVerticalTimeline() {
    const timelineModal = document.getElementById('vertical-timeline-modal');
    timelineModal.style.display = 'none';
}

function renderVerticalTimeline(data) {
    currentTimelineData = data;
    const timelineBody = document.getElementById('vertical-timeline-body');

    if (!data || !data.agent_communications) {
        timelineBody.innerHTML = `
            <div class="timeline-loading">
                <p>No timeline data available</p>
            </div>
        `;
        return;
    }

    const agents = data.agent_communications.agents || [];
    const totalDuration = calculateTotalDuration(agents);

    // Create timeline summary
    const summaryHtml = createTimelineSummary(agents, totalDuration);

    // Create timeline events
    const eventsHtml = createTimelineEvents(agents, totalDuration);

    timelineBody.innerHTML = `
        <div class="vertical-timeline-container">
            <div class="timeline-axis"></div>
            ${summaryHtml}
            ${eventsHtml}
        </div>
    `;
}

function calculateTotalDuration(agents) {
    if (!agents || agents.length === 0) return 1000; // Default 1 second

    let maxEndTime = 0;
    let minStartTime = Infinity;

    agents.forEach(agent => {
        if (agent.start_time) {
            const startTime = new Date(agent.start_time).getTime();
            minStartTime = Math.min(minStartTime, startTime);
        }
        if (agent.end_time) {
            const endTime = new Date(agent.end_time).getTime();
            maxEndTime = Math.max(maxEndTime, endTime);
        }
    });

    return maxEndTime > minStartTime ? maxEndTime - minStartTime : 1000;
}

function createTimelineSummary(agents, totalDuration) {
    const totalAgents = agents.length;
    const successfulAgents = agents.filter(a => a.status === 'success').length;
    const avgDuration = totalAgents > 0 ? totalDuration / totalAgents : 0;
    const slowestAgent = agents.reduce((slowest, agent) => {
        const duration = agent.end_time && agent.start_time ?
            new Date(agent.end_time) - new Date(agent.start_time) : 0;
        return duration > (slowest.duration || 0) ? {...agent, duration} : slowest;
    }, {});

    return `
        <div class="timeline-summary">
            <h3>📊 Execution Summary</h3>
            <div class="timeline-summary-grid">
                <div class="timeline-summary-item">
                    <span class="timeline-summary-value">${totalAgents}</span>
                    <span class="timeline-summary-label">Total Agents</span>
                </div>
                <div class="timeline-summary-item">
                    <span class="timeline-summary-value">${(totalDuration / 1000).toFixed(1)}s</span>
                    <span class="timeline-summary-label">Total Duration</span>
                </div>
                <div class="timeline-summary-item">
                    <span class="timeline-summary-value">${(avgDuration / 1000).toFixed(1)}s</span>
                    <span class="timeline-summary-label">Avg per Agent</span>
                </div>
                <div class="timeline-summary-item">
                    <span class="timeline-summary-value">${successfulAgents}/${totalAgents}</span>
                    <span class="timeline-summary-label">Success Rate</span>
                </div>
            </div>
        </div>
    `;
}

function createTimelineEvents(agents, totalDuration) {
    if (!agents || agents.length === 0) return '';

    return agents.map((agent, index) => {
        const duration = agent.end_time && agent.start_time ?
            new Date(agent.end_time) - new Date(agent.start_time) : 1000;

        // Calculate proportional height based on duration
        const baseHeight = 120; // Minimum height in pixels
        const proportionalHeight = Math.max(baseHeight, (duration / totalDuration) * 500 * timelineScale);

        const status = agent.status || 'unknown';
        const statusClass = getStatusClass(status);
        const durationClass = getDurationClass(duration);

        return `
            <div class="timeline-event ${statusClass}" style="min-height: ${proportionalHeight}px; margin-bottom: ${20 * timelineScale}px;">
                <div class="timeline-marker ${statusClass}">
                    ${getStatusIcon(status)}
                </div>
                <div class="timeline-event-content">
                    <div class="timeline-event-header">
                        <div class="timeline-agent-info">
                            <div class="timeline-agent-name">${agent.agent_name || `Agent ${index + 1}`}</div>
                            <span class="timeline-stage-badge">${agent.stage || 'unknown'}</span>
                        </div>
                        <div class="timeline-performance">
                            <span class="timeline-duration ${durationClass}">
                                ${(duration / 1000).toFixed(2)}s
                            </span>
                            <span class="timeline-timestamp">
                                ${agent.start_time ? new Date(agent.start_time).toLocaleTimeString() : 'N/A'}
                            </span>
                        </div>
                    </div>

                    <div class="timeline-event-details">
                        ${createEventDetails(agent)}
                    </div>

                    ${createEventMetrics(agent, duration)}

                    ${createEventIssues(agent)}
                </div>
            </div>
        `;
    }).join('');
}

function getStatusClass(status) {
    switch(status?.toLowerCase()) {
        case 'success': return 'success';
        case 'error': case 'failed': return 'error';
        case 'warning': return 'warning';
        default: return 'unknown';
    }
}

function getDurationClass(duration) {
    if (duration > 5000) return 'slow';
    if (duration < 1000) return 'fast';
    return '';
}

function getStatusIcon(status) {
    switch(status?.toLowerCase()) {
        case 'success': return '✓';
        case 'error': case 'failed': return '✗';
        case 'warning': return '⚠';
        default: return '?';
    }
}

function createEventDetails(agent) {
    const details = [];

    if (agent.input_data) {
        details.push({
            title: 'Input Data',
            content: typeof agent.input_data === 'string' ? agent.input_data : JSON.stringify(agent.input_data, null, 2)
        });
    }

    if (agent.output_data) {
        details.push({
            title: 'Output Data',
            content: typeof agent.output_data === 'string' ? agent.output_data : JSON.stringify(agent.output_data, null, 2)
        });
    }

    if (agent && agent.tool_calls && agent.tool_calls.length > 0) {
        details.push({
            title: 'Tool Calls',
            content: `${agent.tool_calls.length} tool calls: ${agent.tool_calls.map(tc => tc && tc.tool_name ? tc.tool_name : 'Unknown Tool').join(', ')}`
        });
    }

    if (details.length === 0) {
        return '<p class="timeline-detail-content">No detailed information available</p>';
    }

    return `
        <div class="timeline-details-grid">
            ${details.map(detail => `
                <div class="timeline-detail-card">
                    <h6>${detail.title}</h6>
                    <div class="timeline-detail-content">${detail.content}</div>
                </div>
            `).join('')}
        </div>
    `;
}

function createEventMetrics(agent, duration) {
    const metrics = [];

    if (agent && agent.tool_calls) {
        metrics.push({
            value: agent.tool_calls.length,
            label: 'Tool Calls'
        });
    }

    if (agent.token_usage) {
        metrics.push({
            value: agent.token_usage,
            label: 'Tokens'
        });
    }

    if (agent.llm_calls) {
        metrics.push({
            value: agent.llm_calls,
            label: 'LLM Calls'
        });
    }

    if (metrics.length === 0) return '';

    return `
        <div class="timeline-metrics">
            ${metrics.map(metric => `
                <div class="timeline-metric">
                    <span class="timeline-metric-value">${metric.value}</span>
                    <span class="timeline-metric-label">${metric.label}</span>
                </div>
            `).join('')}
        </div>
    `;
}

function createEventIssues(agent) {
    const issues = [];

    if (agent.errors && agent.errors.length > 0) {
        agent.errors.forEach(error => {
            issues.push({
                type: 'error',
                message: error
            });
        });
    }

    if (agent.warnings && agent.warnings.length > 0) {
        agent.warnings.forEach(warning => {
            issues.push({
                type: 'warning',
                message: warning
            });
        });
    }

    if (issues.length === 0) return '';

    return `
        <div class="timeline-issues">
            ${issues.map(issue => `
                <div class="timeline-issue ${issue.type}">
                    ${issue.type === 'error' ? '❌' : '⚠️'} ${issue.message}
                </div>
            `).join('')}
        </div>
    `;
}

// Timeline controls
function zoomTimelineIn() {
    timelineScale = Math.min(timelineScale * 1.5, 5);
    updateTimelineScale();
    if (currentTimelineData) {
        renderVerticalTimeline(currentTimelineData);
    }
}

function zoomTimelineOut() {
    timelineScale = Math.max(timelineScale / 1.5, 0.2);
    updateTimelineScale();
    if (currentTimelineData) {
        renderVerticalTimeline(currentTimelineData);
    }
}

function resetTimelineZoom() {
    timelineScale = 1;
    updateTimelineScale();
    if (currentTimelineData) {
        renderVerticalTimeline(currentTimelineData);
    }
}

function updateTimelineScale() {
    const scaleIndicator = document.getElementById('timeline-scale');
    if (scaleIndicator) {
        scaleIndicator.textContent = `${timelineScale.toFixed(1)}x`;
    }
}

// Event listeners for timeline modal
document.addEventListener('DOMContentLoaded', function() {
    // Timeline toggle button
    const timelineToggleBtn = document.getElementById('show-timeline-btn');
    if (timelineToggleBtn) {
        timelineToggleBtn.addEventListener('click', showVerticalTimeline);
    }

    // Timeline modal close buttons
    const timelineModal = document.getElementById('vertical-timeline-modal');
    if (timelineModal) {
        const closeBtn = timelineModal.querySelector('.close');
        if (closeBtn) {
            closeBtn.addEventListener('click', hideVerticalTimeline);
        }
    }

    // Timeline control buttons
    const zoomInBtn = document.getElementById('timeline-zoom-in');
    const zoomOutBtn = document.getElementById('timeline-zoom-out');
    const resetBtn = document.getElementById('timeline-reset');

    if (zoomInBtn) zoomInBtn.addEventListener('click', zoomTimelineIn);
    if (zoomOutBtn) zoomOutBtn.addEventListener('click', zoomTimelineOut);
    if (resetBtn) resetBtn.addEventListener('click', resetTimelineZoom);

    // Close timeline modal when clicking outside
    window.addEventListener('click', function(event) {
        const timelineModal = document.getElementById('vertical-timeline-modal');
        if (event.target === timelineModal) {
            hideVerticalTimeline();
        }
    });
});
</script>
