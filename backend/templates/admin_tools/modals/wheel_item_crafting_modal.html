<!-- Wheel Item Crafting Sequence Modal -->
<div id="wheel-item-crafting-modal" class="modal">
    <div class="modal-content crafting-modal">
        <span class="close">&times;</span>
        <div class="modal-header crafting-header">
            <h2>🔧 Wheel Item Crafting Sequence</h2>
            <p class="modal-description">
                Complete journey from generic activity to personalized wheel item. Trace every decision, transformation, and agent contribution.
            </p>
            <div class="crafting-badges">
                <span class="crafting-badge generic" title="Generic Activity Source">
                    📋 Generic Activity
                </span>
                <span class="crafting-badge tailoring" title="Tailoring Process">
                    🎯 Tailoring Process
                </span>
                <span class="crafting-badge wheel-item" title="Final Wheel Item">
                    🎡 Wheel Item
                </span>
                <span class="crafting-badge phase2" title="Phase 2 Enhanced Analysis">
                    🚀 Phase 2
                </span>
                <span class="crafting-badge state-tracking" title="Real-Time State Tracking">
                    ⚡ State Tracking
                </span>
                <span class="crafting-badge tool-analysis" title="Tool Call Analysis">
                    🔧 Tool Analysis
                </span>
            </div>
        </div>
        <div id="crafting-modal-body" class="crafting-modal-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading crafting sequence...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Crafting Modal Specific Styles */
.crafting-modal .modal-content {
    max-width: 1200px;
    width: 95%;
}

.crafting-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.crafting-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.crafting-badges {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.crafting-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: help;
}

.crafting-badge:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.crafting-badge.generic {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.crafting-badge.tailoring {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.crafting-badge.wheel-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.crafting-badge.phase2 {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.crafting-badge.state-tracking {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.crafting-badge.tool-analysis {
    background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
}

/* Crafting Sequence Styles */
.crafting-sequence {
    display: flex;
    flex-direction: column;
    gap: 30px;
    padding: 20px 0;
}

.crafting-step {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    position: relative;
    transition: all 0.3s ease;
}

.crafting-step:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.crafting-step::after {
    content: '↓';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    color: #6c757d;
    background: white;
    padding: 5px 10px;
    border-radius: 50%;
    border: 2px solid #e9ecef;
}

.crafting-step:last-child::after {
    display: none;
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.step-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.step-title h3 {
    margin: 0;
    color: #495057;
    font-size: 1.4em;
}

.step-icon {
    font-size: 1.5em;
}

.step-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.step-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.step-badge.source {
    background: #e9ecef;
    color: #6c757d;
}

.step-badge.process {
    background: #fff3cd;
    color: #856404;
}

.step-badge.result {
    background: #d4edda;
    color: #155724;
}

.step-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.content-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.panel-content {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.data-field {
    margin-bottom: 15px;
}

.data-field:last-child {
    margin-bottom: 0;
}

.field-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
    font-size: 14px;
}

.field-value {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

.field-value.code {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 12px;
}

/* Decision Points */
.decision-points {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.decision-points h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 16px;
}

.decision-item {
    background: white;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.decision-item:last-child {
    margin-bottom: 0;
}

.decision-criterion {
    font-weight: 600;
    color: #856404;
    margin-bottom: 5px;
}

.decision-rationale {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Agent Contributions */
.agent-contributions {
    background: #e7f3ff;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.agent-contributions h4 {
    margin: 0 0 10px 0;
    color: #0c5460;
    font-size: 16px;
}

.agent-contribution {
    background: white;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.agent-contribution:last-child {
    margin-bottom: 0;
}

.agent-name {
    font-weight: 600;
    color: #0c5460;
    margin-bottom: 5px;
}

.agent-input {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Tool Calls */
.tool-calls {
    background: #f3e5f5;
    border: 1px solid #e1bee7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.tool-calls h4 {
    margin: 0 0 10px 0;
    color: #6f42c1;
    font-size: 16px;
}

.tool-call {
    background: white;
    border: 1px solid #e1bee7;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 10px;
}

.tool-call:last-child {
    margin-bottom: 0;
}

.tool-name {
    font-weight: 600;
    color: #6f42c1;
    margin-bottom: 5px;
}

.tool-params {
    color: #6c757d;
    font-size: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    margin-top: 5px;
}

.crafting-modal-body {
    max-height: 80vh;
    overflow-y: auto;
}

/* Enhanced scrollbar for crafting modal body */
.crafting-modal-body::-webkit-scrollbar {
    width: 8px;
}

.crafting-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.crafting-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.crafting-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Phase 2 Enhanced Crafting Styles */
.phase2-crafting-enhancement {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.phase2-crafting-enhancement h4 {
    margin: 0 0 15px 0;
    font-size: 1.3em;
    display: flex;
    align-items: center;
    gap: 10px;
}

.crafting-insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.crafting-insight-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.crafting-insight-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.crafting-insight-card h5 {
    margin: 0 0 10px 0;
    font-size: 1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.crafting-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.crafting-metric:last-child {
    border-bottom: none;
}

.crafting-metric-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.crafting-metric-value {
    font-weight: 600;
    font-size: 1em;
}

/* State Transition Visualization */
.state-transition-flow {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e9ecef;
}

.state-transition-flow h5 {
    margin: 0 0 15px 0;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.transition-step {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    margin: 5px 0;
    border-radius: 6px;
    background: white;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.transition-step:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateX(5px);
}

.transition-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    flex-shrink: 0;
}

.transition-info {
    flex: 1;
}

.transition-agent {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.transition-description {
    font-size: 0.9em;
    color: #6c757d;
}

.transition-timestamp {
    font-size: 0.8em;
    color: #6c757d;
    font-family: monospace;
}

/* Tool Call Sequence in Crafting */
.crafting-tool-sequence {
    background: #f3e5f5;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #e1bee7;
}

.crafting-tool-sequence h5 {
    margin: 0 0 15px 0;
    color: #6f42c1;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tool-sequence-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 8px 0;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e1bee7;
}

.tool-sequence-number {
    background: #6f42c1;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    flex-shrink: 0;
}

.tool-sequence-details {
    flex: 1;
}

.tool-sequence-name {
    font-weight: 600;
    color: #6f42c1;
    margin-bottom: 2px;
}

.tool-sequence-purpose {
    font-size: 0.9em;
    color: #6c757d;
}

.tool-sequence-duration {
    background: rgba(111, 66, 193, 0.1);
    color: #6f42c1;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* Performance Correlation in Crafting */
.crafting-performance-correlation {
    background: #e7f3ff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid #bee5eb;
}

.crafting-performance-correlation h5 {
    margin: 0 0 15px 0;
    color: #0c5460;
    display: flex;
    align-items: center;
    gap: 8px;
}

.correlation-insight {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: white;
    border-radius: 6px;
    border: 1px solid #bee5eb;
}

.correlation-description {
    font-weight: 600;
    color: #0c5460;
}

.correlation-strength {
    display: flex;
    align-items: center;
    gap: 8px;
}

.correlation-value {
    font-weight: 600;
    font-family: monospace;
}

.correlation-value.strong-positive {
    color: #28a745;
}

.correlation-value.strong-negative {
    color: #dc3545;
}

.correlation-value.weak {
    color: #6c757d;
}

.correlation-bar {
    width: 40px;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.correlation-fill {
    height: 100%;
    transition: width 0.3s ease;
}

.correlation-fill.positive {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.correlation-fill.negative {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

/* Responsive design */
@media (max-width: 768px) {
    .step-content {
        grid-template-columns: 1fr;
    }

    .step-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .crafting-insights-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Wheel Item Crafting Modal Functions - Make it globally accessible
window.showWheelItemCrafting = function(activityId, wheelData, agentCommunications) {
    const modal = document.getElementById('wheel-item-crafting-modal');
    const modalBody = document.getElementById('crafting-modal-body');
    
    if (!modal || !modalBody) {
        console.error('Crafting modal elements not found');
        return;
    }
    
    // Show modal
    modal.style.display = 'block';
    
    // Load crafting sequence
    renderCraftingSequence(modalBody, activityId, wheelData, agentCommunications);
}

function renderCraftingSequence(modalBody, activityId, wheelData, agentCommunications) {
    // Find the specific activity and wheel item
    const activities = wheelData.activities || [];
    const items = wheelData.items || [];

    // Phase 2 Enhanced Data
    const stateSnapshots = agentCommunications.state_snapshots || [];
    const toolCallSequences = agentCommunications.tool_call_sequences || [];
    const performanceCorrelations = agentCommunications.performance_correlations || [];
    const advancedMonitoring = agentCommunications.advanced_monitoring || false;
    const performanceInsights = agentCommunications.performance_insights || {};
    const debuggingRecommendations = agentCommunications.debugging_recommendations || [];

    // Try to find activity by ID, or use index-based matching
    let activity = activities.find(a => a.id === activityId);
    let wheelItem = items.find(i => i.activity_id === activityId || i.id === activityId);

    // If not found by ID, try index-based matching
    if (!activity && activities.length > 0) {
        const activityIndex = activities.findIndex((a, index) => index.toString() === activityId || a.name === activityId);
        if (activityIndex >= 0) {
            activity = activities[activityIndex];
            wheelItem = items[activityIndex] || activity;
        }
    }

    // If still not found, try to use the first activity as an example
    if (!activity && activities.length > 0) {
        activity = activities[0];
        wheelItem = items[0] || activity;
        console.warn(`Activity ${activityId} not found, using first activity as example`);
    }

    if (!activity) {
        modalBody.innerHTML = `
            <div class="no-data">
                <h3>⚠️ Activity Data Not Available</h3>
                <p>The requested activity (ID: ${activityId}) could not be found in the benchmark data.</p>
                <p>This could mean:</p>
                <ul>
                    <li>The benchmark was run with mocked data</li>
                    <li>The wheel generation failed</li>
                    <li>The data structure has changed</li>
                    <li>The activity ID format is different than expected</li>
                </ul>
                <p>Available data:</p>
                <ul>
                    <li>Activities: ${activities.length}</li>
                    <li>Items: ${items.length}</li>
                    <li>Agent Communications: ${agentCommunications.agents?.length || 0}</li>
                </ul>
                <details>
                    <summary>Debug Information</summary>
                    <pre>${JSON.stringify({
                        requestedId: activityId,
                        availableActivities: activities.map(a => ({ id: a.id, name: a.name })),
                        availableItems: items.map(i => ({ id: i.id, name: i.name }))
                    }, null, 2)}</pre>
                </details>
            </div>
        `;
        return;
    }
    
    // Extract agent communications related to this activity
    const agents = agentCommunications || [];
    const relevantAgents = agents.filter(agent => 
        agent.output_data && 
        (JSON.stringify(agent.output_data).includes(activityId) || 
         agent.agent === 'Wheel/Activity Agent' || 
         agent.agent === 'Strategy Agent')
    );
    
    modalBody.innerHTML = `
        <!-- Phase 2 Enhanced Crafting Analysis -->
        ${advancedMonitoring ? `
        <div class="phase2-crafting-enhancement">
            <h4>🚀 Phase 2 Enhanced Crafting Analysis</h4>
            <p>Real-time insights into the activity tailoring process with state tracking and performance correlation</p>

            <div class="crafting-insights-grid">
                <div class="crafting-insight-card">
                    <h5>⚡ State Transitions</h5>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Snapshots Captured:</span>
                        <span class="crafting-metric-value">${stateSnapshots.length}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Valid States:</span>
                        <span class="crafting-metric-value">${stateSnapshots.filter(s => s.validation_status === 'valid').length}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Consistency Issues:</span>
                        <span class="crafting-metric-value">${stateSnapshots.filter(s => s.validation_status === 'inconsistent').length}</span>
                    </div>
                </div>

                <div class="crafting-insight-card">
                    <h5>🔧 Tool Sequences</h5>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Sequences Analyzed:</span>
                        <span class="crafting-metric-value">${toolCallSequences.length}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Parallel Execution:</span>
                        <span class="crafting-metric-value">${toolCallSequences.filter(s => s.sequence_type === 'parallel').length}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Dependencies:</span>
                        <span class="crafting-metric-value">${toolCallSequences.reduce((sum, s) => sum + s.dependencies.length, 0)}</span>
                    </div>
                </div>

                <div class="crafting-insight-card">
                    <h5>📊 Performance</h5>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Efficiency Score:</span>
                        <span class="crafting-metric-value">${performanceInsights.workflow_efficiency?.efficiency_score ? (performanceInsights.workflow_efficiency.efficiency_score * 100).toFixed(1) + '%' : 'N/A'}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Correlations Found:</span>
                        <span class="crafting-metric-value">${performanceCorrelations.length}</span>
                    </div>
                    <div class="crafting-metric">
                        <span class="crafting-metric-label">Recommendations:</span>
                        <span class="crafting-metric-value">${debuggingRecommendations.length}</span>
                    </div>
                </div>
            </div>
        </div>
        ` : ''}

        <!-- State Transition Flow -->
        ${stateSnapshots.length > 0 ? `
        <div class="state-transition-flow">
            <h5>🔄 State Transition Flow</h5>
            ${stateSnapshots.filter(s => s.agent !== 'system').map(snapshot => `
                <div class="transition-step">
                    <div class="transition-indicator ${snapshot.validation_status}"></div>
                    <div class="transition-info">
                        <div class="transition-agent">${snapshot.agent}</div>
                        <div class="transition-description">${snapshot.stage} - ${snapshot.validation_status}</div>
                    </div>
                    <div class="transition-timestamp">${new Date(snapshot.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Tool Call Sequence Analysis -->
        ${toolCallSequences.length > 0 ? `
        <div class="crafting-tool-sequence">
            <h5>🔧 Tool Call Sequence Analysis</h5>
            ${toolCallSequences.map((sequence, index) => `
                <div class="tool-sequence-item">
                    <div class="tool-sequence-number">${index + 1}</div>
                    <div class="tool-sequence-details">
                        <div class="tool-sequence-name">${sequence.sequence_type.toUpperCase()} Sequence</div>
                        <div class="tool-sequence-purpose">${sequence.tool_calls.length} tools, ${sequence.dependencies.length} dependencies</div>
                    </div>
                    <div class="tool-sequence-duration">${sequence.total_duration_ms.toFixed(2)}ms</div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <!-- Performance Correlation Insights -->
        ${performanceCorrelations.length > 0 ? `
        <div class="crafting-performance-correlation">
            <h5>📈 Performance Correlation Insights</h5>
            ${performanceCorrelations.map(correlation => `
                <div class="correlation-insight">
                    <div class="correlation-description">${correlation.component_a} ↔ ${correlation.component_b}</div>
                    <div class="correlation-strength">
                        <span class="correlation-value ${Math.abs(correlation.correlation_coefficient) > 0.7 ? (correlation.correlation_coefficient > 0 ? 'strong-positive' : 'strong-negative') : 'weak'}">${correlation.correlation_coefficient.toFixed(3)}</span>
                        <div class="correlation-bar">
                            <div class="correlation-fill ${correlation.correlation_coefficient > 0 ? 'positive' : 'negative'}" style="width: ${Math.abs(correlation.correlation_coefficient) * 100}%"></div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <div class="crafting-sequence">
            ${renderGenericActivityStep(activity)}
            ${renderStrategyDecisionStep(activity, relevantAgents)}
            ${renderTailoringStep(activity, relevantAgents)}
            ${renderWheelIntegrationStep(activity, wheelItem, relevantAgents)}
            ${renderFinalResultStep(activity, wheelItem)}
        </div>
    `;
}

function renderGenericActivityStep(activity) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">📋</span>
                    <h3>Generic Activity Source</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge source">Source</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>📋</span> Original Activity Template
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Activity ID</div>
                            <div class="field-value code">${activity.id || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Name</div>
                            <div class="field-value">${activity.name || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Domain</div>
                            <div class="field-value">${activity.domain || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Base Challenge Rating</div>
                            <div class="field-value">${activity.challenge_rating || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Generic Properties
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Duration Range</div>
                            <div class="field-value">${activity.duration_range || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Resources Required</div>
                            <div class="field-value">${activity.resources_required || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Instructions</div>
                            <div class="field-value">${activity.instructions || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Generic Activity Code</div>
                            <div class="field-value code">${activity.generic_activity_code || 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderStrategyDecisionStep(activity, relevantAgents) {
    const strategyAgent = relevantAgents.find(agent => agent.agent === 'Strategy Agent');
    const strategyData = strategyAgent?.output_data?.strategy_framework || {};

    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🧠</span>
                    <h3>Strategy Framework Decision</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Strategy</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Selection Criteria
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Domain Distribution</div>
                            <div class="field-value code">${JSON.stringify(strategyData.domain_distribution || {}, null, 2)}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Calibration</div>
                            <div class="field-value code">${JSON.stringify(strategyData.challenge_calibration || {}, null, 2)}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Selection Constraints</div>
                            <div class="field-value code">${JSON.stringify(strategyData.selection_constraints || {}, null, 2)}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🤖</span> Agent Decision Process
                    </div>
                    <div class="panel-content">
                        ${strategyAgent ? `
                            <div class="data-field">
                                <div class="field-label">Agent</div>
                                <div class="field-value">${strategyAgent.agent}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Stage</div>
                                <div class="field-value">${strategyAgent.stage || 'N/A'}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Duration</div>
                                <div class="field-value">${strategyAgent.duration_ms ? strategyAgent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</div>
                            </div>
                            <div class="data-field">
                                <div class="field-label">Success</div>
                                <div class="field-value">${strategyAgent.success !== false ? '✅ Yes' : '❌ No'}</div>
                            </div>
                        ` : '<div class="field-value">No strategy agent data available</div>'}
                    </div>
                </div>
            </div>
            ${renderDecisionPoints(activity)}
        </div>
    `;
}

function renderTailoringStep(activity, relevantAgents) {
    // Find the wheel/activity agent with better matching logic
    const wheelAgent = relevantAgents && relevantAgents.length > 0
        ? relevantAgents.find(agent =>
            agent && (
                agent.agent === 'Wheel/Activity Agent' ||
                agent.agent === 'Activity' ||
                agent.agent === 'activity' ||
                (agent.stage && agent.stage.includes('activity'))
            )
        )
        : null;

    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🎯</span>
                    <h3>Activity Tailoring Process</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Tailoring</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🔧</span> Tailoring Transformations
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Tailorization Level</div>
                            <div class="field-value">${activity.tailorization_level || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Personalized Name</div>
                            <div class="field-value">${activity.name || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Customized Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Adjusted Challenge Rating</div>
                            <div class="field-value">${activity.challenge_rating || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Personalized Instructions</div>
                            <div class="field-value">${activity.instructions || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎨</span> Customization Details
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Value Proposition</div>
                            <div class="field-value">${activity.value_proposition || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User Context Applied</div>
                            <div class="field-value">${activity.user_context_applied || 'User profile, preferences, and environmental factors'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Psychological Alignment</div>
                            <div class="field-value">${activity.psychological_alignment || 'Aligned with user trust phase and growth goals'}</div>
                        </div>
                    </div>
                </div>
            </div>
            ${renderAgentContributions(wheelAgent)}
            ${renderToolCalls(wheelAgent)}
        </div>
    `;
}

function renderWheelIntegrationStep(activity, wheelItem, relevantAgents) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">🎡</span>
                    <h3>Wheel Integration</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge process">Integration</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>⚖️</span> Probability Assignment
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Wheel Item ID</div>
                            <div class="field-value code">${wheelItem?.id || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Assigned Percentage</div>
                            <div class="field-value">${wheelItem?.percentage ? wheelItem.percentage.toFixed(1) + '%' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Position in Wheel</div>
                            <div class="field-value">${wheelItem?.position !== undefined ? wheelItem.position : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Color Assignment</div>
                            <div class="field-value">
                                ${wheelItem?.color ? `<span style="display: inline-block; width: 20px; height: 20px; background: ${wheelItem.color}; border-radius: 3px; margin-right: 8px; vertical-align: middle;"></span>${wheelItem.color}` : 'N/A'}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎯</span> Strategic Placement
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Domain Balance</div>
                            <div class="field-value">Contributes to ${activity.domain || 'Unknown'} domain representation</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Distribution</div>
                            <div class="field-value">Provides ${activity.challenge_rating || 'Unknown'} level challenge</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User Growth Alignment</div>
                            <div class="field-value">Supports user's current trust phase and development goals</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderFinalResultStep(activity, wheelItem) {
    return `
        <div class="crafting-step">
            <div class="step-header">
                <div class="step-title">
                    <span class="step-icon">✅</span>
                    <h3>Final Wheel Item</h3>
                </div>
                <div class="step-meta">
                    <span class="step-badge result">Complete</span>
                </div>
            </div>
            <div class="step-content">
                <div class="content-panel">
                    <div class="panel-header">
                        <span>🎡</span> Ready for User Selection
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Final Activity Name</div>
                            <div class="field-value"><strong>${activity.name || 'N/A'}</strong></div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">User-Facing Description</div>
                            <div class="field-value">${activity.description || 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Selection Probability</div>
                            <div class="field-value">${wheelItem?.percentage ? wheelItem.percentage.toFixed(1) + '%' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Estimated Duration</div>
                            <div class="field-value">${activity.duration_range || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="content-panel">
                    <div class="panel-header">
                        <span>📊</span> Quality Metrics
                    </div>
                    <div class="panel-content">
                        <div class="data-field">
                            <div class="field-label">Personalization Score</div>
                            <div class="field-value">${activity.tailorization_level || 'N/A'}/100</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Challenge Appropriateness</div>
                            <div class="field-value">${activity.challenge_rating ? 'Calibrated to user level' : 'N/A'}</div>
                        </div>
                        <div class="data-field">
                            <div class="field-label">Value Proposition</div>
                            <div class="field-value">${activity.value_proposition || 'Supports user growth and engagement'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderDecisionPoints(activity) {
    const decisionPoints = activity.decision_points || [];

    if (decisionPoints.length === 0) {
        return `
            <div class="decision-points">
                <h4>🎯 Decision Points</h4>
                <div class="decision-item">
                    <div class="decision-criterion">Activity Selection</div>
                    <div class="decision-rationale">Selected based on domain distribution strategy and user profile alignment</div>
                </div>
            </div>
        `;
    }

    const pointsHtml = decisionPoints.map(point => `
        <div class="decision-item">
            <div class="decision-criterion">${point.criterion || 'Decision Point'}</div>
            <div class="decision-rationale">${point.rationale || 'No rationale provided'}</div>
        </div>
    `).join('');

    return `
        <div class="decision-points">
            <h4>🎯 Decision Points</h4>
            ${pointsHtml}
        </div>
    `;
}

function renderAgentContributions(agent) {
    if (!agent) {
        return `
            <div class="agent-contributions">
                <h4>🤖 Agent Contributions</h4>
                <div class="agent-contribution">
                    <div class="agent-name">Wheel/Activity Agent</div>
                    <div class="agent-input">No detailed agent communication data available</div>
                </div>
            </div>
        `;
    }

    return `
        <div class="agent-contributions">
            <h4>🤖 Agent Contributions</h4>
            <div class="agent-contribution">
                <div class="agent-name">${agent.agent}</div>
                <div class="agent-input">
                    <strong>Stage:</strong> ${agent.stage || 'N/A'}<br>
                    <strong>Duration:</strong> ${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}<br>
                    <strong>Success:</strong> ${agent.success !== false ? '✅ Yes' : '❌ No'}<br>
                    <strong>Input Data:</strong> ${Object.keys(agent.input_data || {}).length} fields<br>
                    <strong>Output Data:</strong> ${Object.keys(agent.output_data || {}).length} fields
                </div>
            </div>
        </div>
    `;
}

function extractToolCallsFromAgent(agentData) {
    // Handle null/undefined agentData gracefully
    if (!agentData) {
        console.warn('extractToolCallsFromAgent: agentData is null or undefined');
        return [];
    }

    // Try multiple ways to extract tool calls
    if (agentData?.tool_calls) return agentData.tool_calls;

    // Check execution_context for tool_calls array (Phase 2 data structure)
    if (agentData?.execution_context?.tool_calls) {
        const toolCalls = agentData.execution_context.tool_calls;
        if (Array.isArray(toolCalls)) {
            return toolCalls.map((toolCall, index) => {
                // Handle both UUID strings and full tool call objects
                if (typeof toolCall === 'string') {
                    return {
                        id: toolCall,
                        name: `Tool Call ${index + 1}`,
                        success: agentData?.success !== false,
                        input: {},
                        output: {},
                        uuid: toolCall
                    };
                } else if (typeof toolCall === 'object') {
                    return {
                        id: toolCall.id || `tool-${index}`,
                        name: toolCall.tool_name || `Tool Call ${index + 1}`,
                        success: toolCall.success !== false,
                        input: toolCall.tool_input || {},
                        output: toolCall.tool_output || {},
                        uuid: toolCall.id,
                        duration_ms: toolCall.duration_ms,
                        execution_mode: toolCall.execution_mode
                    };
                }
                return {
                    id: `tool-${index}`,
                    name: `Tool Call ${index + 1}`,
                    success: agentData?.success !== false,
                    input: {},
                    output: {}
                };
            });
        }
    }

    if (agentData?.output?.tool_calls) return agentData.output.tool_calls;
    if (agentData?.output_data?.tool_calls) return agentData.output_data.tool_calls;

    // Check performance metrics for tool call count
    const toolCallCount = agentData?.performance_metrics?.tool_call_count || 0;
    if (toolCallCount > 0) {
        // Create placeholder tool calls based on count
        const toolCalls = [];
        for (let i = 0; i < toolCallCount; i++) {
            toolCalls.push({
                name: `Tool Call ${i + 1}`,
                success: agentData?.success !== false,
                input: {},
                output: {},
                placeholder: true
            });
        }
        return toolCalls;
    }

    // Look for tool call patterns in the data
    const toolCalls = [];
    const searchData = JSON.stringify(agentData);

    // This is a simplified extraction - in a real implementation,
    // you'd have more sophisticated tool call detection
    if (searchData.includes('tool_call') || searchData.includes('_call_tool')) {
        toolCalls.push({
            name: 'Detected Tool Call',
            success: agentData?.success !== false,
            input: {},
            output: {}
        });
    }

    return toolCalls;
}

function renderToolCalls(agent) {
    // Handle undefined agent gracefully
    if (!agent) {
        return `
            <div class="tool-calls">
                <h4>🔧 Tool Calls</h4>
                <div class="tool-call">
                    <div class="tool-name">Activity Catalog Query</div>
                    <div class="tool-params">No agent data available for tool call analysis</div>
                </div>
            </div>
        `;
    }

    // Try multiple ways to extract tool calls from agent data
    let toolCalls = extractToolCallsFromAgent(agent);

    if (!toolCalls || toolCalls.length === 0) {
        // Check if agent has performance metrics indicating tool calls
        const toolCallCount = agent?.performance_metrics?.tool_call_count || 0;
        if (toolCallCount > 0) {
            return `
                <div class="tool-calls">
                    <h4>🔧 Tool Calls</h4>
                    <div class="tool-call">
                        <div class="tool-name">${toolCallCount} Tool Call${toolCallCount > 1 ? 's' : ''} Executed</div>
                        <div class="tool-params">Tool call details not captured in this benchmark run</div>
                    </div>
                </div>
            `;
        }

        return `
            <div class="tool-calls">
                <h4>🔧 Tool Calls</h4>
                <div class="tool-call">
                    <div class="tool-name">No Tool Calls</div>
                    <div class="tool-params">This agent did not make any tool calls</div>
                </div>
            </div>
        `;
    }

    const callsHtml = toolCalls.map((call, index) => `
        <div class="tool-call">
            <div class="tool-name">${call.tool_name || call.name || `Tool Call ${index + 1}`}</div>
            <div class="tool-params">
                ${call.placeholder ?
                    'Tool call executed but details not captured' :
                    JSON.stringify(call.input_params || call.input || {}, null, 2)
                }
                ${call.uuid ? `<br><small>ID: ${call.uuid}</small>` : ''}
            </div>
        </div>
    `).join('');

    return `
        <div class="tool-calls">
            <h4>🔧 Tool Calls (${toolCalls.length})</h4>
            ${callsHtml}
        </div>
    `;
}

// Modal close functionality
document.addEventListener('DOMContentLoaded', function() {
    const craftingModal = document.getElementById('wheel-item-crafting-modal');
    const craftingCloseButton = craftingModal ? craftingModal.querySelector('.close') : null;

    if (craftingCloseButton) {
        craftingCloseButton.onclick = function() {
            craftingModal.style.display = 'none';
        };
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target == craftingModal) {
            craftingModal.style.display = 'none';
        }
    });
});
</script>
