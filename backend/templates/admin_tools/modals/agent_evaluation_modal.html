<!-- Agent Evaluation Modal -->
<div id="agent-details-modal" class="modal">
    <div class="modal-content agent-evaluation-modal">
        <span class="close">&times;</span>
        <div class="modal-header">
            <h2>🤖 Agent Evaluation Details</h2>
            <p class="modal-description">
                Comprehensive analysis of individual agent performance with optimization insights and recommendations.
            </p>
            <div class="modal-actions">
                <button id="copy-run-data-btn" class="btn btn-secondary" title="Copy full run data as JSON">
                    📋 Copy Run Data
                </button>
                <button id="refresh-modal-btn" class="btn btn-secondary" title="Refresh modal data">
                    🔄 Refresh
                </button>
            </div>
        </div>
        <div id="agent-modal-body" class="agent-modal-body">
            <!-- Content will be loaded here by JS -->
            <div class="modal-loading">
                <div class="loader"></div>
                <p>Loading agent evaluation data...</p>
            </div>
        </div>
    </div>
</div>

<style>
/* Agent Evaluation Modal Specific Styles */
.agent-evaluation-modal .modal-content {
    max-width: 1200px;
    width: 90%;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
}

.modal-actions {
    position: absolute;
    top: 15px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.modal-actions .btn {
    padding: 6px 12px;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.2);
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85em;
    transition: all 0.2s;
}

.modal-actions .btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
}

.modal-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.modal-description {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.agent-modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.modal-loading .loader {
    margin-bottom: 15px;
}

/* Enhanced scrollbar for modal body */
.agent-modal-body::-webkit-scrollbar {
    width: 8px;
}

.agent-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.agent-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.agent-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Executive Summary Dashboard Styles */
.executive-summary-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.agent-identity {
    display: flex;
    align-items: center;
    gap: 15px;
}

.agent-avatar {
    font-size: 48px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.agent-info h2 {
    margin: 0 0 5px 0;
    font-size: 1.8em;
    font-weight: 600;
}

.scenario-name {
    margin: 0 0 10px 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.run-metadata {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
}

.run-id-badge {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
}

.health-status-indicator {
    text-align: center;
}

.health-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    backdrop-filter: blur(10px);
}

.health-badge.excellent { background: rgba(76, 175, 80, 0.8); }
.health-badge.good { background: rgba(139, 195, 74, 0.8); }
.health-badge.warning { background: rgba(255, 152, 0, 0.8); }
.health-badge.critical { background: rgba(244, 67, 54, 0.8); }

.health-score {
    font-size: 1.5em;
    font-weight: bold;
    opacity: 0.9;
}

.key-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.metric-card {
    background: rgba(255,255,255,0.15);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: transform 0.2s, box-shadow 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.metric-card {
    display: flex;
    align-items: center;
    gap: 15px;
}

.metric-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 1.4em;
    font-weight: bold;
    margin-bottom: 2px;
}

.metric-label {
    font-size: 0.9em;
    opacity: 0.8;
    margin-bottom: 4px;
}

.metric-trend {
    font-size: 0.8em;
    opacity: 0.7;
}

.metric-trend.up { color: #4caf50; }
.metric-trend.down { color: #f44336; }
.metric-trend.stable { color: #ff9800; }

.critical-alerts {
    background: rgba(244, 67, 54, 0.2);
    border: 1px solid rgba(244, 67, 54, 0.4);
    border-radius: 8px;
    padding: 15px;
    backdrop-filter: blur(10px);
}

.critical-alerts h4 {
    margin: 0 0 15px 0;
    color: #ffcdd2;
}

.alert-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    font-size: 0.9em;
}

.alert-icon {
    font-size: 16px;
}

/* Enhanced Section Styles */
.enhanced-section {
    background: #fff;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.section-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.3em;
}

.section-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.section-status.excellent { background: #d4edda; color: #155724; }
.section-status.good { background: #d1ecf1; color: #0c5460; }
.section-status.warning { background: #fff3cd; color: #856404; }
.section-status.critical { background: #f8d7da; color: #721c24; }

.performance-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    align-items: start;
}

.performance-chart-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.performance-metrics-enhanced {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.metric-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.metric-group h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1em;
}

.metrics-grid-enhanced {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.metric-enhanced {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.metric-enhanced .metric-label {
    font-size: 0.85em;
    color: #6c757d;
    font-weight: 500;
}

.metric-enhanced .metric-value {
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
}

.metric-enhanced .metric-value.primary {
    font-size: 1.3em;
    color: #007bff;
}

.metric-enhanced .metric-benchmark {
    font-size: 0.8em;
    font-weight: 500;
}

.reliability-display {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.success-rate-visual {
    display: flex;
    align-items: center;
    gap: 10px;
}

.success-rate-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.success-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.success-rate-text {
    font-weight: 600;
    color: #495057;
    min-width: 50px;
}

.reliability-status {
    text-align: center;
    padding: 8px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9em;
}

/* LLM Analysis Section Styles */
.llm-dashboard {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    margin-bottom: 25px;
}

.llm-config-panel, .cost-analysis-panel {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.llm-config-panel h4, .cost-analysis-panel h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1em;
}

.config-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.config-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.config-icon {
    font-size: 20px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e3f2fd;
    border-radius: 50%;
}

.config-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.config-label {
    font-size: 0.85em;
    color: #6c757d;
    font-weight: 500;
}

.config-value {
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
}

.config-note {
    font-size: 0.8em;
    color: #6c757d;
    font-style: italic;
}

.cost-breakdown {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.cost-summary {
    text-align: center;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.cost-main {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
}

.cost-amount {
    font-size: 1.8em;
    font-weight: bold;
    color: #007bff;
}

.cost-label {
    font-size: 0.9em;
    color: #6c757d;
}

.cost-projection {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    font-size: 0.9em;
}

.projection-amount {
    font-weight: 600;
    color: #495057;
}

.token-metrics {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.token-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.token-label {
    font-size: 0.9em;
    color: #6c757d;
}

.token-value {
    font-weight: 600;
    color: #495057;
}

.token-cost {
    font-size: 0.8em;
    color: #007bff;
}

.intelligent-recommendations {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.intelligent-recommendations h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.recommendations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.recommendation-category {
    background: white;
    border-radius: 6px;
    padding: 15px;
    border-left: 4px solid #007bff;
}

.recommendation-category.important {
    border-left-color: #dc3545;
}

.recommendation-category.optimization {
    border-left-color: #ffc107;
}

.recommendation-category h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 1em;
}

.recommendation-category ul {
    margin: 0;
    padding-left: 20px;
}

.recommendation-category li {
    margin-bottom: 6px;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .agent-evaluation-modal .modal-content {
        width: 95%;
        max-width: none;
    }

    .performance-dashboard {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .llm-dashboard {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .key-metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .summary-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .key-metrics-grid {
        grid-template-columns: 1fr;
    }

    .metrics-grid-enhanced {
        grid-template-columns: 1fr;
    }

    .recommendations-grid {
        grid-template-columns: 1fr;
    }

    .agent-modal-body {
        max-height: 60vh;
    }
}

@media (max-width: 480px) {
    .executive-summary-dashboard {
        padding: 15px;
    }

    .enhanced-section {
        padding: 15px;
    }

    .agent-avatar {
        width: 60px;
        height: 60px;
        font-size: 36px;
    }

    .agent-info h2 {
        font-size: 1.4em;
    }
}

/* Agent Output Display Styles */
.agent-output-display {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
}

.output-sections {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 20px;
    align-items: start;
}

.input-section, .output-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.response-section {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 20px;
    border: 2px solid #2196f3;
}

.input-section h4, .response-section h4, .output-section h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 12px;
}

.context-label {
    font-size: 0.85em;
    color: #6c757d;
    font-weight: 500;
}

.context-value {
    font-size: 1em;
    color: #495057;
    font-weight: 600;
    background: white;
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.response-display {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #bbdefb;
}

.response-text {
    font-size: 1.1em;
    line-height: 1.6;
    color: #2c3e50;
    margin-bottom: 15px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.response-meta {
    display: flex;
    gap: 15px;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
    font-size: 0.9em;
    color: #6c757d;
}

.no-data {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 10px;
}

.no-agent-output {
    padding: 20px;
}

/* Responsive design for agent output */
@media (max-width: 1200px) {
    .output-sections {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .response-section {
        order: -1;
    }
}

/* Enhanced debugging data styles */
.enhanced-section {
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    margin: 15px 0;
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.section-header h3 {
    margin: 0;
    font-size: 1.1em;
    font-weight: 600;
}

.section-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-status.excellent {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.section-status.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.warning-banner {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.warning-banner h4 {
    margin: 0 0 8px 0;
    color: #856404;
}

.possible-reasons {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.possible-reasons h5 {
    margin: 0 0 10px 0;
    color: #495057;
}

.possible-reasons ul {
    margin: 0;
    padding-left: 20px;
}

.possible-reasons li {
    margin-bottom: 5px;
    color: #6c757d;
}

.no-rich-data {
    padding: 20px;
}

.fallback-data {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

.fallback-data h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

/* Enhanced LLM Interactions Styles */
.llm-interactions-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.interactions-summary, .tool-calls-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.interactions-summary h4, .tool-calls-summary h4 {
    margin: 0 0 15px 0;
    color: #495057;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
}

.interactions-details, .tool-calls-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.interaction-card, .tool-call-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.interaction-header, .tool-call-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.interaction-header h5, .tool-call-header h5 {
    margin: 0;
    color: #495057;
    font-size: 1.1em;
}

.interaction-meta, .tool-call-meta {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 500;
}

.badge.success {
    background: #d4edda;
    color: #155724;
}

.badge.error {
    background: #f8d7da;
    color: #721c24;
}

.badge.real-mode {
    background: #d4edda;
    color: #155724;
}

.badge.mock-mode {
    background: #fff3cd;
    color: #856404;
}

.interaction-content, .tool-call-content {
    padding: 20px;
}

.interaction-section, .tool-call-section {
    margin-bottom: 20px;
}

.interaction-section:last-child, .tool-call-section:last-child {
    margin-bottom: 0;
}

.interaction-section h6, .tool-call-section h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 0.95em;
    font-weight: 600;
}

.code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.code-block.error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.token-usage {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.token-stat {
    padding: 6px 12px;
    background: #e3f2fd;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    color: #1565c0;
}

.no-llm-data, .no-tool-calls {
    padding: 20px;
}

.info-banner {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.info-banner h4 {
    margin: 0 0 8px 0;
    color: #1565c0;
}

.debug-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
}

.debug-info h5 {
    margin: 0 0 10px 0;
    color: #495057;
}

/* Workflow Context Section Styles */
.workflow-context-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.workflow-context-section .section-header h3 {
    color: white;
}

.workflow-context-dashboard {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.workflow-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.workflow-info-card {
    background: rgba(255,255,255,0.15);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.info-header {
    display: flex;
    align-items: center;
    gap: 15px;
}

.info-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
}

.info-content h4 {
    margin: 0 0 8px 0;
    font-size: 1.1em;
    opacity: 0.9;
}

.workflow-type-badge, .workflow-stage-badge, .agent-role-badge {
    background: rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 1.1em;
    display: inline-block;
}

.agent-coordination-section {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.agent-coordination-section h4 {
    margin: 0 0 20px 0;
    color: white;
    font-size: 1.2em;
}

.coordination-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.coordination-card {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(255,255,255,0.2);
}

.coordination-card h5 {
    margin: 0 0 15px 0;
    color: white;
    font-size: 1em;
}

.output-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.output-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    font-size: 0.9em;
}

.output-badge {
    font-size: 14px;
}

.output-text {
    flex: 1;
    word-break: break-word;
}

.agent-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.next-agent-badge {
    background: rgba(76, 175, 80, 0.3);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
}

.no-data {
    color: rgba(255,255,255,0.7);
    font-style: italic;
    margin: 0;
}

.evaluation-context-section {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.evaluation-context-section h4 {
    margin: 0 0 15px 0;
    color: white;
    font-size: 1.2em;
}

.context-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.context-id {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9em;
}

.context-id-badge {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.8em;
}

.overrides-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.override-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 6px;
    font-size: 0.85em;
}

.override-key {
    font-weight: 500;
}

.override-value {
    font-weight: 600;
    color: #ffd54f;
}

.workflow-benefits-section {
    background: rgba(255,255,255,0.1);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.workflow-benefits-section h4 {
    margin: 0 0 20px 0;
    color: white;
    font-size: 1.2em;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.2);
}

.benefit-icon {
    font-size: 20px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
}

.benefit-text {
    font-size: 0.9em;
    font-weight: 500;
}
</style>

<script>
// Agent Details Rendering Function - Made global for cross-page access
window.renderAgentDetails = async function(agentModalBody, data, runId) {
    // The modal body element is now passed as an argument
    if (!agentModalBody) {
        console.error('Agent modal body element not provided to renderAgentDetails');
        return;
    }

    // Ensure all fields are accessed safely with defaults
    const meanDuration = data.mean_duration !== null ? data.mean_duration.toFixed(2) : 'N/A';
    const medianDuration = data.median_duration !== null ? data.median_duration.toFixed(2) : 'N/A';
    const minDuration = data.min_duration !== null ? data.min_duration.toFixed(2) : 'N/A';
    const maxDuration = data.max_duration !== null ? data.max_duration.toFixed(2) : 'N/A';
    const stdDev = data.std_dev !== null ? data.std_dev.toFixed(2) : 'N/A';
    const successRate = data.success_rate !== null ? (data.success_rate * 100).toFixed(1) + '%' : 'N/A';
    const semanticScore = data.semantic_score !== null ? parseFloat(data.semantic_score).toFixed(2) : 'N/A';

    // Enhanced analysis for better insights
    const performanceAnalysis = analyzeAgentPerformance(data);
    const healthStatus = calculateHealthStatus(data);
    const recommendations = generateIntelligentRecommendations(data, performanceAnalysis);

    agentModalBody.innerHTML = `
        <!-- Executive Summary Dashboard -->
        <div class="executive-summary-dashboard">
            <div class="summary-header">
                <div class="agent-identity">
                    <div class="agent-avatar">🤖</div>
                    <div class="agent-info">
                        <h2>${data.agent_role ? data.agent_role.charAt(0).toUpperCase() + data.agent_role.slice(1) : 'Unknown'} Agent</h2>
                        <p class="scenario-name">${data.scenario || 'N/A'}</p>
                        <div class="run-metadata">
                            <span class="run-id-badge">ID: ${runId.substring(0, 8)}...</span>
                            <span class="execution-time">${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</span>
                        </div>
                    </div>
                </div>
                <div class="health-status-indicator">
                    <div class="health-badge ${healthStatus.level}">
                        <span class="health-icon">${healthStatus.icon}</span>
                        <span class="health-text">${healthStatus.label}</span>
                    </div>
                    <div class="health-score">${healthStatus.score}/100</div>
                </div>
            </div>

            <div class="key-metrics-grid">
                <div class="metric-card performance ${performanceAnalysis.performance.status}">
                    <div class="metric-icon">⚡</div>
                    <div class="metric-content">
                        <div class="metric-value">${meanDuration}ms</div>
                        <div class="metric-label">Avg Response Time</div>
                        <div class="metric-trend ${performanceAnalysis.performance.trend}">${performanceAnalysis.performance.trendIcon}</div>
                    </div>
                </div>
                <div class="metric-card quality ${performanceAnalysis.quality.status}">
                    <div class="metric-icon">⭐</div>
                    <div class="metric-content">
                        <div class="metric-value">${semanticScore}</div>
                        <div class="metric-label">Quality Score</div>
                        <div class="metric-trend ${performanceAnalysis.quality.trend}">${performanceAnalysis.quality.trendIcon}</div>
                    </div>
                </div>
                <div class="metric-card reliability ${performanceAnalysis.reliability.status}">
                    <div class="metric-icon">🎯</div>
                    <div class="metric-content">
                        <div class="metric-value">${successRate}</div>
                        <div class="metric-label">Success Rate</div>
                        <div class="metric-trend ${performanceAnalysis.reliability.trend}">${performanceAnalysis.reliability.trendIcon}</div>
                    </div>
                </div>
                <div class="metric-card cost ${performanceAnalysis.cost.status}">
                    <div class="metric-icon">💰</div>
                    <div class="metric-content">
                        <div class="metric-value">${data.estimated_cost !== null ? '$' + parseFloat(data.estimated_cost).toFixed(4) : 'N/A'}</div>
                        <div class="metric-label">Est. Cost</div>
                        <div class="metric-trend ${performanceAnalysis.cost.trend}">${performanceAnalysis.cost.trendIcon}</div>
                    </div>
                </div>
            </div>

            ${recommendations.critical.length > 0 ? `
            <div class="critical-alerts">
                <h4>🚨 Critical Issues Requiring Attention</h4>
                <div class="alert-list">
                    ${recommendations.critical.map(alert => `
                        <div class="alert-item critical">
                            <span class="alert-icon">⚠️</span>
                            <span class="alert-text">${alert}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}
        </div>

        <div class="agent-evaluation-header">
            <div class="evaluation-type-badge agent">AGENT</div>
            <div class="evaluation-title">
                <h3>🤖 ${data.agent_role ? data.agent_role.charAt(0).toUpperCase() + data.agent_role.slice(1) : 'Unknown'} Agent</h3>
                <p class="scenario-info">${data.scenario || 'N/A'}</p>
                <div class="evaluation-focus">
                    📋 Focus: Communication Style & Decision Making
                    <span class="tone-analysis-indicator included">
                        ✅ Tone Analysis Included
                    </span>
                </div>
                <div class="evaluation-meta">
                    <span class="run-id">Run ID: ${runId}</span>
                    <span class="execution-date">${data.execution_date ? new Date(data.execution_date).toLocaleString() : 'N/A'}</span>
                    <span class="agent-version">Version: ${data.agent_version || 'N/A'}</span>
                </div>
            </div>
            <div class="optimization-insights">
                <h4>🎯 Optimization Focus</h4>
                <div class="insight-cards">
                    <div class="insight-card llm-config">
                        <div class="card-icon">⚙️</div>
                        <div class="card-content">
                            <h5>LLM Configuration</h5>
                            <p>Model: ${data.llm_model || 'N/A'}</p>
                            <p>Temperature: ${data.llm_temperature !== null ? parseFloat(data.llm_temperature).toFixed(1) : 'N/A'}</p>
                            <p class="optimization-tip">💡 Adjust temperature for creativity vs consistency balance</p>
                        </div>
                    </div>
                    <div class="insight-card performance">
                        <div class="card-icon">⚡</div>
                        <div class="card-content">
                            <h5>Performance</h5>
                            <p>Duration: ${meanDuration}ms</p>
                            <p>Success: ${successRate}</p>
                            <p class="optimization-tip">💡 Optimize prompts to reduce latency</p>
                        </div>
                    </div>
                    <div class="insight-card quality">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <h5>Quality Score</h5>
                            <p>Semantic: ${semanticScore}</p>
                            <p class="optimization-tip">💡 Review semantic evaluation details for improvement areas</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Performance Analysis Section -->
        <div class="enhanced-section performance-analysis">
            <div class="section-header">
                <h3>📊 Performance Analysis</h3>
                <div class="section-status ${performanceAnalysis.performance.status}">
                    ${performanceAnalysis.performance.status.toUpperCase()}
                </div>
            </div>

            <div class="performance-dashboard">
                <div class="performance-chart-container">
                    <canvas id="performance-distribution-chart" width="400" height="200"></canvas>
                </div>

                <div class="performance-metrics-enhanced">
                    <div class="metric-group timing">
                        <h4>⏱️ Response Timing</h4>
                        <div class="metrics-grid-enhanced">
                            <div class="metric-enhanced">
                                <span class="metric-label">Average</span>
                                <span class="metric-value primary">${meanDuration}ms</span>
                                <span class="metric-benchmark">${meanDuration < 1000 ? '🟢 Fast' : meanDuration < 3000 ? '🟡 Moderate' : '🔴 Slow'}</span>
                            </div>
                            <div class="metric-enhanced">
                                <span class="metric-label">Median</span>
                                <span class="metric-value">${medianDuration}ms</span>
                            </div>
                            <div class="metric-enhanced">
                                <span class="metric-label">Range</span>
                                <span class="metric-value">${minDuration} - ${maxDuration}ms</span>
                            </div>
                            <div class="metric-enhanced">
                                <span class="metric-label">Variability</span>
                                <span class="metric-value">±${stdDev}ms</span>
                                <span class="metric-benchmark">${parseFloat(stdDev) < 500 ? '🟢 Consistent' : parseFloat(stdDev) < 1000 ? '🟡 Variable' : '🔴 Inconsistent'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="metric-group reliability">
                        <h4>🎯 Reliability</h4>
                        <div class="reliability-display">
                            <div class="success-rate-visual">
                                <div class="success-rate-bar">
                                    <div class="success-fill" style="width: ${parseFloat(successRate)}%"></div>
                                </div>
                                <span class="success-rate-text">${successRate}</span>
                            </div>
                            <div class="reliability-status ${performanceAnalysis.reliability.status}">
                                ${parseFloat(successRate) >= 95 ? '🟢 Excellent' : parseFloat(successRate) >= 85 ? '🟡 Good' : '🔴 Needs Improvement'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced LLM Configuration & Cost Analysis -->
        <div class="enhanced-section llm-analysis">
            <div class="section-header">
                <h3>🔧 LLM Configuration & Cost Analysis</h3>
                <div class="section-status ${performanceAnalysis.cost.status}">
                    COST ${performanceAnalysis.cost.status.toUpperCase()}
                </div>
            </div>

            <div class="llm-dashboard">
                <div class="llm-config-panel">
                    <h4>⚙️ Model Configuration</h4>
                    <div class="config-grid">
                        <div class="config-item model">
                            <div class="config-icon">🤖</div>
                            <div class="config-details">
                                <span class="config-label">Model</span>
                                <span class="config-value">${data.llm_model || 'N/A'}</span>
                                <span class="config-note">${getModelInsight(data.llm_model)}</span>
                            </div>
                        </div>
                        <div class="config-item temperature">
                            <div class="config-icon">🌡️</div>
                            <div class="config-details">
                                <span class="config-label">Temperature</span>
                                <span class="config-value">${data.llm_temperature !== null ? parseFloat(data.llm_temperature).toFixed(1) : 'N/A'}</span>
                                <span class="config-note">${getTemperatureInsight(data.llm_temperature)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="cost-analysis-panel">
                    <h4>💰 Cost & Token Analysis</h4>
                    <div class="cost-breakdown">
                        <div class="cost-summary">
                            <div class="cost-main">
                                <span class="cost-amount">${data.estimated_cost !== null ? '$' + parseFloat(data.estimated_cost).toFixed(4) : 'N/A'}</span>
                                <span class="cost-label">Per Run</span>
                            </div>
                            <div class="cost-projection">
                                <span class="projection-label">Monthly (1000 runs):</span>
                                <span class="projection-amount">${data.estimated_cost !== null ? '$' + (parseFloat(data.estimated_cost) * 1000).toFixed(2) : 'N/A'}</span>
                            </div>
                        </div>

                        <div class="token-metrics">
                            <div class="token-item input">
                                <span class="token-label">Input Tokens</span>
                                <span class="token-value">${data.total_input_tokens ?? 'N/A'}</span>
                                <span class="token-cost">${calculateTokenCost(data.total_input_tokens, 'input')}</span>
                            </div>
                            <div class="token-item output">
                                <span class="token-label">Output Tokens</span>
                                <span class="token-value">${data.total_output_tokens ?? 'N/A'}</span>
                                <span class="token-cost">${calculateTokenCost(data.total_output_tokens, 'output')}</span>
                            </div>
                            <div class="token-item calls">
                                <span class="token-label">LLM Calls</span>
                                <span class="token-value">${data.llm_calls ?? 'N/A'}</span>
                                <span class="token-cost">Efficiency: ${calculateEfficiency(data)}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="intelligent-recommendations">
                <h4>🧠 Intelligent Optimization Recommendations</h4>
                <div class="recommendations-grid">
                    ${recommendations.important.length > 0 ? `
                    <div class="recommendation-category important">
                        <h5>🔥 High Priority</h5>
                        <ul>
                            ${recommendations.important.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}

                    ${recommendations.optimization.length > 0 ? `
                    <div class="recommendation-category optimization">
                        <h5>⚡ Optimization Opportunities</h5>
                        <ul>
                            ${recommendations.optimization.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                    ` : ''}

                    <div class="recommendation-category general">
                        <h5>💡 General Suggestions</h5>
                        <ul>
                            <li>Monitor token usage patterns to identify optimization opportunities</li>
                            <li>Consider A/B testing different temperature values for your use case</li>
                            <li>Track cost trends over time to budget effectively</li>
                            ${data.llm_calls > 5 ? '<li>High number of LLM calls - consider prompt consolidation</li>' : ''}
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agent Output Display Section -->
        <div class="enhanced-section agent-output-display">
            <div class="section-header">
                <h3>💬 Agent Response Output</h3>
                <div class="section-status excellent">
                    AGENT RESPONSE
                </div>
            </div>
            <div class="agent-output-content">
                ${renderAgentOutputDisplay(data)}
            </div>
        </div>

        ${data.semantic_score !== null ? `
        <div class="enhanced-section semantic-evaluation">
            <div class="section-header">
                <h3>🎯 Semantic Quality Analysis</h3>
                <div class="section-status ${data.semantic_score >= 0.8 ? 'excellent' : data.semantic_score >= 0.6 ? 'good' : data.semantic_score >= 0.4 ? 'warning' : 'critical'}">
                    SCORE: ${semanticScore}
                </div>
            </div>
            <div class="semantic-content">
                <div class="metrics-grid">
                    <div class="metric"><span class="metric-label">Overall Score</span><span class="metric-value">${semanticScore}</span></div>
                </div>
                <div id="agent-semantic-evaluation-details">
                    <!-- Semantic details will be rendered here by JS -->
                </div>
            </div>
        </div>` : `
        <div class="enhanced-section semantic-evaluation">
            <div class="section-header">
                <h3>🎯 Semantic Quality Analysis</h3>
                <div class="section-status warning">
                    EVALUATION SKIPPED
                </div>
            </div>
            <div class="semantic-content">
                <div class="warning-banner">
                    <h4>⚠️ Semantic Evaluation Was Skipped</h4>
                    <p>${data.semantic_evaluation_details?.notes || 'Evaluation skipped: Agent response or quality criteria missing/invalid.'}</p>
                </div>
                <div class="possible-reasons">
                    <h5>Common reasons for skipped evaluation:</h5>
                    <ul>
                        <li>Agent response was empty or malformed</li>
                        <li>Evaluation criteria template not found or invalid</li>
                        <li>Evaluator LLM model not configured properly</li>
                        <li>Agent output doesn't match expected format for evaluation</li>
                        <li>Semantic evaluation disabled in benchmark configuration</li>
                    </ul>
                </div>
                <div class="debug-info">
                    <h5>Debug Information:</h5>
                    <p>Semantic evaluation enabled: ${data.parameters?.semantic_evaluation ? 'Yes' : 'No'}</p>
                    <p>Evaluator model: ${data.evaluator_llm_model || 'Not specified'}</p>
                    <p>Agent response length: ${data.raw_results?.last_output?.user_response?.length || 0} characters</p>
                </div>
            </div>
        </div>
        `}

        <!-- Enhanced LLM Interactions Section -->
        <div class="enhanced-section llm-interactions-analysis">
            <div class="section-header">
                <h3>🧠 LLM Interactions Analysis</h3>
                <div class="section-status" id="llm-interactions-status">
                    ANALYZING...
                </div>
            </div>
            <div id="llm-interactions-content">
                <!-- LLM interactions will be rendered here -->
            </div>
        </div>

        <div class="modal-section">
            <h3>🛠️ Tool Usage Analysis</h3>
            ${data.tool_call_details && data.tool_call_details.total_calls > 0 ? `
                <div class="metrics-grid">
                    <div class="metric"><span class="metric-label">Total Calls</span><span class="metric-value">${data.tool_call_details.total_calls}</span></div>
                    <div class="metric"><span class="metric-label">Mocked Calls</span><span class="metric-value" style="color: #ff9800;">${data.tool_call_details.mocked_calls || 0}</span></div>
                    <div class="metric"><span class="metric-label">Real Calls</span><span class="metric-value" style="color: #4caf50;">${data.tool_call_details.real_calls || 0}</span></div>
                </div>
            ` : ''}
            ${renderToolUsageForAgent(data)}
            <div id="enhanced-tool-calls-section">
                <!-- Enhanced tool calls will be rendered here -->
            </div>
            <div class="tool-chart-container">
                <canvas id="agent-modal-tool-chart"></canvas>
            </div>
        </div>

        ${data.context_package ? `
        <div class="modal-section">
            <h3>📋 Context Analysis</h3>
            <div id="agent-context-package-details">
                <!-- Context package details will be rendered here by JS -->
            </div>
        </div>
        ` : ''}

        ${isWorkflowBenchmark(data) ? `
        <div class="enhanced-section workflow-context-section">
            <div class="section-header">
                <h3>🔄 Workflow Context Analysis</h3>
                <div class="section-status excellent">WORKFLOW-AWARE</div>
            </div>
            <div id="workflow-context-details">
                <!-- Workflow context details will be rendered here by JS -->
            </div>
        </div>
        ` : ''}

        <div class="modal-section">
            <h3>⚙️ Parameters & Configuration</h3>
            <pre class="json-viewer">${JSON.stringify(data.parameters || {}, null, 2)}</pre>
        </div>

        <!-- Enhanced Agent Output Analysis -->
        <div class="enhanced-section agent-output-analysis">
            <div class="section-header">
                <h3>🤖 Enhanced Agent Analysis</h3>
                <div class="section-status" id="agent-data-status">
                    ANALYZING...
                </div>
            </div>

            <div id="agent-output-data-section">
                <!-- Enhanced agent output data will be rendered here -->
            </div>
        </div>

        <div class="modal-section">
            <h3>📄 Raw Results</h3>
            <pre class="json-viewer">${JSON.stringify(data.raw_results || {}, null, 2)}</pre>
        </div>

        ${data.comparison_results ? `
        <div class="modal-section">
            <h3>📈 Statistical Comparison</h3>
            <div class="metrics-grid">
                <div class="metric"><span class="metric-label">Compared to Run</span><span class="metric-value">${data.comparison_results.compared_to_run_id}</span></div>
                <div class="metric"><span class="metric-label">P-Value</span><span class="metric-value">${data.comparison_results.performance_p_value !== null ? data.comparison_results.performance_p_value.toFixed(4) : 'N/A'}</span></div>
                <div class="metric"><span class="metric-label">Significant?</span><span class="metric-value">${data.comparison_results.is_performance_significant !== null ? (data.comparison_results.is_performance_significant ? 'Yes' : 'No') : 'N/A'}</span></div>
            </div>
        </div>
        ` : ''}
    `;

    // Create tool chart in agent modal
    const agentModalToolCtx = document.getElementById('agent-modal-tool-chart')?.getContext('2d');

    // Check if there's an existing chart instance and destroy it
    if (window.modalToolChartInstance) {
        window.modalToolChartInstance.destroy();
        window.modalToolChartInstance = null;
    }

    const toolBreakdown = data.tool_breakdown || {};
    if (agentModalToolCtx && Object.keys(toolBreakdown).length > 0) {
        const tools = Object.keys(toolBreakdown);
        const counts = tools.map(tool => toolBreakdown[tool]);
        const bgColors = tools.map((_, i) => `hsl(${i * (360 / tools.length)}, 70%, 70%)`);

        window.modalToolChartInstance = new Chart(agentModalToolCtx, {
            type: 'pie',
            data: {
                labels: tools,
                datasets: [{
                    label: 'Tool Usage',
                    data: counts,
                    backgroundColor: bgColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'right' },
                    title: { display: false }
                }
            }
        });
    } else if (agentModalToolCtx) {
        agentModalToolCtx.font = "14px Arial";
        agentModalToolCtx.fillStyle = "#666";
        agentModalToolCtx.textAlign = "center";
        agentModalToolCtx.fillText("No tool usage data available", agentModalToolCtx.canvas.width / 2, agentModalToolCtx.canvas.height / 2);
    }

    // Render semantic evaluation details for agent modal
    const agentSemanticDetailsContainer = agentModalBody.querySelector('#agent-semantic-evaluation-details');
    if (agentSemanticDetailsContainer && data.semantic_score !== null) {
        window.renderSemanticEvaluationDetails(agentSemanticDetailsContainer, data);
    }

    // Render context package details for agent modal
    const agentContextPackageContainer = agentModalBody.querySelector('#agent-context-package-details');
    if (agentContextPackageContainer && data.context_package) {
        window.renderContextPackageDetails(agentContextPackageContainer, data.context_package);
    }

    // Render workflow context details for workflow-aware benchmarks
    const workflowContextContainer = agentModalBody.querySelector('#workflow-context-details');
    if (workflowContextContainer && isWorkflowBenchmark(data)) {
        renderWorkflowContextDetails(workflowContextContainer, data);
    }

    // Render rich agent output data
    const agentOutputDataContainer = agentModalBody.querySelector('#agent-output-data-section');
    if (agentOutputDataContainer) {
        renderRichAgentOutputData(agentOutputDataContainer, data);
    }

    // Render enhanced LLM interactions
    const llmInteractionsContainer = agentModalBody.querySelector('#llm-interactions-content');
    if (llmInteractionsContainer) {
        renderLLMInteractions(llmInteractionsContainer, data);
    }

    // Render enhanced tool calls
    const toolCallsContainer = agentModalBody.querySelector('#enhanced-tool-calls-section');
    if (toolCallsContainer) {
        renderEnhancedToolCalls(toolCallsContainer, data);
    }

    // Set up copy run data functionality
    setupCopyRunDataButton(data, runId);

    // Create performance distribution chart
    createPerformanceDistributionChart(data);
}

// Function to render rich agent output data from enhanced_debugging_data
function renderRichAgentOutputData(container, data) {
    console.log('renderRichAgentOutputData: Starting with data:', data);

    // Update status indicator
    const statusElement = document.getElementById('agent-data-status');

    // Try to find rich agent data in enhanced_debugging_data from multiple possible locations
    let enhancedData = null;
    let enhancedAgents = null;

    // Check raw_results.enhanced_debugging_data first (new location)
    if (data.raw_results?.enhanced_debugging_data) {
        enhancedData = data.raw_results.enhanced_debugging_data;
        enhancedAgents = enhancedData.agents;
        console.log('Found enhanced debugging data in raw_results:', enhancedData);
    }
    // Fallback to old location
    else if (data.raw_results?.last_output?.enhanced_debugging_data) {
        enhancedData = data.raw_results.last_output.enhanced_debugging_data;
        enhancedAgents = enhancedData.agents;
        console.log('Found enhanced debugging data in last_output:', enhancedData);
    }

    if (!enhancedAgents || !Array.isArray(enhancedAgents)) {
        console.log('renderRichAgentOutputData: No enhanced agents found, showing fallback');

        // Update status to indicate limited data
        if (statusElement) {
            statusElement.textContent = 'LIMITED DATA';
            statusElement.className = 'section-status warning';
        }

        container.innerHTML = `
            <div class="no-rich-data">
                <div class="warning-banner">
                    <h4>⚠️ Enhanced Debugging Data Not Available</h4>
                    <p>This benchmark run doesn't contain enhanced debugging information.</p>
                </div>

                <div class="possible-reasons">
                    <h5>Possible reasons:</h5>
                    <ul>
                        <li>Benchmark was run before enhanced debugging was implemented</li>
                        <li>Enhanced debugging was disabled during execution</li>
                        <li>Error occurred during data collection</li>
                        <li>Agent communication tracker was not properly integrated</li>
                    </ul>
                </div>

                <div class="fallback-data">
                    <h4>Available Output Data:</h4>
                    <pre class="json-viewer">${JSON.stringify(data.raw_results?.last_output || {}, null, 2)}</pre>
                </div>
            </div>
        `;
        return;
    }

    // Update status to indicate rich data is available
    if (statusElement) {
        statusElement.textContent = 'RICH DATA AVAILABLE';
        statusElement.className = 'section-status excellent';
    }

    console.log('renderRichAgentOutputData: Found enhanced agents:', enhancedAgents.length);

    // Filter agents that have rich output data
    const richAgents = enhancedAgents.filter(agent => {
        if (!agent.output || typeof agent.output !== 'object') return false;

        const richFields = ['combined_resource_context', 'engagement_analysis', 'psychological_assessment', 'strategy_framework', 'wheel', 'ethical_validation'];
        return richFields.some(field => field in agent.output);
    });

    if (richAgents.length === 0) {
        container.innerHTML = `
            <div class="no-rich-data">
                <p>⚠️ No agents with rich output data found.</p>
                <p>Available agents: ${enhancedAgents.map(a => a.agent).join(', ')}</p>
                <details>
                    <summary>Debug Info</summary>
                    <pre>${JSON.stringify(enhancedAgents.map(a => ({
                        agent: a.agent,
                        hasOutput: !!a.output,
                        outputKeys: a.output ? Object.keys(a.output) : []
                    })), null, 2)}</pre>
                </details>
            </div>
        `;
        return;
    }

    console.log('renderRichAgentOutputData: Found rich agents:', richAgents.map(a => a.agent));

    // Render rich agent data
    let html = `
        <div class="rich-agent-data">
            <div class="agent-data-header">
                <h4>🎯 Rich Agent Output Data</h4>
                <p>Detailed output from ${richAgents.length} agents with rich data structures</p>
            </div>
            <div class="agent-data-tabs">
                <div class="tab-buttons">
                    ${richAgents.map((agent, index) => `
                        <button class="tab-button ${index === 0 ? 'active' : ''}" data-tab="agent-${index}">
                            ${agent.agent}
                        </button>
                    `).join('')}
                </div>
                <div class="tab-content">
                    ${richAgents.map((agent, index) => `
                        <div id="agent-${index}-tab" class="tab-pane ${index === 0 ? 'active' : ''}">
                            ${renderSingleAgentRichOutput(agent, data)}
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;

    // Add tab switching functionality
    const tabButtons = container.querySelectorAll('.tab-button');
    const tabPanes = container.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            const targetPane = container.querySelector(`#${targetTab}-tab`);
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });
}

// Function to render a single agent's rich output
function renderSingleAgentRichOutput(agent, workflowData = null) {
    const output = agent.output;
    const agentName = agent.agent;

    // Use the existing rich output rendering functions
    if (typeof window.extractAgentOutputData === 'function') {
        // Pass the full data context to access enhanced_debugging_data
        const extractedData = window.extractAgentOutputData(agent, workflowData);
        if (typeof window.renderRichAgentOutput === 'function') {
            return `
                <div class="single-agent-rich-output">
                    <div class="agent-header">
                        <h5>🤖 ${agentName} Agent Output</h5>
                        <div class="agent-meta">
                            <span class="badge">Stage: ${agent.stage || 'Unknown'}</span>
                            <span class="badge">Duration: ${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                            <span class="badge ${agent.success ? 'success' : 'error'}">
                                ${agent.success ? '✅ Success' : '❌ Failed'}
                            </span>
                        </div>
                    </div>
                    <div class="rich-output-content">
                        ${window.renderRichAgentOutput(extractedData)}
                    </div>
                </div>
            `;
        }
    }

    // Fallback to JSON display
    return `
        <div class="single-agent-rich-output">
            <div class="agent-header">
                <h5>🤖 ${agentName} Agent Output</h5>
                <div class="agent-meta">
                    <span class="badge">Stage: ${agent.stage || 'Unknown'}</span>
                    <span class="badge">Duration: ${agent.duration_ms ? agent.duration_ms.toFixed(2) + 'ms' : 'N/A'}</span>
                    <span class="badge ${agent.success ? 'success' : 'error'}">
                        ${agent.success ? '✅ Success' : '❌ Failed'}
                    </span>
                </div>
            </div>
            <div class="rich-output-content">
                <pre class="json-viewer">${JSON.stringify(output, null, 2)}</pre>
            </div>
        </div>
    `;
}

// Enhanced Analysis Functions for Agent Performance
function analyzeAgentPerformance(data) {
    const meanDuration = parseFloat(data.mean_duration) || 0;
    const successRate = parseFloat(data.success_rate) || 0;
    const semanticScore = parseFloat(data.semantic_score) || 0;
    const cost = parseFloat(data.estimated_cost) || 0;

    return {
        performance: {
            status: meanDuration < 1000 ? 'excellent' : meanDuration < 3000 ? 'good' : meanDuration < 5000 ? 'warning' : 'critical',
            trend: 'stable', // Would need historical data for real trend analysis
            trendIcon: '→'
        },
        quality: {
            status: semanticScore >= 0.8 ? 'excellent' : semanticScore >= 0.6 ? 'good' : semanticScore >= 0.4 ? 'warning' : 'critical',
            trend: 'stable',
            trendIcon: '→'
        },
        reliability: {
            status: successRate >= 0.95 ? 'excellent' : successRate >= 0.85 ? 'good' : successRate >= 0.7 ? 'warning' : 'critical',
            trend: 'stable',
            trendIcon: '→'
        },
        cost: {
            status: cost < 0.001 ? 'excellent' : cost < 0.01 ? 'good' : cost < 0.05 ? 'warning' : 'critical',
            trend: 'stable',
            trendIcon: '→'
        }
    };
}

function calculateHealthStatus(data) {
    const meanDuration = parseFloat(data.mean_duration) || 0;
    const successRate = parseFloat(data.success_rate) || 0;
    const semanticScore = parseFloat(data.semantic_score) || 0;
    const cost = parseFloat(data.estimated_cost) || 0;

    // Calculate weighted health score
    let score = 0;
    let weights = { performance: 25, quality: 30, reliability: 30, cost: 15 };

    // Performance score (0-25)
    if (meanDuration < 1000) score += 25;
    else if (meanDuration < 3000) score += 20;
    else if (meanDuration < 5000) score += 15;
    else score += 5;

    // Quality score (0-30)
    score += Math.round(semanticScore * 30);

    // Reliability score (0-30)
    score += Math.round(successRate * 30);

    // Cost efficiency score (0-15)
    if (cost < 0.001) score += 15;
    else if (cost < 0.01) score += 12;
    else if (cost < 0.05) score += 8;
    else score += 3;

    // Determine status level
    let level, icon, label;
    if (score >= 85) {
        level = 'excellent';
        icon = '🟢';
        label = 'Excellent';
    } else if (score >= 70) {
        level = 'good';
        icon = '🟡';
        label = 'Good';
    } else if (score >= 50) {
        level = 'warning';
        icon = '🟠';
        label = 'Needs Attention';
    } else {
        level = 'critical';
        icon = '🔴';
        label = 'Critical Issues';
    }

    return { score, level, icon, label };
}

function generateIntelligentRecommendations(data, performanceAnalysis) {
    const recommendations = {
        critical: [],
        important: [],
        optimization: []
    };

    const meanDuration = parseFloat(data.mean_duration) || 0;
    const successRate = parseFloat(data.success_rate) || 0;
    const semanticScore = parseFloat(data.semantic_score) || 0;
    const cost = parseFloat(data.estimated_cost) || 0;
    const temperature = parseFloat(data.llm_temperature) || 0;

    // Critical issues
    if (successRate < 0.7) {
        recommendations.critical.push('Success rate below 70% - Review agent instructions and error patterns immediately');
    }
    if (meanDuration > 10000) {
        recommendations.critical.push('Response time exceeds 10 seconds - Optimize prompts or consider model change');
    }
    if (semanticScore < 0.3) {
        recommendations.critical.push('Quality score critically low - Review evaluation criteria and agent output');
    }

    // Important improvements
    if (successRate < 0.9) {
        recommendations.important.push('Success rate could be improved - Analyze failure patterns');
    }
    if (meanDuration > 5000) {
        recommendations.important.push('Response time is slow - Consider prompt optimization');
    }
    if (cost > 0.05) {
        recommendations.important.push('High cost per run - Consider using a more efficient model');
    }

    // Optimization suggestions
    if (temperature > 0.8) {
        recommendations.optimization.push('High temperature may cause inconsistent outputs - Consider reducing for more reliable results');
    }
    if (semanticScore < 0.8 && semanticScore >= 0.6) {
        recommendations.optimization.push('Quality score has room for improvement - Review semantic evaluation details');
    }

    return recommendations;
}

// Function to render agent output display - Made global
function renderAgentOutputDisplay(data) {
    console.log('renderAgentOutputDisplay: Starting with data:', data);

    // Check if this is a ResourceAgent - look in multiple locations
    const agentRole = data.raw_results?.agent_role || data.agent_role;
    console.log('Agent role detected:', agentRole);

    if (agentRole === 'ResourceAgent' || agentRole === 'resource') {
        // Try multiple locations for ResourceAgent data
        let resourceContext = null;

        // Location 1: raw_results.last_output.resource_context
        if (data.raw_results?.last_output?.resource_context) {
            resourceContext = data.raw_results.last_output.resource_context;
            console.log('Found ResourceAgent data in raw_results.last_output');
        }
        // Location 2: enhanced_debugging_data.agents[0].output.resource_context
        else if (data.enhanced_debugging_data?.agents?.[0]?.output?.resource_context) {
            resourceContext = data.enhanced_debugging_data.agents[0].output.resource_context;
            console.log('Found ResourceAgent data in enhanced_debugging_data');
        }

        if (resourceContext) {
            console.log('Rendering ResourceAgent with context:', resourceContext);
            return renderResourceAgentOutput({ ...data, resource_context: resourceContext });
        }
    }

    // Extract agent response from multiple possible locations
    let agentResponse = null;
    let inputContext = null;
    let outputContext = null;

    // Try to get the agent response from various locations
    if (data.raw_results?.last_output?.user_response) {
        agentResponse = data.raw_results.last_output.user_response;
    } else if (data.context_package?.agent_output?.final_output?.user_response) {
        agentResponse = data.context_package.agent_output.final_output.user_response;
    } else if (data.enhanced_debugging_data?.agents?.[0]?.output?.user_response) {
        agentResponse = data.enhanced_debugging_data.agents[0].output.user_response;
    }

    // Get input context
    if (data.context_package?.input_context?.scenario_input) {
        inputContext = data.context_package.input_context.scenario_input;
    } else if (data.enhanced_debugging_data?.agents?.[0]?.input) {
        inputContext = data.enhanced_debugging_data.agents[0].input;
    }

    // Get output context
    if (data.raw_results?.last_output) {
        outputContext = data.raw_results.last_output;
    } else if (data.enhanced_debugging_data?.agents?.[0]?.output) {
        outputContext = data.enhanced_debugging_data.agents[0].output;
    }

    if (!agentResponse) {
        return `
            <div class="no-agent-output">
                <div class="warning-banner">
                    <h4>⚠️ No Agent Response Found</h4>
                    <p>Unable to locate the agent's response in the benchmark data.</p>
                </div>
                <div class="debug-info">
                    <h5>Available Data Locations:</h5>
                    <ul>
                        <li>raw_results.last_output: ${data.raw_results?.last_output ? 'Available' : 'Missing'}</li>
                        <li>context_package.agent_output: ${data.context_package?.agent_output ? 'Available' : 'Missing'}</li>
                        <li>enhanced_debugging_data.agents: ${data.enhanced_debugging_data?.agents?.length || 0} agents</li>
                    </ul>
                </div>
            </div>
        `;
    }

    return `
        <div class="agent-output-display">
            <div class="output-sections">
                <div class="input-section">
                    <h4>📥 Input Context</h4>
                    <div class="input-details">
                        ${inputContext ? `
                            <div class="context-item">
                                <span class="context-label">User Input:</span>
                                <span class="context-value">"${inputContext.user_input || 'N/A'}"</span>
                            </div>
                            <div class="context-item">
                                <span class="context-label">Scenario Type:</span>
                                <span class="context-value">${inputContext.scenario_type || 'N/A'}</span>
                            </div>
                            <div class="context-item">
                                <span class="context-label">User Profile ID:</span>
                                <span class="context-value">${inputContext.user_profile_id || 'N/A'}</span>
                            </div>
                        ` : `
                            <p class="no-data">Input context not available</p>
                        `}
                    </div>
                </div>

                <div class="response-section">
                    <h4>💬 Agent Response</h4>
                    <div class="response-display">
                        <div class="response-text">
                            ${agentResponse}
                        </div>
                        <div class="response-meta">
                            <span class="response-length">${agentResponse.length} characters</span>
                            <span class="response-words">${agentResponse.split(' ').length} words</span>
                        </div>
                    </div>
                </div>

                <div class="output-section">
                    <h4>📤 Output Context</h4>
                    <div class="output-details">
                        ${outputContext ? `
                            <div class="context-item">
                                <span class="context-label">Next Agent:</span>
                                <span class="context-value">${outputContext.next_agent || 'N/A'}</span>
                            </div>
                            <div class="context-item">
                                <span class="context-label">Context Packet:</span>
                                <span class="context-value">${Object.keys(outputContext.context_packet || {}).length} items</span>
                            </div>
                        ` : `
                            <p class="no-data">Output context not available</p>
                        `}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Function to render LLM interactions from enhanced debugging data - Made global
window.renderLLMInteractions = function(container, data) {
    console.log('renderLLMInteractions: Starting with data:', data);

    const statusElement = document.getElementById('llm-interactions-status');

    // Try to find LLM interactions in multiple possible locations
    let llmInteractions = [];

    // Check multiple locations for LLM interactions data
    if (data.llm_interactions && Array.isArray(data.llm_interactions)) {
        llmInteractions = data.llm_interactions;
        console.log('Found LLM interactions at top level:', llmInteractions.length);
    } else if (data.enhanced_debugging_data?.llm_interactions && Array.isArray(data.enhanced_debugging_data.llm_interactions)) {
        llmInteractions = data.enhanced_debugging_data.llm_interactions;
        console.log('Found LLM interactions in enhanced_debugging_data:', llmInteractions.length);
    } else if (data.raw_results?.enhanced_debugging_data?.llm_interactions && Array.isArray(data.raw_results.enhanced_debugging_data.llm_interactions)) {
        llmInteractions = data.raw_results.enhanced_debugging_data.llm_interactions;
        console.log('Found LLM interactions in raw_results.enhanced_debugging_data:', llmInteractions.length);
    }

    if (llmInteractions.length === 0) {
        if (statusElement) {
            statusElement.textContent = 'NO LLM CALLS DETECTED';
            statusElement.className = 'section-status warning';
        }

        // Check if we have token data indicating LLM calls were made
        const hasTokenData = (data.total_input_tokens > 0 || data.total_output_tokens > 0);
        const llmCallsReported = data.llm_calls > 0 || data.enhanced_debugging_data?.summary?.llm_interactions?.total > 0;

        container.innerHTML = `
            <div class="no-llm-data">
                ${hasTokenData || llmCallsReported ? `
                    <div class="warning-banner">
                        <h4>⚠️ LLM Calls Detected But Details Missing</h4>
                        <p>Token usage indicates LLM calls were made (${data.total_input_tokens || 0} input, ${data.total_output_tokens || 0} output tokens), but detailed interaction data is not available.</p>
                    </div>
                    <div class="possible-reasons">
                        <h5>Possible reasons:</h5>
                        <ul>
                            <li>LLM call tracking not properly configured during execution</li>
                            <li>Enhanced debugging data collection failed</li>
                            <li>Data structure mismatch between tracking and display</li>
                            <li>LLM calls made outside of enhanced debugging scope</li>
                        </ul>
                    </div>
                ` : `
                    <div class="info-banner">
                        <h4>ℹ️ No LLM Interactions in This Run</h4>
                        <p>This agent execution did not involve any LLM calls.</p>
                    </div>
                    <div class="possible-reasons">
                        <h5>This could be normal if:</h5>
                        <ul>
                            <li>Agent uses cached responses or predefined logic</li>
                            <li>Agent execution failed before reaching LLM calls</li>
                            <li>Agent is designed to work without LLM for this scenario</li>
                            <li>Tool-only agent that doesn't require LLM reasoning</li>
                        </ul>
                    </div>
                `}
                <div class="debug-info">
                    <h5>Debug Information:</h5>
                    <p>Total input tokens: ${data.total_input_tokens || 0}</p>
                    <p>Total output tokens: ${data.total_output_tokens || 0}</p>
                    <p>LLM calls reported: ${data.llm_calls || 0}</p>
                    <p>Enhanced debugging enabled: ${data.enhanced_debugging_data?.enabled || data.raw_results?.enhanced_debugging_data?.enabled ? 'Yes' : 'No'}</p>
                    <p>Summary LLM interactions: ${data.enhanced_debugging_data?.summary?.llm_interactions?.total || 0}</p>
                </div>
            </div>
        `;
        return;
    }

    if (statusElement) {
        statusElement.textContent = `${llmInteractions.length} LLM CALLS FOUND`;
        statusElement.className = 'section-status excellent';
    }

    // Render LLM interactions
    let html = `
        <div class="llm-interactions-list">
            <div class="interactions-summary">
                <h4>📊 LLM Interactions Summary</h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Calls:</span>
                        <span class="stat-value">${llmInteractions.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Input Tokens:</span>
                        <span class="stat-value">${llmInteractions.reduce((sum, i) => sum + (i.token_usage?.input || 0), 0)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Output Tokens:</span>
                        <span class="stat-value">${llmInteractions.reduce((sum, i) => sum + (i.token_usage?.output || 0), 0)}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Average Duration:</span>
                        <span class="stat-value">${(llmInteractions.reduce((sum, i) => sum + (i.duration_ms || 0), 0) / llmInteractions.length).toFixed(2)}ms</span>
                    </div>
                </div>
            </div>
            <div class="interactions-details">
                ${llmInteractions.map((interaction, index) => `
                    <div class="interaction-card">
                        <div class="interaction-header">
                            <h5>🧠 LLM Call #${index + 1}</h5>
                            <div class="interaction-meta">
                                <span class="badge">Agent: ${interaction.agent}</span>
                                <span class="badge">Model: ${interaction.model}</span>
                                <span class="badge">Duration: ${interaction.duration_ms?.toFixed(2) || 'N/A'}ms</span>
                                <span class="badge ${interaction.success ? 'success' : 'error'}">
                                    ${interaction.success ? '✅ Success' : '❌ Failed'}
                                </span>
                            </div>
                        </div>
                        <div class="interaction-content">
                            <div class="interaction-section">
                                <h6>📝 Prompt</h6>
                                <div class="prompt-content">
                                    <pre class="code-block">${interaction.prompt || 'N/A'}</pre>
                                </div>
                            </div>
                            <div class="interaction-section">
                                <h6>💬 Response</h6>
                                <div class="response-content">
                                    <pre class="code-block">${interaction.response || 'N/A'}</pre>
                                </div>
                            </div>
                            <div class="interaction-section">
                                <h6>📊 Token Usage</h6>
                                <div class="token-usage">
                                    <span class="token-stat">Input: ${interaction.token_usage?.input || 0}</span>
                                    <span class="token-stat">Output: ${interaction.token_usage?.output || 0}</span>
                                    <span class="token-stat">Total: ${(interaction.token_usage?.input || 0) + (interaction.token_usage?.output || 0)}</span>
                                </div>
                            </div>
                            ${interaction.error ? `
                                <div class="interaction-section error">
                                    <h6>❌ Error</h6>
                                    <div class="error-content">
                                        <pre class="code-block error">${interaction.error}</pre>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// Function to render ResourceAgent output using our new component system
function renderResourceAgentOutput(data) {
    console.log('renderResourceAgentOutput: Rendering ResourceAgent data');

    // Get resource context from the data structure
    const resourceContext = data.resource_context || data.raw_results?.last_output?.resource_context;

    if (!resourceContext) {
        console.error('No resource context found in data:', data);
        return `
            <div class="agent-output-error">
                <h4>⚠️ ResourceAgent Data Not Found</h4>
                <p>Unable to locate resource context data in the benchmark results.</p>
                <details>
                    <summary>Debug Information</summary>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </details>
            </div>
        `;
    }

    const resources = resourceContext.resources || {};
    const environment = resourceContext.environment || {};
    const analysis = resourceContext.analysis_summary || {};

    console.log('ResourceAgent data found:', {
        feasibility: resourceContext.feasibility_score,
        inventory: resources.inventory_count,
        skills: resources.capabilities_summary?.total_skills,
        limitations: resources.limitations_count
    });

    // Render using our enhanced ResourceAgent component structure
    return `
        <div class="agent-output-success">
            <div class="output-header">
                <h4>🎒 ResourceAgent Analysis Complete</h4>
                <div class="output-meta">
                    <span class="badge success">✅ Analysis Successful</span>
                    <span class="badge">Feasibility: ${(resourceContext.feasibility_score * 100).toFixed(1)}%</span>
                    <span class="badge">Inventory: ${resources.inventory_count || 0} items</span>
                    <span class="badge">Skills: ${resources.capabilities_summary?.total_skills || 0}</span>
                    <span class="badge">Limitations: ${resources.limitations_count || 0}</span>
                </div>
            </div>

            <div class="resource-agent-display">
                ${renderResourceSummaryCards(resourceContext)}
                ${renderResourceInventorySection(resources)}
                ${renderResourceCapabilitiesSection(resources)}
                ${renderResourceLimitationsSection(resources)}
                ${renderResourceEnvironmentSection(environment)}
                ${renderResourceAnalysisSection(analysis)}
            </div>
        </div>
    `;
}

// Function to render resource summary cards
function renderResourceSummaryCards(resourceContext) {
    const resources = resourceContext.resources || {};
    const feasibilityScore = resourceContext.feasibility_score || 0;

    return `
        <div class="resource-summary-cards">
            <div class="summary-card inventory-card">
                <div class="card-icon">🎒</div>
                <div class="card-content">
                    <div class="card-value">${resources.inventory_count || 0}</div>
                    <div class="card-label">Inventory Items</div>
                    <div class="card-detail">Available resources</div>
                </div>
            </div>

            <div class="summary-card capabilities-card">
                <div class="card-icon">⭐</div>
                <div class="card-content">
                    <div class="card-value">${resources.capabilities_summary?.total_skills || 0}</div>
                    <div class="card-label">Skills</div>
                    <div class="card-detail">Identified capabilities</div>
                </div>
            </div>

            <div class="summary-card limitations-card">
                <div class="card-icon">⚠️</div>
                <div class="card-content">
                    <div class="card-value">${resources.limitations_count || 0}</div>
                    <div class="card-label">Limitations</div>
                    <div class="card-detail">Reported constraints</div>
                </div>
            </div>

            <div class="summary-card feasibility-card">
                <div class="card-icon">📊</div>
                <div class="card-content">
                    <div class="card-value">${(feasibilityScore * 100).toFixed(1)}%</div>
                    <div class="card-label">Feasibility</div>
                    <div class="card-detail">Overall score</div>
                </div>
            </div>
        </div>
    `;
}

// Function to render resource inventory section
function renderResourceInventorySection(resources) {
    const inventory = resources.available_inventory || [];
    if (inventory.length === 0) {
        return '<div class="no-data">No inventory items available</div>';
    }

    // Group by category
    const categories = {};
    inventory.forEach(item => {
        const category = item.category || 'Other';
        if (!categories[category]) {
            categories[category] = [];
        }
        categories[category].push(item);
    });

    return `
        <div class="resource-section inventory-section">
            <h4>🎒 Inventory Analysis (${inventory.length} items)</h4>
            <div class="inventory-grid">
                ${Object.entries(categories).map(([category, items]) => `
                    <div class="category-section">
                        <h5>${category} (${items.length})</h5>
                        <div class="items-grid">
                            ${items.map(item => `
                                <div class="inventory-item">
                                    <div class="item-header">
                                        <span class="item-name">${item.name}</span>
                                        <span class="item-code">${item.code}</span>
                                    </div>
                                    <div class="item-details">
                                        <div class="item-location">📍 ${item.location}</div>
                                        <div class="item-ownership">👤 ${item.ownership}</div>
                                        ${item.notes ? `<div class="item-notes">💭 ${item.notes}</div>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Function to render resource capabilities section
function renderResourceCapabilitiesSection(resources) {
    const capabilities = resources.capabilities || {};
    const domains = Object.keys(capabilities);

    if (domains.length === 0) {
        return '<div class="no-data">No capabilities data available</div>';
    }

    return `
        <div class="resource-section capabilities-section">
            <h4>⭐ Capabilities Analysis</h4>
            ${domains.map(domain => `
                <div class="domain-section">
                    <h5>${domain} Domain</h5>
                    <div class="skills-grid">
                        ${capabilities[domain].map(skill => `
                            <div class="skill-item">
                                <div class="skill-header">
                                    <span class="skill-name">${skill.name}</span>
                                    <span class="skill-level level-${getSkillLevelClass(skill.level)}">${skill.level}</span>
                                </div>
                                <div class="skill-metrics">
                                    <div class="metric">
                                        <span class="metric-label">Awareness</span>
                                        <div class="metric-bar">
                                            <div class="metric-fill" style="width: ${skill.awareness}%"></div>
                                        </div>
                                        <span class="metric-value">${skill.awareness}%</span>
                                    </div>
                                    <div class="metric">
                                        <span class="metric-label">Enjoyment</span>
                                        <div class="metric-bar">
                                            <div class="metric-fill enjoyment" style="width: ${skill.enjoyment}%"></div>
                                        </div>
                                        <span class="metric-value">${skill.enjoyment}%</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// Function to render resource limitations section
function renderResourceLimitationsSection(resources) {
    const limitations = resources.reported_limitations || [];

    if (limitations.length === 0) {
        return '<div class="no-data">No limitations reported</div>';
    }

    return `
        <div class="resource-section limitations-section">
            <h4>⚠️ Limitations Analysis (${limitations.length} constraints)</h4>
            <div class="limitations-grid">
                ${limitations.map(limitation => `
                    <div class="limitation-item severity-${getSeverityClass(limitation.severity)}">
                        <div class="limitation-header">
                            <span class="limitation-type">${limitation.type}</span>
                            <span class="limitation-severity">${limitation.severity}/100</span>
                            <span class="limitation-status ${limitation.is_active ? 'active' : 'inactive'}">
                                ${limitation.is_active ? '🔴 Active' : '🟢 Inactive'}
                            </span>
                        </div>
                        <div class="limitation-description">${limitation.description}</div>
                        <div class="severity-bar">
                            <div class="severity-fill" style="width: ${limitation.severity}%"></div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Function to render resource environment section
function renderResourceEnvironmentSection(environment) {
    return `
        <div class="resource-section environment-section">
            <h4>🌍 Environment Analysis</h4>
            <div class="environment-grid">
                <div class="env-card">
                    <h5>📍 Location Context</h5>
                    <div class="env-details">
                        <div class="env-item">
                            <span class="env-label">Type:</span>
                            <span class="env-value">${environment.analyzed_type || 'Unknown'}</span>
                        </div>
                        <div class="env-item">
                            <span class="env-label">Space Size:</span>
                            <span class="env-value">${environment.space_size || 'Unknown'}</span>
                        </div>
                        <div class="env-item">
                            <span class="env-label">Privacy Level:</span>
                            <span class="env-value">${environment.privacy_level || 0}%</span>
                        </div>
                        <div class="env-item">
                            <span class="env-label">Noise Level:</span>
                            <span class="env-value">${environment.noise_level || 'Unknown'}</span>
                        </div>
                    </div>
                </div>

                <div class="env-card">
                    <h5>🎯 Activity Support</h5>
                    <div class="activity-support">
                        ${Object.entries(environment.activity_support || {}).map(([activity, supported]) => `
                            <div class="support-item ${supported ? 'supported' : 'not-supported'}">
                                <span class="support-icon">${supported ? '✅' : '❌'}</span>
                                <span class="support-label">${activity.replace('_', ' ')}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Function to render resource analysis section
function renderResourceAnalysisSection(analysis) {
    return `
        <div class="resource-section analysis-section">
            <h4>💡 Analysis Summary</h4>
            <div class="analysis-grid">
                <div class="analysis-card opportunities">
                    <h5>🚀 Key Opportunities</h5>
                    <ul>
                        ${(analysis.key_opportunities || []).map(opp => `<li>${opp}</li>`).join('')}
                    </ul>
                </div>

                <div class="analysis-card constraints">
                    <h5>⚠️ Primary Constraints</h5>
                    <ul>
                        ${(analysis.primary_constraints || []).map(constraint => `<li>${constraint}</li>`).join('')}
                    </ul>
                </div>

                <div class="analysis-card recommendations">
                    <h5>🎯 Recommended Activities</h5>
                    <div class="activity-tags">
                        ${(analysis.recommended_activity_types || []).map(activity =>
                            `<span class="activity-tag">${activity}</span>`
                        ).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Helper functions
function getSkillLevelClass(level) {
    if (level >= 80) return 'expert';
    if (level >= 60) return 'advanced';
    if (level >= 40) return 'intermediate';
    if (level >= 20) return 'beginner';
    return 'novice';
}

function getSeverityClass(severity) {
    if (severity >= 80) return 'critical';
    if (severity >= 60) return 'high';
    if (severity >= 40) return 'medium';
    return 'low';
}

// Function to render tool usage for different agent types
function renderToolUsageForAgent(data) {
    // Check for ResourceAgent in multiple locations
    const agentRole = data.raw_results?.agent_role || data.agent_role;

    // For ResourceAgent, show internal processing analysis
    if (agentRole === 'ResourceAgent' || agentRole === 'resource') {
        // Try multiple locations for ResourceAgent data
        let resourceContext = data.resource_context ||
                             data.raw_results?.last_output?.resource_context ||
                             data.enhanced_debugging_data?.agents?.[0]?.output?.resource_context;

        if (resourceContext) {
            return `
                <div class="agent-tool-analysis">
                    <h5>🔧 Internal Processing Analysis</h5>
                    <div class="processing-metrics">
                        <div class="metric">
                            <span class="metric-label">Data Sources Analyzed</span>
                            <span class="metric-value">4</span>
                            <span class="metric-detail">Inventory, Skills, Limitations, Environment</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Inventory Items Processed</span>
                            <span class="metric-value">${resourceContext.resources?.inventory_count || 0}</span>
                            <span class="metric-detail">Available resources catalogued</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Skills Evaluated</span>
                            <span class="metric-value">${resourceContext.resources?.capabilities_summary?.total_skills || 0}</span>
                            <span class="metric-detail">Capabilities assessed</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Limitations Identified</span>
                            <span class="metric-value">${resourceContext.resources?.limitations_count || 0}</span>
                            <span class="metric-detail">Constraints analyzed</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Feasibility Score</span>
                            <span class="metric-value">${(resourceContext.feasibility_score * 100).toFixed(1)}%</span>
                            <span class="metric-detail">Overall resource assessment</span>
                        </div>
                    </div>
                    <div class="processing-note">
                        <p><strong>Note:</strong> ResourceAgent performs internal data analysis rather than external tool calls.
                        The metrics above show the scope of data processing and analysis performed.</p>
                    </div>
                </div>
            `;
        }
    }

    // For other agents, show standard tool call metrics
    if (data.tool_call_details && data.tool_call_details.total_calls > 0) {
        return `
            <div class="standard-tool-metrics">
                <div class="metrics-grid">
                    <div class="metric">
                        <span class="metric-label">Total Calls</span>
                        <span class="metric-value">${data.tool_call_details.total_calls}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Real Calls</span>
                        <span class="metric-value real">${data.tool_call_details.real_calls || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Mocked Calls</span>
                        <span class="metric-value mocked">${data.tool_call_details.mocked_calls || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // Default case - no tool usage
    return `
        <div class="no-tool-usage">
            <p>No external tool calls detected for this agent execution.</p>
            <p class="debug-info">Agent: ${data.agent_role || 'Unknown'}, LLM calls: ${data.llm_calls || 0}</p>
        </div>
    `;
}

// Function to render enhanced tool calls from debugging data - Made global
window.renderEnhancedToolCalls = function(container, data) {
    console.log('renderEnhancedToolCalls: Starting with data:', data);

    // Try to find tool calls in enhanced_debugging_data
    let toolCalls = [];

    if (data.raw_results?.enhanced_debugging_data?.tool_calls) {
        toolCalls = data.raw_results.enhanced_debugging_data.tool_calls;
    }

    const statusElement = document.getElementById('tool-calls-status');

    if (toolCalls.length === 0) {
        if (statusElement) {
            statusElement.textContent = 'NO TOOL CALLS DETECTED';
            statusElement.className = 'section-status good';
        }

        container.innerHTML = `
            <div class="no-tool-calls">
                <div class="info-banner">
                    <h4>ℹ️ No Tool Calls in This Run</h4>
                    <p>This agent execution completed successfully without using any tools.</p>
                </div>
                <div class="possible-reasons">
                    <h5>This is normal when:</h5>
                    <ul>
                        <li>Agent provides direct responses without needing external data</li>
                        <li>Agent uses cached information or built-in knowledge</li>
                        <li>Simple conversational interactions that don't require tools</li>
                        <li>Agent is designed for basic communication tasks</li>
                    </ul>
                </div>
                <div class="debug-info">
                    <h5>Debug Information:</h5>
                    <p>Tool call details total: ${data.tool_call_details?.total_calls || 0}</p>
                    <p>Enhanced debugging enabled: ${data.raw_results?.enhanced_debugging_data?.enabled ? 'Yes' : 'No'}</p>
                    <p>Available tools in registry: ${data.raw_results?.enhanced_debugging_data?.summary?.available_tools || 'N/A'}</p>
                </div>
            </div>
        `;
        return;
    }

    if (statusElement) {
        statusElement.textContent = `${toolCalls.length} TOOL CALLS FOUND`;
        statusElement.className = 'section-status excellent';
    }

    // Render tool calls
    let html = `
        <div class="tool-calls-enhanced">
            <div class="tool-calls-summary">
                <h4>🛠️ Tool Calls Summary</h4>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Calls:</span>
                        <span class="stat-value">${toolCalls.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Real Mode Calls:</span>
                        <span class="stat-value">${toolCalls.filter(tc => tc.execution_mode === 'real').length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Mock Mode Calls:</span>
                        <span class="stat-value">${toolCalls.filter(tc => tc.execution_mode === 'mock').length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Success Rate:</span>
                        <span class="stat-value">${((toolCalls.filter(tc => tc.success).length / toolCalls.length) * 100).toFixed(1)}%</span>
                    </div>
                </div>
            </div>
            <div class="tool-calls-details">
                ${toolCalls.map((toolCall, index) => `
                    <div class="tool-call-card">
                        <div class="tool-call-header">
                            <h5>🔧 ${toolCall.tool_name} #${index + 1}</h5>
                            <div class="tool-call-meta">
                                <span class="badge ${toolCall.execution_mode === 'real' ? 'real-mode' : 'mock-mode'}">
                                    ${toolCall.execution_mode === 'real' ? '🟢 Real' : '🟡 Mock'}
                                </span>
                                <span class="badge">Duration: ${toolCall.duration_ms?.toFixed(2) || 'N/A'}ms</span>
                                <span class="badge ${toolCall.success ? 'success' : 'error'}">
                                    ${toolCall.success ? '✅ Success' : '❌ Failed'}
                                </span>
                            </div>
                        </div>
                        <div class="tool-call-content">
                            <div class="tool-call-section">
                                <h6>📥 Input</h6>
                                <div class="tool-input">
                                    <pre class="code-block">${JSON.stringify(toolCall.tool_input || {}, null, 2)}</pre>
                                </div>
                            </div>
                            <div class="tool-call-section">
                                <h6>📤 Output</h6>
                                <div class="tool-output">
                                    <pre class="code-block">${JSON.stringify(toolCall.tool_output || {}, null, 2)}</pre>
                                </div>
                            </div>
                            ${toolCall.error ? `
                                <div class="tool-call-section error">
                                    <h6>❌ Error</h6>
                                    <div class="error-content">
                                        <pre class="code-block error">${toolCall.error}</pre>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// Function to set up copy run data functionality - Made global
window.setupCopyRunDataButton = function(data, runId) {
    const copyButton = document.getElementById('copy-run-data-btn');
    const refreshButton = document.getElementById('refresh-modal-btn');

    if (copyButton) {
        copyButton.addEventListener('click', async function() {
            try {
                // Prepare comprehensive run data for export
                const exportData = {
                    run_id: runId,
                    export_timestamp: new Date().toISOString(),
                    benchmark_run_data: data,
                    enhanced_debugging_data: data.raw_results?.enhanced_debugging_data || null,
                    parameters_and_configuration: data.parameters || {},
                    performance_metrics: {
                        mean_duration: data.mean_duration,
                        success_rate: data.success_rate,
                        total_input_tokens: data.total_input_tokens,
                        total_output_tokens: data.total_output_tokens,
                        estimated_cost: data.estimated_cost,
                        semantic_score: data.semantic_score
                    },
                    llm_interactions: data.raw_results?.enhanced_debugging_data?.llm_interactions || [],
                    tool_calls: data.raw_results?.enhanced_debugging_data?.tool_calls || [],
                    agent_communications: data.raw_results?.enhanced_debugging_data?.agents || [],
                    raw_results: data.raw_results || {}
                };

                // Copy to clipboard
                const jsonString = JSON.stringify(exportData, null, 2);
                await navigator.clipboard.writeText(jsonString);

                // Show success feedback
                const originalText = copyButton.textContent;
                copyButton.textContent = '✅ Copied!';
                copyButton.style.background = 'rgba(76, 175, 80, 0.3)';

                setTimeout(() => {
                    copyButton.textContent = originalText;
                    copyButton.style.background = '';
                }, 2000);

                console.log('Run data copied to clipboard:', exportData);

            } catch (error) {
                console.error('Failed to copy run data:', error);

                // Show error feedback
                const originalText = copyButton.textContent;
                copyButton.textContent = '❌ Copy Failed';
                copyButton.style.background = 'rgba(244, 67, 54, 0.3)';

                setTimeout(() => {
                    copyButton.textContent = originalText;
                    copyButton.style.background = '';
                }, 2000);

                // Fallback: create downloadable file
                try {
                    const exportData = {
                        run_id: runId,
                        export_timestamp: new Date().toISOString(),
                        benchmark_run_data: data
                    };

                    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `benchmark_run_${runId.substring(0, 8)}_${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    copyButton.textContent = '📥 Downloaded';
                    copyButton.style.background = 'rgba(33, 150, 243, 0.3)';

                    setTimeout(() => {
                        copyButton.textContent = originalText;
                        copyButton.style.background = '';
                    }, 2000);

                } catch (downloadError) {
                    console.error('Fallback download also failed:', downloadError);
                }
            }
        });
    }

    if (refreshButton) {
        refreshButton.addEventListener('click', function() {
            // Refresh the modal by re-opening it
            if (typeof openAgentEvaluationModal === 'function') {
                openAgentEvaluationModal(runId);
            }
        });
    }
}

// Helper functions for LLM analysis
function getModelInsight(model) {
    if (!model) return 'Model information not available';

    const modelInsights = {
        'gpt-4': 'High-quality, expensive - Best for complex tasks',
        'gpt-3.5-turbo': 'Balanced cost/performance - Good for most tasks',
        'claude-3': 'Excellent reasoning - Good for analytical tasks',
        'mistral': 'Cost-effective - Good for simple tasks'
    };

    for (const [key, insight] of Object.entries(modelInsights)) {
        if (model.toLowerCase().includes(key)) {
            return insight;
        }
    }

    return 'Custom model - Performance characteristics unknown';
}

function getTemperatureInsight(temperature) {
    if (temperature === null || temperature === undefined) return 'Temperature not specified';

    const temp = parseFloat(temperature);
    if (temp < 0.3) return 'Very deterministic - Consistent but potentially rigid';
    if (temp < 0.7) return 'Balanced - Good mix of consistency and creativity';
    if (temp < 1.0) return 'Creative - More varied but potentially inconsistent';
    return 'Highly creative - May produce unpredictable results';
}

function calculateTokenCost(tokens, type) {
    if (!tokens) return 'N/A';

    // Rough estimates - would need actual pricing data
    const inputCostPer1k = 0.001;
    const outputCostPer1k = 0.002;

    const costPer1k = type === 'input' ? inputCostPer1k : outputCostPer1k;
    const cost = (tokens / 1000) * costPer1k;

    return `$${cost.toFixed(4)}`;
}

function calculateEfficiency(data) {
    const totalTokens = (data.total_input_tokens || 0) + (data.total_output_tokens || 0);
    const calls = data.llm_calls || 1;

    if (calls === 0) return 'N/A';

    const tokensPerCall = totalTokens / calls;

    if (tokensPerCall < 500) return 'High';
    if (tokensPerCall < 1500) return 'Medium';
    return 'Low';
}

// Create performance distribution chart
function createPerformanceDistributionChart(data) {
    const chartCanvas = document.getElementById('performance-distribution-chart');
    if (!chartCanvas) return;

    const ctx = chartCanvas.getContext('2d');

    // Create sample data for demonstration (in real implementation, this would come from historical data)
    const performanceData = {
        labels: ['Min', 'Q1', 'Median', 'Q3', 'Max'],
        datasets: [{
            label: 'Response Time Distribution (ms)',
            data: [
                data.min_duration || 0,
                data.min_duration ? data.min_duration * 1.2 : 0,
                data.median_duration || 0,
                data.median_duration ? data.median_duration * 1.3 : 0,
                data.max_duration || 0
            ],
            backgroundColor: [
                'rgba(75, 192, 192, 0.6)',
                'rgba(54, 162, 235, 0.6)',
                'rgba(255, 206, 86, 0.6)',
                'rgba(255, 159, 64, 0.6)',
                'rgba(255, 99, 132, 0.6)'
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(255, 99, 132, 1)'
            ],
            borderWidth: 1
        }]
    };

    new Chart(ctx, {
        type: 'bar',
        data: performanceData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Performance Distribution'
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Response Time (ms)'
                    }
                }
            }
        }
    });
}

// Helper function to detect workflow-aware benchmarks
function isWorkflowBenchmark(data) {
    // Check if this is a workflow-aware benchmark
    return (
        data.scenario_name && data.scenario_name.includes('Workflow_') ||
        data.scenario_metadata && data.scenario_metadata.scenario_type === 'workflow_benchmark' ||
        data.raw_results && data.raw_results.scenario_metadata && data.raw_results.scenario_metadata.scenario_type === 'workflow_benchmark' ||
        data.parameters && data.parameters.scenario_context && data.parameters.scenario_context.workflow_context
    );
}

// Function to render workflow context details
function renderWorkflowContextDetails(container, data) {
    if (!container) return;

    // Extract workflow context from various possible locations
    let workflowContext = null;
    let evaluationContext = null;
    let agentCoordination = null;

    // Try to find workflow context in different data structures
    if (data.parameters && data.parameters.scenario_context) {
        workflowContext = data.parameters.scenario_context.workflow_context;
        evaluationContext = data.parameters.scenario_context.evaluation_context_id;
        agentCoordination = data.parameters.scenario_context.agent_coordination;
    } else if (data.raw_results && data.raw_results.scenario_metadata) {
        const metadata = data.raw_results.scenario_metadata;
        workflowContext = {
            workflow_type: metadata.workflow_type || 'Unknown',
            workflow_stage: metadata.workflow_stage || 'Unknown',
            current_agent: data.agent_name || 'Unknown'
        };
        agentCoordination = {
            previous_agent_outputs: metadata.previous_agent_outputs || [],
            expected_next_agents: metadata.expected_next_agents || []
        };
    }

    // Extract evaluation context information
    let contextOverrides = null;
    if (data.scenario_metadata && data.scenario_metadata.evaluation_context) {
        contextOverrides = data.scenario_metadata.evaluation_context;
    }

    const html = `
        <div class="workflow-context-dashboard">
            ${workflowContext ? `
            <div class="workflow-info-grid">
                <div class="workflow-info-card">
                    <div class="info-header">
                        <div class="info-icon">🔄</div>
                        <div class="info-content">
                            <h4>Workflow Type</h4>
                            <p class="workflow-type-badge">${workflowContext.workflow_type}</p>
                        </div>
                    </div>
                </div>

                <div class="workflow-info-card">
                    <div class="info-header">
                        <div class="info-icon">📍</div>
                        <div class="info-content">
                            <h4>Workflow Stage</h4>
                            <p class="workflow-stage-badge">${workflowContext.workflow_stage}</p>
                        </div>
                    </div>
                </div>

                <div class="workflow-info-card">
                    <div class="info-header">
                        <div class="info-icon">🤖</div>
                        <div class="info-content">
                            <h4>Current Agent</h4>
                            <p class="agent-role-badge">${workflowContext.current_agent}</p>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}

            ${agentCoordination ? `
            <div class="agent-coordination-section">
                <h4>🔗 Agent Coordination Context</h4>
                <div class="coordination-grid">
                    <div class="coordination-card">
                        <h5>📥 Previous Agent Outputs</h5>
                        ${agentCoordination.previous_agent_outputs && agentCoordination.previous_agent_outputs.length > 0 ? `
                            <div class="output-list">
                                ${agentCoordination.previous_agent_outputs.map(output => `
                                    <div class="output-item">
                                        <span class="output-badge">📤</span>
                                        <span class="output-text">${output}</span>
                                    </div>
                                `).join('')}
                            </div>
                        ` : '<p class="no-data">No previous agent outputs</p>'}
                    </div>

                    <div class="coordination-card">
                        <h5>📤 Expected Next Agents</h5>
                        ${agentCoordination.expected_next_agents && agentCoordination.expected_next_agents.length > 0 ? `
                            <div class="agent-list">
                                ${agentCoordination.expected_next_agents.map(agent => `
                                    <span class="next-agent-badge">${agent}</span>
                                `).join('')}
                            </div>
                        ` : '<p class="no-data">No expected next agents (workflow end)</p>'}
                    </div>
                </div>
            </div>
            ` : ''}

            <div class="workflow-benefits-section">
                <h4>✨ Workflow-Aware Benefits</h4>
                <div class="benefits-grid">
                    <div class="benefit-item">
                        <span class="benefit-icon">🎯</span>
                        <span class="benefit-text">Context-specific evaluation criteria</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🔄</span>
                        <span class="benefit-text">Realistic workflow state simulation</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">🤝</span>
                        <span class="benefit-text">Agent coordination testing</span>
                    </div>
                    <div class="benefit-item">
                        <span class="benefit-icon">📊</span>
                        <span class="benefit-text">Enhanced debugging insights</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

</script>

<style>
/* ResourceAgent Display Styles */
.resource-agent-display {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.resource-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: transform 0.2s, box-shadow 0.2s;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.summary-card .card-icon {
    font-size: 28px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e3f2fd;
    border-radius: 50%;
}

.summary-card .card-content {
    flex: 1;
}

.summary-card .card-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 4px;
}

.summary-card .card-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.summary-card .card-detail {
    font-size: 0.9em;
    color: #6c757d;
}

.resource-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.resource-section h4 {
    margin: 0 0 20px 0;
    color: #495057;
    font-size: 1.2em;
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 10px;
}

.inventory-grid .category-section {
    margin-bottom: 25px;
}

.inventory-grid .category-section h5 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.1em;
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.inventory-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: transform 0.2s;
}

.inventory-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.item-name {
    font-weight: 600;
    color: #495057;
}

.item-code {
    font-family: monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
}

.item-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.9em;
    color: #6c757d;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 15px;
}

.skill-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.skill-name {
    font-weight: 600;
    color: #495057;
}

.skill-level {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
}

.skill-level.expert { background: #d4edda; color: #155724; }
.skill-level.advanced { background: #d1ecf1; color: #0c5460; }
.skill-level.intermediate { background: #fff3cd; color: #856404; }
.skill-level.beginner { background: #f8d7da; color: #721c24; }
.skill-level.novice { background: #e2e3e5; color: #383d41; }

.skill-metrics {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.85em;
}

.metric-label {
    min-width: 80px;
    color: #6c757d;
}

.metric-bar {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: #007bff;
    transition: width 0.3s ease;
}

.metric-fill.enjoyment {
    background: #28a745;
}

.metric-value {
    min-width: 35px;
    text-align: right;
    font-weight: 600;
}

.limitations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.limitation-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #6c757d;
}

.limitation-item.severity-critical { border-left-color: #dc3545; }
.limitation-item.severity-high { border-left-color: #fd7e14; }
.limitation-item.severity-medium { border-left-color: #ffc107; }
.limitation-item.severity-low { border-left-color: #28a745; }

.limitation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.limitation-type {
    font-weight: 600;
    color: #495057;
}

.limitation-severity {
    font-weight: 600;
    color: #dc3545;
}

.limitation-status.active {
    color: #dc3545;
}

.limitation-status.inactive {
    color: #28a745;
}

.limitation-description {
    color: #6c757d;
    margin-bottom: 10px;
}

.severity-bar {
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.severity-fill {
    height: 100%;
    background: #dc3545;
    transition: width 0.3s ease;
}

.environment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.env-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.env-card h5 {
    margin: 0 0 15px 0;
    color: #495057;
}

.env-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.env-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.env-label {
    color: #6c757d;
    font-size: 0.9em;
}

.env-value {
    font-weight: 600;
    color: #495057;
}

.activity-support {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.support-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 0;
}

.support-item.supported {
    color: #28a745;
}

.support-item.not-supported {
    color: #6c757d;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.analysis-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.analysis-card h5 {
    margin: 0 0 15px 0;
    color: #495057;
}

.analysis-card ul {
    margin: 0;
    padding-left: 20px;
}

.analysis-card li {
    margin-bottom: 5px;
    color: #6c757d;
}

.activity-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.activity-tag {
    background: #e3f2fd;
    color: #1565c0;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

/* Tool Analysis Styles */
.agent-tool-analysis {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.agent-tool-analysis h5 {
    margin: 0 0 15px 0;
    color: #495057;
}

.processing-metrics {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 15px;
}

.processing-metrics .metric {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.processing-metrics .metric-label {
    min-width: 150px;
    font-weight: 600;
    color: #495057;
}

.processing-metrics .metric-value {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
    min-width: 50px;
}

.processing-metrics .metric-detail {
    flex: 1;
    font-size: 0.9em;
    color: #6c757d;
    font-style: italic;
}

.processing-note {
    background: #e3f2fd;
    border-radius: 6px;
    padding: 12px;
    border-left: 4px solid #2196f3;
}

.processing-note p {
    margin: 0;
    color: #1565c0;
    font-size: 0.9em;
}

.standard-tool-metrics .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.standard-tool-metrics .metric {
    background: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.standard-tool-metrics .metric-value.real {
    color: #28a745;
}

.standard-tool-metrics .metric-value.mocked {
    color: #ffc107;
}

.no-tool-usage {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: #6c757d;
    border: 1px solid #e9ecef;
}

.no-tool-usage .debug-info {
    font-size: 0.8em;
    margin-top: 10px;
    opacity: 0.7;
}
</style>
