<!-- Quick Test Configuration Modal -->
<div id="quick-test-config-modal" class="modal quick-test-modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <div class="quick-test-header">
                <span class="close" onclick="hideQuickTestModal()">&times;</span>
                <h2>⚡ Configure Quick Test</h2>
                <p class="modal-description">
                    Set your preferred benchmark parameters. These will be saved and used for quick testing.
                </p>
            </div>
        </div>

        <div class="modal-body">
            <!-- Enhanced Tab Navigation -->
            <div class="config-tabs">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="basic">
                        <i class="fas fa-cog"></i> Basic Config
                    </button>
                    <button class="tab-button" data-tab="advanced">
                        <i class="fas fa-sliders-h"></i> Advanced
                    </button>
                    <button class="tab-button" data-tab="preview">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                </div>
            </div>

            <form id="quick-test-config-form" class="quick-test-form">
                <!-- Basic Configuration Tab -->
                <div id="basic-tab" class="tab-content active">
                    <div class="form-section collapsible">
                        <div class="section-header" data-toggle="collapse">
                            <h3>📋 Test Configuration</h3>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="section-content">
                            <div class="form-group">
                                <label for="quick-scenario-select">Default Scenario:</label>
                                <select id="quick-scenario-select" required>
                                    <option value="">-- Select Scenario --</option>
                                    <!-- Will be populated by JavaScript -->
                                </select>
                                <div class="field-status" id="scenario-status"></div>
                            </div>

                            <div class="form-group">
                                <label for="quick-runs-input">Number of Runs:</label>
                                <input type="number" id="quick-runs-input" value="1" min="1" max="5">
                                <small class="form-help">Higher values provide better statistical significance</small>
                            </div>

                            <div class="form-group checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="quick-semantic-eval" checked>
                                    <span class="checkmark"></span>
                                    Include Semantic Evaluation
                                </label>
                                <small class="form-help">Enables quality assessment of responses</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-section collapsible">
                        <div class="section-header" data-toggle="collapse">
                            <h3>🔧 Execution Settings</h3>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="section-content">
                            <div class="form-group">
                                <label for="quick-execution-mode-select">Execution Mode:</label>
                                <select id="quick-execution-mode-select" class="execution-mode-select">
                                    <option value="mock">🎭 Mock Mode (Safe - No costs)</option>
                                    <option value="real-tools">🛠️ Real Tools Only</option>
                                    <option value="real-llm">🧠 Real LLM Only</option>
                                    <option value="real-db">🗄️ Real Database Only</option>
                                    <option value="partial-real">⚡ Partial Real (Tools + DB)</option>
                                    <option value="full-real">🚀 Full Real Mode (All components)</option>
                                </select>
                                <small class="form-help warning">⚠️ Real modes use actual resources and may incur costs</small>
                                <div class="execution-mode-info" id="execution-mode-info" style="display: none;">
                                    <!-- Dynamic execution mode information -->
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="quick-user-profile-select">Test User Profile:</label>
                                <select id="quick-user-profile-select" class="user-profile-select">
                                    <option value="">-- Select Test Profile --</option>
                                    <!-- Will be populated by JavaScript -->
                                </select>
                                <small class="form-help">Select a fake user profile for testing (only non-real profiles shown)</small>
                                <div class="field-status" id="profile-status"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section collapsible">
                        <div class="section-header" data-toggle="collapse">
                            <h3>📊 Evaluation Template</h3>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div class="section-content">
                            <div class="form-group">
                                <label for="quick-template-select">Evaluation Template:</label>
                                <select id="quick-template-select" class="template-select">
                                    <option value="">-- Select Template --</option>
                                </select>
                                <small class="form-help">Choose a pre-configured evaluation template with criteria and dimensions</small>
                                <div class="field-status" id="template-status"></div>
                            </div>

                            <div class="form-group">
                                <div id="quick-template-preview" class="template-preview">
                                    <!-- Template preview will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Configuration Tab -->
                <div id="advanced-tab" class="tab-content">
                    <div class="form-section">
                        <h3>🚀 Advanced Options</h3>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="quick-detailed-logging">
                                <span class="checkmark"></span>
                                Enable Detailed Logging
                            </label>
                            <small class="form-help">Capture detailed execution logs for debugging</small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="quick-save-artifacts">
                                <span class="checkmark"></span>
                                Save Test Artifacts
                            </label>
                            <small class="form-help">Save intermediate results and generated content</small>
                        </div>

                        <div class="form-group">
                            <label for="quick-timeout-input">Test Timeout (seconds):</label>
                            <input type="number" id="quick-timeout-input" value="300" min="60" max="1800">
                            <small class="form-help">Maximum time to wait for test completion</small>
                        </div>

                        <div class="form-group">
                            <label for="quick-retry-attempts">Retry Attempts:</label>
                            <input type="number" id="quick-retry-attempts" value="1" min="0" max="3">
                            <small class="form-help">Number of retry attempts on failure</small>
                        </div>
                    </div>
                </div>

                <!-- Preview Tab -->
                <div id="preview-tab" class="tab-content">
                    <div class="config-preview">
                        <h3>📋 Configuration Summary</h3>
                        <div id="config-summary" class="summary-content">
                            <p class="text-muted">Configure your settings in the Basic Config tab to see a preview here.</p>
                        </div>

                        <div class="preview-actions">
                            <button type="button" id="validate-config-btn" class="btn btn-info">
                                <i class="fas fa-check-circle"></i> Validate Configuration
                            </button>
                            <button type="button" id="test-connection-btn" class="btn btn-warning">
                                <i class="fas fa-wifi"></i> Test Connection
                            </button>
                            <button type="button" id="export-config-btn" class="btn btn-secondary">
                                <i class="fas fa-download"></i> Export Config
                            </button>
                            <button type="button" id="import-config-btn" class="btn btn-secondary">
                                <i class="fas fa-upload"></i> Import Config
                            </button>
                        </div>

                        <div class="config-health-check" id="config-health-check">
                            <h6><i class="fas fa-heartbeat"></i> Configuration Health</h6>
                            <div class="health-indicators" id="health-indicators">
                                <div class="health-item" data-check="scenario">
                                    <span class="health-icon">⚪</span>
                                    <span class="health-label">Scenario Selection</span>
                                    <span class="health-status">Not checked</span>
                                </div>
                                <div class="health-item" data-check="template">
                                    <span class="health-icon">⚪</span>
                                    <span class="health-label">Template Configuration</span>
                                    <span class="health-status">Not checked</span>
                                </div>
                                <div class="health-item" data-check="profile">
                                    <span class="health-icon">⚪</span>
                                    <span class="health-label">User Profile</span>
                                    <span class="health-status">Not checked</span>
                                </div>
                                <div class="health-item" data-check="execution">
                                    <span class="health-icon">⚪</span>
                                    <span class="health-label">Execution Mode</span>
                                    <span class="health-status">Not checked</span>
                                </div>
                            </div>
                        </div>

                        <div id="validation-results" class="validation-results" style="display: none;">
                            <!-- Validation results will be shown here -->
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="form-actions">
            <button type="button" id="cancel-config-btn" class="btn btn-secondary">
                <span class="btn-icon">❌</span>
                Cancel
            </button>
            <button type="button" id="reset-config-btn" class="btn btn-outline-secondary">
                <span class="btn-icon">🔄</span>
                Reset
            </button>
            <button type="submit" form="quick-test-config-form" class="btn btn-primary">
                <span class="btn-icon">💾</span>
                Save Configuration
            </button>
        </div>
    </div>
</div>

<!-- Hidden file input for configuration import -->
<input type="file" id="config-file-input" accept=".json" style="display: none;">

<style>
/* Quick Test Modal Specific Styles - Enhanced with proper scrolling fix */
.quick-test-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    z-index: 1000;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
    /* Ensure modal can be scrolled on small screens */
    min-height: 100vh;
}

.quick-test-modal .modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 950px;
    width: 100%;
    min-height: 400px;
    /* Critical fix: Use dynamic height calculation */
    max-height: calc(100vh - 20px);
    display: flex;
    flex-direction: column;
    margin: 10px auto;
    position: relative;
    /* Enable resize functionality */
    resize: both;
    overflow: hidden;
}

.quick-test-modal .modal-header {
    flex-shrink: 0;
    padding: 0;
    border-bottom: none;
    /* Add drag handle for modal repositioning */
    cursor: move;
}

.quick-test-modal .modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    /* Critical fix: Proper height calculation to ensure all content is accessible */
    max-height: calc(100vh - 180px);
    scrollbar-width: thin;
    scrollbar-color: #ccc #f1f1f1;
    /* Ensure bottom content is always accessible */
    padding-bottom: 40px;
    /* Add scroll behavior */
    scroll-behavior: smooth;
}

.quick-test-modal .modal-body::-webkit-scrollbar {
    width: 8px;
}

.quick-test-modal .modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.quick-test-modal .modal-body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.quick-test-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: #999;
}

.quick-test-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
    text-align: center;
    position: relative;
}

.quick-test-header .close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
    background: none;
    border: none;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.quick-test-header .close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
}

.quick-test-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.modal-description {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.quick-test-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
    padding-bottom: 20px;
}

.form-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
}

.form-section h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1.2em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-help {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.form-help.warning {
    color: #856404;
    font-weight: 600;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #495057;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.2);
}

.template-preview {
    display: none;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
}

.template-preview.visible {
    display: block;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: white;
    border-radius: 0 0 12px 12px;
    flex-shrink: 0;
    /* Ensure actions are always visible and accessible */
    position: sticky;
    bottom: 0;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    /* Add margin to prevent overlap with scrolled content */
    margin-top: auto;
}

/* Enhanced Tab System */
.config-tabs {
    margin-bottom: 20px;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    gap: 5px;
}

.tab-button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    color: #6c757d;
    border-radius: 8px 8px 0 0;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.tab-button:hover {
    background: #f8f9fa;
    color: #495057;
}

.tab-button.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Tab content visibility - using maximum specificity */
.quick-test-modal .modal-content .modal-body .tab-content {
    display: none !important;
    animation: fadeIn 0.3s ease-in-out;
}

.quick-test-modal .modal-content .modal-body .tab-content.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Additional fallback rules with even higher specificity */
#quick-test-config-modal .tab-content {
    display: none !important;
}

#quick-test-config-modal .tab-content.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Collapsible Sections */
.form-section.collapsible .section-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    margin: -20px -20px 15px -20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px 8px 0 0;
    transition: all 0.2s;
    user-select: none; /* Prevent text selection */
}

.form-section.collapsible .section-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.form-section.collapsible .section-header h3 {
    margin: 0;
    border: none;
    padding: 0;
    pointer-events: none; /* Prevent h3 from interfering with click */
}

.toggle-icon {
    transition: transform 0.3s ease;
    color: #6c757d;
    pointer-events: none; /* Prevent icon from interfering with click */
}

.form-section.collapsible.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

/* CSS Specificity Fix: Use highly specific selectors to override any conflicting rules */
/* IMPORTANT: These rules must account for the tab system where sections are inside tab-content */

/* Default state: sections are expanded and visible (only when parent tab is active) */
.quick-test-modal .tab-content.active .form-section.collapsible .section-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transition: none !important; /* Remove transitions that might cause issues */
    max-height: none !important;
    overflow: visible !important;
}

/* Collapsed state: sections are hidden */
.quick-test-modal .tab-content.active .form-section.collapsible.collapsed .section-content {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
}

/* Ensure sections start expanded by default */
.quick-test-modal .form-section.collapsible {
    position: relative; /* Default state is expanded */
}

/* Additional specificity overrides for any potential conflicts */
.quick-test-modal .modal-body .tab-content.active .form-section.collapsible .section-content {
    display: block !important;
}

.quick-test-modal .modal-body .tab-content.active .form-section.collapsible.collapsed .section-content {
    display: none !important;
}

/* Fallback rules for when tab system is not used */
.quick-test-modal .form-section.collapsible .section-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.quick-test-modal .form-section.collapsible.collapsed .section-content {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Field Status Indicators */
.field-status {
    margin-top: 5px;
    font-size: 12px;
    display: none;
}

.field-status.success {
    color: #28a745;
    display: block;
}

.field-status.error {
    color: #dc3545;
    display: block;
}

.field-status.warning {
    color: #ffc107;
    display: block;
}

.field-status::before {
    margin-right: 5px;
}

.field-status.success::before {
    content: '✓';
}

.field-status.error::before {
    content: '✗';
}

.field-status.warning::before {
    content: '⚠';
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-icon {
    font-size: 16px;
}

/* Enhanced Button Styles */
.btn-outline-secondary {
    background: transparent;
    border: 2px solid #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

/* Preview Tab Styles */
.config-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.summary-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    min-height: 100px;
}

.preview-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.validation-results {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 15px;
}

.validation-results.success {
    border-color: #28a745;
    background: #d4edda;
}

.validation-results.error {
    border-color: #dc3545;
    background: #f8d7da;
}

.validation-results.warning {
    border-color: #ffc107;
    background: #fff3cd;
}

/* Execution Mode Info */
.execution-mode-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    font-size: 12px;
}

.execution-mode-info.warning {
    background: #fff3cd;
    border-color: #ffeaa7;
}

.execution-mode-info.danger {
    background: #f8d7da;
    border-color: #f5c6cb;
}

/* Enhanced Template Preview */
.template-preview {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
    max-height: 300px;
    overflow-y: auto;
}

.template-preview.visible {
    display: block;
    animation: slideDown 0.3s ease-out;
}

.template-preview h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-weight: 600;
}

.template-preview .criteria-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.template-preview .criteria-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 5px;
    font-size: 13px;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Configuration Summary Styles */
.config-summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-section {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.summary-section h6 {
    margin: 0 0 10px 0;
    color: #495057;
    font-weight: 600;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.summary-item .label {
    font-weight: 600;
    color: #6c757d;
}

.summary-item .value {
    color: #495057;
}

.summary-item .value.valid {
    color: #28a745;
    font-weight: 600;
}

.summary-item .value.invalid {
    color: #dc3545;
    font-style: italic;
}

.config-status {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    font-size: 14px;
}

.config-status.valid {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.config-status.invalid {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Validation Results Styles */
.validation-items {
    margin-bottom: 15px;
}

.validation-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-size: 13px;
}

.validation-item.valid {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.validation-item.invalid {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

.validation-icon {
    font-size: 14px;
}

.validation-label {
    font-weight: 600;
    flex: 1;
}

.validation-message {
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.validation-summary {
    padding: 12px;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
}

.validation-summary.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.validation-summary.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Connection Test Styles */
.connection-tests {
    margin-bottom: 15px;
}

.test-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-size: 13px;
}

.test-item.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.test-item.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
}

.test-item.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

.test-icon {
    font-size: 14px;
}

.test-label {
    font-weight: 600;
    flex: 1;
}

.test-status {
    font-size: 12px;
    color: #6c757d;
}

.test-summary {
    padding: 12px;
    border-radius: 6px;
    text-align: center;
    font-weight: 600;
}

.test-summary.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* Loading Spinner */
.loading-spinner {
    text-align: center;
    padding: 20px;
    color: #6c757d;
    font-style: italic;
}

.loading-spinner::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    vertical-align: middle;
}

/* Enhanced Responsive design with better mobile support */
@media (max-width: 768px) {
    .quick-test-modal {
        padding: 5px;
        align-items: flex-start;
    }

    .quick-test-modal .modal-content {
        width: 100%;
        max-height: calc(100vh - 10px);
        border-radius: 8px;
        margin: 5px auto;
    }

    .quick-test-modal .modal-body {
        max-height: calc(100vh - 160px);
        padding: 15px;
        padding-bottom: 50px;
    }

    .quick-test-header {
        padding: 15px;
        border-radius: 8px 8px 0 0;
    }

    .quick-test-header h2 {
        font-size: 1.5em;
    }

    .form-section {
        padding: 15px;
    }

    .form-actions {
        flex-direction: column;
        padding: 15px;
        border-radius: 0 0 8px 8px;
        position: sticky;
        bottom: 0;
    }

    .btn {
        justify-content: center;
        width: 100%;
    }

    /* Improve tab navigation on mobile */
    .tab-buttons {
        flex-wrap: wrap;
        gap: 2px;
    }

    .tab-button {
        flex: 1;
        min-width: 80px;
        padding: 8px 12px;
        font-size: 12px;
    }
}

@media (max-height: 600px) {
    .quick-test-modal .modal-content {
        max-height: 95vh;
    }

    .quick-test-modal .modal-body {
        max-height: calc(95vh - 120px);
    }

    .quick-test-header {
        padding: 15px;
    }

    .quick-test-header h2 {
        font-size: 1.4em;
        margin-bottom: 5px;
    }

    .modal-description {
        font-size: 1em;
    }

    .form-section {
        padding: 15px;
        gap: 15px;
    }

    .quick-test-form {
        gap: 15px;
    }
}

/* Enhanced Features Styles */
.auto-save-indicator {
    position: absolute;
    top: 10px;
    right: 50px;
    background: #28a745;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1001;
}

.keyboard-hints {
    position: absolute;
    bottom: 5px;
    left: 20px;
    font-size: 11px;
    opacity: 0.8;
}

.keyboard-hints kbd {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    padding: 1px 4px;
    font-size: 10px;
    font-family: monospace;
}

.help-modal {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.help-modal h4 {
    margin-top: 0;
    color: #495057;
}

.help-modal .help-section {
    margin-bottom: 20px;
}

.help-modal h5 {
    color: #6c757d;
    margin-bottom: 10px;
}

.help-modal ul {
    margin: 0;
    padding-left: 20px;
}

.help-modal li {
    margin-bottom: 5px;
}

.help-modal kbd {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 12px;
    font-family: monospace;
}

/* Drag functionality styles */
.quick-test-modal .modal-content.dragging {
    transition: none;
    user-select: none;
}

.quick-test-header.dragging {
    cursor: grabbing !important;
}

/* Enhanced scrollbar styles */
.quick-test-modal .modal-body::-webkit-scrollbar {
    width: 12px;
}

.quick-test-modal .modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

.quick-test-modal .modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
}

.quick-test-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus indicators for accessibility */
.quick-test-modal input:focus,
.quick-test-modal select:focus,
.quick-test-modal button:focus,
.quick-test-modal textarea:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Enhanced form validation styles */
.field-status.loading {
    color: #6c757d;
    display: block;
}

.field-status.loading::before {
    content: '⏳';
}

/* Improved collapsible section animations */
.form-section.collapsible .section-content {
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

.form-section.collapsible.collapsed .section-content {
    max-height: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Enhanced button hover effects */
.btn:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.btn:active {
    transform: translateY(1px);
}

/* Configuration Health Check Styles */
.config-health-check {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-top: 20px;
}

.config-health-check h6 {
    margin: 0 0 15px 0;
    color: #495057;
    font-weight: 600;
}

.health-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.health-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.2s;
}

.health-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.health-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.health-label {
    flex: 1;
    font-weight: 600;
    color: #495057;
}

.health-status {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
}

.health-item.healthy .health-icon {
    color: #28a745;
}

.health-item.healthy .health-status {
    color: #28a745;
}

.health-item.warning .health-icon {
    color: #ffc107;
}

.health-item.warning .health-status {
    color: #856404;
}

.health-item.error .health-icon {
    color: #dc3545;
}

.health-item.error .health-status {
    color: #721c24;
}

/* Import/Export functionality */
.config-import-export {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.import-export-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Hidden file input for import */
#config-file-input {
    display: none;
}

/* Notification animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Enhanced modal resize handle */
.quick-test-modal .modal-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: linear-gradient(-45deg, transparent 0%, transparent 40%, #ccc 40%, #ccc 60%, transparent 60%);
    cursor: se-resize;
    z-index: 1001;
}

/* Improved loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: 12px;
}

.loading-spinner-enhanced {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced focus management */
.modal-focus-trap {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 1px;
    height: 1px;
    opacity: 0;
    pointer-events: none;
}

/* Improved error states */
.form-group.error input,
.form-group.error select {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.form-group.success input,
.form-group.success select {
    border-color: #28a745;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
}
</style>
