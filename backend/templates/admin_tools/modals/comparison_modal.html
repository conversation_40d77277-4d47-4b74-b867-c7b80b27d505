<!-- Benchmark Comparison Modal -->
<div id="comparison-modal" class="modal">
    <div class="modal-content comparison-modal">
        <span class="close">&times;</span>
        <div class="comparison-header">
            <h2>📊 Compare Benchmark Execution Sessions</h2>
            <p class="modal-description">
                Side-by-side comparison of selected benchmark runs to identify performance differences and optimization opportunities.
            </p>
        </div>

        <div id="comparison-modal-body" class="comparison-modal-body">
            <!-- Comparison columns will be loaded here by JS -->
            <div class="comparison-loading">
                <div class="loader"></div>
                <p>Loading comparison data...</p>
            </div>
        </div>

        <div class="comparison-actions">
            <button type="button" class="btn btn-secondary" onclick="exportComparison()">
                <span class="btn-icon">📥</span>
                Export Comparison
            </button>
            <button type="button" class="btn btn-primary" onclick="generateComparisonReport()">
                <span class="btn-icon">📋</span>
                Generate Report
            </button>
        </div>
    </div>
</div>

<style>
/* Comparison Modal Specific Styles */
.comparison-modal .modal-content {
    max-width: 1400px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.comparison-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    text-align: center;
}

.comparison-header h2 {
    margin: 0 0 10px 0;
    font-size: 1.8em;
}

.comparison-modal-body {
    display: flex;
    gap: 20px;
    overflow-x: auto;
    min-height: 400px;
    padding: 10px 0;
}

.comparison-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 40px;
    color: #6c757d;
}

.comparison-loading .loader {
    margin-bottom: 15px;
}

.comparison-column {
    flex: 1;
    min-width: 400px;
    max-width: 500px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #fdfdfd;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comparison-column-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.comparison-column-header h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 1.1em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.run-id-badge {
    background: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

.comparison-meta {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    font-size: 12px;
    color: #6c757d;
}

.meta-item {
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.comparison-column-content {
    padding: 15px;
    max-height: 600px;
    overflow-y: auto;
}

.comparison-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.comparison-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.comparison-section h5 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.comparison-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 13px;
    border-bottom: 1px solid #f8f9fa;
}

.comparison-metric:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
}

.metric-value {
    color: #6c757d;
    text-align: right;
    font-family: monospace;
}

.metric-value.better {
    color: #28a745;
    font-weight: 600;
}

.metric-value.worse {
    color: #dc3545;
    font-weight: 600;
}

.metric-value.neutral {
    color: #6c757d;
}

.comparison-highlight {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 8px;
    margin: 5px 0;
}

.comparison-highlight.positive {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.comparison-highlight.negative {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.comparison-json {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.comparison-json pre {
    margin: 0;
    font-size: 11px;
    line-height: 1.4;
    color: #495057;
    background: transparent;
    border: none;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.comparison-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    margin-top: 20px;
}

.comparison-summary {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.comparison-summary h4 {
    margin: 0 0 10px 0;
    color: #0056b3;
    font-size: 16px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.summary-stat {
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #b3d9ff;
    text-align: center;
}

.summary-stat .label {
    font-size: 11px;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
}

.summary-stat .value {
    font-size: 14px;
    font-weight: bold;
    color: #0056b3;
}

/* Responsive design */
@media (max-width: 1200px) {
    .comparison-modal-body {
        flex-direction: column;
    }
    
    .comparison-column {
        min-width: auto;
        max-width: none;
    }
}

@media (max-width: 768px) {
    .comparison-modal .modal-content {
        width: 98%;
        margin: 1% auto;
    }
    
    .comparison-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}

/* Scrollbar styling for comparison content */
.comparison-column-content::-webkit-scrollbar {
    width: 6px;
}

.comparison-column-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.comparison-column-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.comparison-column-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
// Comparison Modal Utility Functions
function exportComparison() {
    // Implementation for exporting comparison data
    console.log('Exporting comparison data...');
    // This would typically generate a CSV or JSON export
}

function generateComparisonReport() {
    // Implementation for generating a detailed comparison report
    console.log('Generating comparison report...');
    // This would typically create a formatted report with insights
}

// Function to render comparison data
function renderComparisonData(runs) {
    const modalBody = document.getElementById('comparison-modal-body');
    if (!modalBody || !runs || runs.length === 0) {
        modalBody.innerHTML = '<p>No data available for comparison.</p>';
        return;
    }

    // Clear loading state
    modalBody.innerHTML = '';

    // Add comparison summary if multiple runs
    if (runs.length > 1) {
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'comparison-summary';
        summaryDiv.innerHTML = generateComparisonSummary(runs);
        modalBody.appendChild(summaryDiv);
    }

    // Create comparison columns
    const columnsContainer = document.createElement('div');
    columnsContainer.className = 'comparison-columns-container';
    columnsContainer.style.display = 'flex';
    columnsContainer.style.gap = '20px';
    columnsContainer.style.overflowX = 'auto';

    runs.forEach((run, index) => {
        const column = document.createElement('div');
        column.className = 'comparison-column';
        column.innerHTML = generateComparisonColumn(run, index);
        columnsContainer.appendChild(column);
    });

    modalBody.appendChild(columnsContainer);
}

function generateComparisonSummary(runs) {
    const bestDuration = Math.min(...runs.map(r => r.mean_duration || Infinity));
    const bestSuccess = Math.max(...runs.map(r => r.success_rate || 0));
    const bestSemantic = Math.max(...runs.map(r => r.semantic_score || 0));

    return `
        <h4>📊 Comparison Summary</h4>
        <p>Comparing ${runs.length} benchmark execution sessions</p>
        <div class="summary-stats">
            <div class="summary-stat">
                <div class="label">Best Duration</div>
                <div class="value">${bestDuration.toFixed(2)}ms</div>
            </div>
            <div class="summary-stat">
                <div class="label">Best Success Rate</div>
                <div class="value">${(bestSuccess * 100).toFixed(1)}%</div>
            </div>
            <div class="summary-stat">
                <div class="label">Best Semantic Score</div>
                <div class="value">${bestSemantic.toFixed(2)}</div>
            </div>
        </div>
    `;
}

function generateComparisonColumn(run, index) {
    return `
        <div class="comparison-column-header">
            <h4>
                🏃 Run ${index + 1}
                <span class="run-id-badge">${run.id}</span>
            </h4>
            <div class="comparison-meta">
                <span class="meta-item">📅 ${new Date(run.execution_date).toLocaleDateString()}</span>
                <span class="meta-item">🎯 ${run.scenario || 'N/A'}</span>
                <span class="meta-item">🤖 ${run.agent_role || 'N/A'}</span>
            </div>
        </div>
        <div class="comparison-column-content">
            ${generateComparisonSections(run)}
        </div>
    `;
}

function generateComparisonSections(run) {
    return `
        <div class="comparison-section">
            <h5>⚡ Performance Metrics</h5>
            <div class="comparison-metric">
                <span class="metric-label">Mean Duration:</span>
                <span class="metric-value">${run.mean_duration ? run.mean_duration.toFixed(2) + 'ms' : 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Success Rate:</span>
                <span class="metric-value">${run.success_rate ? (run.success_rate * 100).toFixed(1) + '%' : 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Semantic Score:</span>
                <span class="metric-value">${run.semantic_score ? run.semantic_score.toFixed(2) : 'N/A'}</span>
            </div>
        </div>
        
        <div class="comparison-section">
            <h5>🔧 Configuration</h5>
            <div class="comparison-metric">
                <span class="metric-label">LLM Model:</span>
                <span class="metric-value">${run.llm_model || 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Temperature:</span>
                <span class="metric-value">${run.llm_temperature ? run.llm_temperature.toFixed(1) : 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Runs Count:</span>
                <span class="metric-value">${run.runs_count || 'N/A'}</span>
            </div>
        </div>
        
        <div class="comparison-section">
            <h5>💰 Resource Usage</h5>
            <div class="comparison-metric">
                <span class="metric-label">Input Tokens:</span>
                <span class="metric-value">${run.total_input_tokens || 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Output Tokens:</span>
                <span class="metric-value">${run.total_output_tokens || 'N/A'}</span>
            </div>
            <div class="comparison-metric">
                <span class="metric-label">Estimated Cost:</span>
                <span class="metric-value">${run.estimated_cost ? '$' + run.estimated_cost.toFixed(4) : 'N/A'}</span>
            </div>
        </div>
    `;
}
</script>
