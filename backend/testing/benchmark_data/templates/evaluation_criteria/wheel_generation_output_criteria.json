{"template_name": "wheel_generation_output_criteria", "description": "Evaluation criteria for wheel generation workflow output quality", "criteria": {"Output_Relevance": ["matches_user_needs", "contextually_appropriate", "personalized"], "Content_Quality": ["accurate", "complete", "actionable"], "System_Performance": ["efficient_coordination", "proper_state_management", "error_handling"]}, "dimension_weights": {"Output_Relevance": 0.5, "Content_Quality": 0.3, "System_Performance": 0.2}, "scoring_thresholds": {"excellent": 0.9, "good": 0.7, "acceptable": 0.5, "poor": 0.3}, "evaluator_models": ["mistral-small-latest"], "note": "Tone analysis is NOT included in workflow evaluation"}