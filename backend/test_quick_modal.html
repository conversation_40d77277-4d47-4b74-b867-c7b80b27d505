<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test Modal - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .debug-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }
        .debug-panel h3 {
            margin-top: 0;
        }
        .debug-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Quick Test Modal - Test Page</h1>
        <p>This page is for testing the enhanced quick test modal functionality.</p>
        
        <div class="test-actions">
            <button class="test-button" onclick="openModal()">Open Quick Test Modal</button>
            <button class="test-button" onclick="testCollapsibleSections()">Test Collapsible Sections</button>
            <button class="test-button" onclick="clearDebugOutput()">Clear Debug Output</button>
        </div>
        
        <div class="debug-panel">
            <h3>Debug Output</h3>
            <div id="debug-output" class="debug-output">
                Console output will appear here...
            </div>
        </div>
        
        <div class="instructions">
            <h3>Testing Instructions</h3>
            <ol>
                <li><strong>Open Modal</strong>: Click "Open Quick Test Modal" to display the modal</li>
                <li><strong>Test Tabs</strong>: Click between Basic Config, Advanced, and Preview tabs</li>
                <li><strong>Test Collapsible Sections</strong>: Click section headers in Basic Config tab to expand/collapse</li>
                <li><strong>Check Scrolling</strong>: Verify all content is accessible and bottom buttons are visible</li>
                <li><strong>Test Validation</strong>: Fill in fields and check validation indicators</li>
                <li><strong>Debug</strong>: Use "Test Collapsible Sections" button to check section states</li>
            </ol>
            
            <h4>Expected Behavior</h4>
            <ul>
                <li>Modal should open without bottom cutoff issues</li>
                <li>All content should be scrollable and accessible</li>
                <li>Collapsible sections should expand/collapse properly without immediately closing</li>
                <li>Tab navigation should work smoothly</li>
                <li>Form validation should provide visual feedback</li>
            </ul>
        </div>
    </div>

    <!-- Include the modal template -->
    {% load static %}
    {% include 'admin_tools/modals/quick_test_config_modal.html' %}
    
    <!-- Include required scripts -->
    <script src="{% static 'admin/js/quick_test.js' %}"></script>
    <script src="{% static 'admin/js/quick_test_modal_enhancements.js' %}"></script>
    
    <script>
        // Override console.log to display in debug panel
        const originalConsoleLog = console.log;
        const debugOutput = document.getElementById('debug-output');
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            if (debugOutput) {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                debugOutput.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
        };
        
        function openModal() {
            console.log('Opening quick test modal...');
            const modal = document.getElementById('quick-test-config-modal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('Modal opened successfully');
            } else {
                console.error('Modal not found!');
            }
        }
        
        function testCollapsibleSections() {
            console.log('Testing collapsible sections...');
            if (window.testCollapsibleSections) {
                window.testCollapsibleSections();
            } else {
                console.error('testCollapsibleSections function not available');
            }
        }
        
        function clearDebugOutput() {
            if (debugOutput) {
                debugOutput.innerHTML = 'Debug output cleared...<br>';
            }
        }
        
        // Initialize modal close functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            const modal = document.getElementById('quick-test-config-modal');
            if (modal) {
                // Close modal when clicking outside or on close button
                modal.addEventListener('click', function(e) {
                    if (e.target === modal || e.target.classList.contains('close')) {
                        modal.style.display = 'none';
                        console.log('Modal closed');
                    }
                });
                
                // Close modal on Escape key
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && modal.style.display === 'flex') {
                        modal.style.display = 'none';
                        console.log('Modal closed with Escape key');
                    }
                });
            }
        });
    </script>
</body>
</html>
