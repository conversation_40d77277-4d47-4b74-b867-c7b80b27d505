# GG TODO

## Missing Tool: get_user_state

**Issue**: Tool code 'get_user_state' not found in database or is inactive.

**Analysis**:
- Referenced in `conversation_dispatcher.py` line 1179 to enrich user context
- Listed as required tool for DISPATCHER agent role in `cmd_tool_connect.py` line 27
- Expected to return user state information that gets merged with extracted message context
- **Never actually implemented** - no function decorated with `@register_tool('get_user_state')` exists

**Impact**:
- System falls back to less optimal context extraction
- May contribute to behavioral differences between environments
- Dispatcher handles failure gracefully, so system continues working

**Current Workarounds**:
- Similar functionality exists in other tools:
  - `get_user_engagement_status` - analyzes user engagement metrics
  - `detect_emotional_state_shifts` - detects emotional changes
  - `analyze_mood_progression` - analyzes mood patterns
  - `get_user_profile` - gets basic user profile data

**Solution Options**:
1. Implement missing `get_user_state` tool combining relevant user state info
2. Remove references if tool not needed
3. Modify dispatcher to use existing tools like `get_user_engagement_status`

## Database Security Rules Relaxed

**Note**: Database security rules have been relaxed to allow build-time database access.

**Context**:
- DigitalOcean App Platform build environment previously couldn't access the database
- This was causing deployment failures during migration attempts in build phase
- Security rules were relaxed to enable database connectivity during builds

**Considerations**:
- Monitor for any security implications
- Consider if this is a temporary or permanent change
- May want to review and tighten rules once build process is stable

**Date**: 2025-07-07

**Priority**: Medium - affects context quality but system remains functional

## Cleanup Worker Configuration

**Issue**: do.yaml contains a worker service configuration that doesn't appear in DigitalOcean UI

**Context**:
- do.yaml defines a separate Celery worker service (lines 89-125)
- DigitalOcean UI only shows 3 components: goali-backend, goali-frontend, static-files
- No separate worker component is visible or active
- Celery appears to be running within the Django backend service instead

**Action needed**: Either:
1. Properly configure and deploy a dedicated Celery worker service, OR
2. Remove the worker configuration from do.yaml and document that Celery runs within Django backend

**Considerations**:
- Current setup works but creates confusion about architecture
- Separate worker service could improve resource allocation and scaling
- Need to verify if background tasks (wheel generation, etc.) actually need dedicated workers

**Priority**: Medium - affects deployment clarity and resource allocation

**Date**: 2025-07-07

## Production Authentication Issues - Track Event API

**Issue**: `/api/track-event/` endpoint returning 401 Unauthorized errors in production

**Log Evidence**:
```
INFO: 176.176.151.211:0 - "POST /api/track-event/ HTTP/1.1" 401 Unauthorized
```

**Root Cause Analysis**:
- EventTrackingView requires authentication (line 1063: "Authentication required")
- Frontend uses session-based authentication (`credentials: 'include'`)
- Production environment (DEBUG=False) doesn't allow debug user impersonation
- Users are not properly authenticated when accessing the application
- Bearer token authentication is not implemented (line 1054-1055)

**Authentication Flow Issues**:
1. Frontend sends tracking events automatically (app-shell.ts line 5369)
2. Production requires valid Django session or Bearer token
3. Users may not be going through proper login flow
4. Session cookies may not be properly set/maintained

**Impact**:
- User activity tracking fails in production
- Analytics and user behavior data is lost
- May affect user experience features that depend on tracking

**Solution Options**:
1. **Fix authentication flow**: Ensure users are properly logged in before tracking events
2. **Implement Bearer token auth**: Complete the Bearer token implementation (currently returns 401)
3. **Make tracking optional**: Allow anonymous tracking for non-critical events
4. **Debug authentication**: Add logging to understand why sessions aren't working

**Priority**: High - affects user experience and data collection

**Date**: 2025-07-07

## ✅ RESOLVED: Celery Configuration Mismatch (2025-07-07)

**Issue**: Backend service and celery-worker were connecting to different Redis instances causing workflow tasks to hang

**Root Cause**:
- Backend was trying to connect to `localhost` for Celery broker instead of managed Redis
- Celery-worker was correctly connected to DigitalOcean managed Redis instance
- Environment variable configuration mismatch between services

**Resolution**:
- Secret rotation in DigitalOcean UI fixed the configuration mismatch
- All services now properly connect to the same Redis instance (`rediss://default:**@goali-valkey-do-user-23397-0.k.db.ondigitalocean.com:25061/0`)
- Wheel generation workflow now completes successfully end-to-end

**Evidence**:
- Task `3224c71d-f655-4102-b810-bc31c40b29d0` completed successfully with 6 personalized activities
- Real-time WebSocket updates working properly
- No more `kombu.exceptions.OperationalError: [Errno 111] Connection refused` errors

**Status**: Production system fully operational with comprehensive debug logging

## 🔐 URGENT: Security - Secret Rotation Required

**Issue**: Accidentally exposed Redis credentials in Git history during troubleshooting

**Exposed Secrets**:
- `REDIS_URL` with full connection string including credentials
- Committed in attempt to fix Celery configuration mismatch

**Required Actions**:
1. **Immediate**: Rotate all DigitalOcean managed Redis credentials
2. **Review**: Check Git history for any other exposed secrets
3. **Update**: All affected services and applications with new credentials
4. **Process**: Use DigitalOcean UI exclusively for secret management

**Important Notes**:
- CLI manifest updates can create inconsistent state where secrets appear in UI but aren't available at runtime
- Always use DigitalOcean UI for environment variable and secret management
- Never commit credentials to version control

**Priority**: CRITICAL - Security vulnerability

**Date**: 2025-07-07