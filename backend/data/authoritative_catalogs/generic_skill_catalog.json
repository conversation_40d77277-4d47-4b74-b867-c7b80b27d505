{"metadata": {"version": "1.0.0", "generated_from": "user_profile_catalog.json extraction", "last_updated": "2025-07-09", "description": "Authoritative catalog of GenericSkill objects and their relationships, extracted from user_profile_catalog.json", "source_files": ["user_profile_catalog.json"], "total_generic_skills": 40, "total_skill_domain_relationships": 0, "total_skill_trait_relationships": 0}, "generic_skills": [{"code": "creative_writing", "name": "Creative Writing", "description": "Ability to craft engaging written content with originality and expressiveness.", "tags": ["creative", "communication", "artistic"]}, {"code": "public_speaking", "name": "Public Speaking", "description": "Ability to deliver clear, compelling presentations to audiences of various sizes.", "tags": ["communication", "social", "professional"]}, {"code": "problem_solving", "name": "Problem Solving", "description": "Ability to identify challenges, analyze possible solutions, and implement effective approaches.", "tags": ["cognitive", "analytical", "professional"]}, {"code": "visual_art", "name": "Visual Art Creation", "description": "Ability to express ideas, emotions, and concepts through visual media and techniques.", "tags": ["creative", "artistic", "visual"]}, {"code": "physical_fitness", "name": "Physical Fitness Training", "description": "Knowledge and ability to develop physical capabilities through targeted exercise and recovery.", "tags": ["physical", "health", "lifestyle"]}, {"code": "emotional_intelligence", "name": "Emotional Intelligence", "description": "Ability to recognize, understand, and manage emotions in oneself and others.", "tags": ["social", "emotional", "interpersonal"]}, {"code": "programming", "name": "Computer Programming", "description": "Ability to design, write, test, and maintain computer code to create software.", "tags": ["technical", "digital", "professional"]}, {"code": "leadership", "name": "Leadership", "description": "Ability to guide, inspire, and coordinate groups toward shared objectives.", "tags": ["social", "professional", "management"]}, {"code": "meditation", "name": "Meditation Practice", "description": "Ability to cultivate focused attention, awareness, and mental clarity through structured mindfulness techniques.", "tags": ["mindfulness", "emotional", "spiritual"]}, {"code": "negotiation", "name": "Negotiation", "description": "Ability to reach agreements through effective communication, understanding of interests, and strategic problem-solving.", "tags": ["social", "professional", "communication"]}, {"code": "communication", "name": "Core Communication", "description": "Core verbal and written communication abilities", "tags": ["communication", "social", "professional"]}, {"code": "soft_communication", "name": "Advanced Interpersonal Communication", "description": "Advanced interpersonal communication nuances", "tags": ["communication", "social", "interpersonal"]}, {"code": "soft_empathy", "name": "Empathy", "description": "Understanding and responding to others' emotions", "tags": ["social", "emotional", "interpersonal"]}, {"code": "soft_conflict_resolution", "name": "Conflict Resolution", "description": "Managing disagreements constructively", "tags": ["social", "professional", "communication"]}, {"code": "soft_leadership", "name": "Leadership Skills", "description": "Guiding and motivating others effectively", "tags": ["social", "professional", "management"]}, {"code": "soft_presentation", "name": "Presentation Skills", "description": "Public speaking and formal presentation skills", "tags": ["communication", "social", "professional"]}, {"code": "soft_networking", "name": "Networking", "description": "Building and maintaining professional relationships", "tags": ["social", "professional", "communication"]}, {"code": "soft_active_listening", "name": "Active Listening", "description": "Focused attention and responsive listening skills", "tags": ["communication", "social", "interpersonal"]}, {"code": "soft_persuasion", "name": "Persuasion", "description": "Influencing others through reasoning and appeal", "tags": ["communication", "social", "professional"]}, {"code": "soft_cultural_competence", "name": "Cultural Competence", "description": "Understanding and working across cultural differences", "tags": ["social", "communication", "interpersonal"]}, {"code": "soft_team_collaboration", "name": "Team Collaboration", "description": "Working effectively within group dynamics", "tags": ["social", "professional", "teamwork"]}, {"code": "soft_introspection", "name": "Introspection", "description": "Self-awareness and reflection capabilities", "tags": ["emotional", "psychological", "self-development"]}, {"code": "soft_emotional_regulation", "name": "Emotional Regulation", "description": "Managing one's own emotional responses", "tags": ["emotional", "psychological", "self-management"]}, {"code": "soft_stress_management", "name": "Stress Management", "description": "Coping with pressure and high-demand situations", "tags": ["emotional", "psychological", "health"]}, {"code": "soft_resilience", "name": "Resilience", "description": "Bouncing back from setbacks and challenges", "tags": ["emotional", "psychological", "self-development"]}, {"code": "soft_mindfulness", "name": "Mindfulness", "description": "Present-moment awareness and attention practices", "tags": ["emotional", "psychological", "spiritual"]}, {"code": "soft_therapeutic", "name": "Therapeutic Skills", "description": "Supporting others through emotional difficulties", "tags": ["social", "emotional", "helping"]}, {"code": "soft_meditation", "name": "Meditation", "description": "Contemplative practices for mental clarity", "tags": ["emotional", "psychological", "spiritual"]}, {"code": "soft_grief_processing", "name": "Grief Processing", "description": "Navigating loss and life transitions", "tags": ["emotional", "psychological", "therapeutic"]}, {"code": "soft_philosophical", "name": "Philosophical Thinking", "description": "Abstract thinking and questioning fundamental concepts", "tags": ["intellectual", "analytical", "reflective"]}, {"code": "soft_critical_thinking", "name": "Critical Thinking", "description": "Analyzing information objectively and systematically", "tags": ["intellectual", "analytical", "cognitive"]}, {"code": "soft_problem_solving", "name": "Problem Solving", "description": "Breaking down complex challenges into manageable parts", "tags": ["intellectual", "analytical", "cognitive"]}, {"code": "soft_research", "name": "Research Skills", "description": "Finding, evaluating, and synthesizing information", "tags": ["intellectual", "analytical", "academic"]}, {"code": "soft_strategic_thinking", "name": "Strategic Thinking", "description": "Long-term planning and systems perspective", "tags": ["intellectual", "analytical", "professional"]}, {"code": "soft_mathematical", "name": "Mathematical Reasoning", "description": "Numerical reasoning and quantitative analysis", "tags": ["intellectual", "analytical", "technical"]}, {"code": "soft_scientific", "name": "Scientific Thinking", "description": "Hypothesis formation and experimental thinking", "tags": ["intellectual", "analytical", "scientific"]}, {"code": "tech_ai_concepts", "name": "AI Concepts", "description": "Understanding AI, machine learning, and automation", "tags": ["technical", "digital", "modern"]}, {"code": "tech_coding_python", "name": "Python Programming", "description": "Programming in Python language", "tags": ["technical", "digital", "programming"]}, {"code": "tech_coding_javascript", "name": "JavaScript Programming", "description": "Programming in JavaScript language", "tags": ["technical", "digital", "programming"]}, {"code": "tech_coding_web", "name": "Web Development", "description": "HTML, CSS, web development fundamentals", "tags": ["technical", "digital", "programming"]}], "skill_domain_relationships": [], "skill_trait_relationships": []}