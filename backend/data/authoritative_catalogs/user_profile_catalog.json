{"metadata": {"version": "1.0.0", "generated_from": "seeding_commands", "last_updated": "2025-06-29", "description": "Authoritative catalog of user profile components extracted from seeding files", "source_files": ["seed_db_10_hexacos.py", "seed_db_20_limitations.py", "seed_db_50_skill_system.py", "seed_db_60_beliefs.py"]}, "hexaco_traits": {"HONESTYHUMILITY": [{"code": "honesty_sincerity", "name": "Sincerity", "description": "Genuineness in self-expression and relationships without manipulation.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_fairness", "name": "Fairness", "description": "Tendency to avoid exploiting others for personal gain.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_greed_avoidance", "name": "Greed Avoidance", "description": "Level of disinterest in luxury, wealth, and social status.", "trait_type": "HONESTYHUMILITY"}, {"code": "honesty_modesty", "name": "Modesty", "description": "Tendency to be humble and unassuming about achievements.", "trait_type": "HONESTYHUMILITY"}], "EMOTIONALITY": [{"code": "emotion_fearfulness", "name": "Fearfulness", "description": "Tendency to experience fear in response to potential dangers.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_anxiety", "name": "Anxiety", "description": "Tendency to worry in a variety of contexts.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_dependence", "name": "Dependence", "description": "Need for emotional support and reassurance from others.", "trait_type": "EMOTIONALITY"}, {"code": "emotion_sentimentality", "name": "Sentimentality", "description": "Tendency to form strong emotional bonds and empathic responses.", "trait_type": "EMOTIONALITY"}], "EXTRAVERSION": [{"code": "extra_self_esteem", "name": "Social Self-Esteem", "description": "Confidence and positive self-evaluation in social contexts.", "trait_type": "EXTRAVERSION"}, {"code": "extra_social_boldness", "name": "Social Boldness", "description": "<PERSON><PERSON><PERSON> in a variety of social situations and leadership roles.", "trait_type": "EXTRAVERSION"}, {"code": "extra_sociability", "name": "Sociability", "description": "Enjoyment of social gatherings and interactions with others.", "trait_type": "EXTRAVERSION"}, {"code": "extra_liveliness", "name": "Liveliness", "description": "Energy level and enthusiasm in social and activity contexts.", "trait_type": "EXTRAVERSION"}], "AGREEABLENESS": [{"code": "agree_forgiveness", "name": "Forgiveness", "description": "Willingness to trust and forgive those who have caused harm.", "trait_type": "AGREEABLENESS"}, {"code": "agree_gentleness", "name": "Gentleness", "description": "Tendency to be mild and lenient in interactions with others.", "trait_type": "AGREEABLENESS"}, {"code": "agree_flexibility", "name": "Flexibility", "description": "Willingness to compromise and cooperate with others.", "trait_type": "AGREEABLENESS"}, {"code": "agree_patience", "name": "Patience", "description": "Tendency to remain calm rather than becoming angry.", "trait_type": "AGREEABLENESS"}], "CONSCIENTIOUSNESS": [{"code": "consc_organization", "name": "Organization", "description": "Tendency to seek order and structure in the physical environment.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_diligence", "name": "Diligence", "description": "Work ethic and persistence in pursuing goals.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_perfectionism", "name": "Perfectionism", "description": "Thoroughness and concern with details and accuracy.", "trait_type": "CONSCIENTIOUSNESS"}, {"code": "consc_prudence", "name": "Prudence", "description": "Tendency to deliberate carefully and inhibit impulses.", "trait_type": "CONSCIENTIOUSNESS"}], "OPENNESS": [{"code": "open_aesthetic", "name": "Aesthetic Appreciation", "description": "Enjoyment of beauty in art, music, and nature.", "trait_type": "OPENNESS"}, {"code": "open_inquisitive", "name": "Inquisitiveness", "description": "Interest in exploring new ideas and understanding complex concepts.", "trait_type": "OPENNESS"}, {"code": "open_creativity", "name": "Creativity", "description": "Preference for innovation and experimentation.", "trait_type": "OPENNESS"}, {"code": "open_unconventional", "name": "Unconventionality", "description": "Willingness to accept the unusual and challenge tradition.", "trait_type": "OPENNESS"}]}, "limitations": {"PHYSICAL": [{"code": "phys_mobility_general", "description": "General mobility limitations affecting overall movement", "limitation_type": "PHYSICAL"}, {"code": "phys_mobility_upper", "description": "Limited mobility in upper body or arms", "limitation_type": "PHYSICAL"}, {"code": "phys_mobility_lower", "description": "Limited mobility in lower body or legs", "limitation_type": "PHYSICAL"}, {"code": "phys_stamina", "description": "Limited physical stamina or endurance", "limitation_type": "PHYSICAL"}, {"code": "phys_strength", "description": "Limited physical strength", "limitation_type": "PHYSICAL"}, {"code": "phys_balance", "description": "Balance or coordination challenges", "limitation_type": "PHYSICAL"}, {"code": "phys_dexterity", "description": "Reduced fine motor skills or dexterity", "limitation_type": "PHYSICAL"}, {"code": "phys_vision", "description": "Visual impairment", "limitation_type": "PHYSICAL"}, {"code": "phys_hearing", "description": "Hearing impairment", "limitation_type": "PHYSICAL"}, {"code": "phys_speech", "description": "Speech or communication difficulties", "limitation_type": "PHYSICAL"}, {"code": "phys_respiration", "description": "Respiratory limitations (asthma, COPD, etc.)", "limitation_type": "PHYSICAL"}, {"code": "phys_cardiovascular", "description": "Cardiovascular limitations", "limitation_type": "PHYSICAL"}, {"code": "phys_chronic_pain", "description": "Chronic pain condition", "limitation_type": "PHYSICAL"}], "COGNITIVE": [{"code": "cog_attention", "description": "Attention or focus difficulties", "limitation_type": "COGNITIVE"}, {"code": "cog_processing", "description": "Information processing speed limitations", "limitation_type": "COGNITIVE"}, {"code": "cog_memory", "description": "Memory or recall challenges", "limitation_type": "COGNITIVE"}, {"code": "cog_executive", "description": "Executive function limitations", "limitation_type": "COGNITIVE"}, {"code": "cog_learning", "description": "Learning or comprehension challenges", "limitation_type": "COGNITIVE"}, {"code": "cog_literacy", "description": "Reading or writing difficulties", "limitation_type": "COGNITIVE"}, {"code": "cog_math", "description": "Mathematical or numerical processing limitations", "limitation_type": "COGNITIVE"}], "PSYCHOLOGICAL": [{"code": "psych_anxiety", "description": "Anxiety-related limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_depression", "description": "Depression-related limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_stress", "description": "Stress sensitivity or management difficulties", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_emotional_regulation", "description": "Emotional regulation challenges", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_social_anxiety", "description": "Social anxiety or discomfort", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_confidence", "description": "Self-confidence or self-esteem limitations", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_trauma", "description": "Trauma-related sensitivities", "limitation_type": "PSYCHOLOGICAL"}, {"code": "psych_motivation", "description": "Motivation or initiative difficulties", "limitation_type": "PSYCHOLOGICAL"}], "SOCIAL": [{"code": "social_communication", "description": "Social communication challenges", "limitation_type": "SOCIAL"}, {"code": "social_interpretation", "description": "Difficulty interpreting social cues", "limitation_type": "SOCIAL"}, {"code": "social_group", "description": "Discomfort in group settings", "limitation_type": "SOCIAL"}, {"code": "social_strangers", "description": "Difficulty interacting with unfamiliar people", "limitation_type": "SOCIAL"}, {"code": "social_conflict", "description": "Challenges handling social conflict or criticism", "limitation_type": "SOCIAL"}], "ENVIRONMENTAL": [{"code": "env_outdoor", "description": "Limitations in outdoor environments", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_noise", "description": "Sensitivity to loud or persistent noise", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_light", "description": "Sensitivity to bright or flashing lights", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_temperature", "description": "Sensitivity to temperature extremes", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_crowds", "description": "Discomfort in crowded spaces", "limitation_type": "ENVIRONMENTAL"}, {"code": "env_allergens", "description": "Environmental allergies or sensitivities", "limitation_type": "ENVIRONMENTAL"}], "TEMPORAL": [{"code": "time_morning", "description": "Difficulty with morning activities", "limitation_type": "TEMPORAL"}, {"code": "time_evening", "description": "Difficulty with evening activities", "limitation_type": "TEMPORAL"}, {"code": "time_duration", "description": "Limitations with extended duration activities", "limitation_type": "TEMPORAL"}, {"code": "time_regularity", "description": "Challenges maintaining regular schedules", "limitation_type": "TEMPORAL"}, {"code": "time_transitions", "description": "Difficulty with transitions between activities", "limitation_type": "TEMPORAL"}], "RESOURCE": [{"code": "res_financial", "description": "Financial resource limitations", "limitation_type": "RESOURCE"}, {"code": "res_transportation", "description": "Transportation access limitations", "limitation_type": "RESOURCE"}, {"code": "res_space", "description": "Limited physical space availability", "limitation_type": "RESOURCE"}, {"code": "res_equipment", "description": "Limited access to necessary equipment", "limitation_type": "RESOURCE"}, {"code": "res_digital", "description": "Limited digital device or internet access", "limitation_type": "RESOURCE"}, {"code": "res_support", "description": "Limited social or professional support", "limitation_type": "RESOURCE"}]}, "beliefs": {"SELF_WORTH": [{"code": "self_worth_inherent", "name": "Inherent Value", "description": "Belief that one has intrinsic value regardless of achievements or external validation.", "typical_stability": 70, "category": "SELF_WORTH"}, {"code": "self_worth_conditional", "name": "Conditional Self-Worth", "description": "Belief that one's value depends on meeting certain conditions, achievements, or standards.", "typical_stability": 75, "category": "SELF_WORTH"}, {"code": "self_worth_comparative", "name": "Comparative Value", "description": "Belief that one's worth is determined by comparison to others.", "typical_stability": 65, "category": "SELF_WORTH"}, {"code": "self_worth_permanent", "name": "Permanence of Worth", "description": "Belief that one's value is stable and enduring rather than fluctuating based on circumstances.", "typical_stability": 80, "category": "SELF_WORTH"}], "SELF_EFFICACY": [{"code": "self_efficacy_general", "name": "General Self-Efficacy", "description": "Belief in one's overall ability to perform tasks and achieve goals across various domains.", "typical_stability": 65, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_domain", "name": "Domain-Specific Efficacy", "description": "Belief in one's ability to succeed in specific areas or types of tasks.", "typical_stability": 60, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_control", "name": "Control Beliefs", "description": "Beliefs about the degree of control one has over outcomes and life circumstances.", "typical_stability": 70, "category": "SELF_EFFICACY"}, {"code": "self_efficacy_resilience", "name": "Resilience Beliefs", "description": "Beliefs about one's ability to recover from setbacks and adapt to challenges.", "typical_stability": 65, "category": "SELF_EFFICACY"}], "IDENTITY": [{"code": "identity_core", "name": "Core Identity", "description": "Fundamental beliefs about who one is as a person, including essential traits and characteristics.", "typical_stability": 85, "category": "IDENTITY"}, {"code": "identity_role", "name": "Role Identity", "description": "Beliefs about one's purpose or function in various social contexts and relationships.", "typical_stability": 75, "category": "IDENTITY"}, {"code": "identity_malleability", "name": "Identity Malleability", "description": "Beliefs about whether one's identity is fixed or can change over time.", "typical_stability": 70, "category": "IDENTITY"}, {"code": "identity_integration", "name": "Identity Integration", "description": "Beliefs about how various aspects of identity connect or conflict with each other.", "typical_stability": 65, "category": "IDENTITY"}], "POTENTIAL": [{"code": "potential_capacity", "name": "Growth Capacity", "description": "Beliefs about the extent to which one can develop new abilities and qualities.", "typical_stability": 60, "category": "POTENTIAL"}, {"code": "potential_trajectory", "name": "Growth Trajectory", "description": "Beliefs about the pace and pattern of personal development over time.", "typical_stability": 55, "category": "POTENTIAL"}, {"code": "potential_limitation", "name": "Inherent Limitations", "description": "Beliefs about fundamental constraints on one's development or achievement.", "typical_stability": 75, "category": "POTENTIAL"}, {"code": "potential_agency", "name": "Growth Agency", "description": "Beliefs about one's ability to actively direct and shape personal development.", "typical_stability": 65, "category": "POTENTIAL"}], "SKILL_LEARNING": [{"code": "learning_intelligence", "name": "Intelligence Beliefs", "description": "Beliefs about whether intelligence is fixed or can be developed through effort.", "typical_stability": 70, "category": "SKILL_LEARNING"}, {"code": "learning_effort", "name": "Effort Value", "description": "Beliefs about the relationship between effort and achievement in skill development.", "typical_stability": 65, "category": "SKILL_LEARNING"}, {"code": "learning_mistakes", "name": "Learning from Mistakes", "description": "Beliefs about the role of failures and mistakes in the learning process.", "typical_stability": 60, "category": "SKILL_LEARNING"}, {"code": "learning_efficiency", "name": "Learning Efficiency", "description": "Beliefs about one's rate of skill acquisition relative to others or to personal expectations.", "typical_stability": 55, "category": "SKILL_LEARNING"}], "TALENT_INNATE": [{"code": "talent_inborn", "name": "Innate Talents", "description": "Beliefs about inherent abilities or predispositions for certain domains.", "typical_stability": 75, "category": "TALENT_INNATE"}, {"code": "talent_domain", "name": "Domain-Specific Talent", "description": "Beliefs about one's innate abilities in particular areas of activity.", "typical_stability": 70, "category": "TALENT_INNATE"}, {"code": "talent_discovery", "name": "Talent Discovery", "description": "Beliefs about how and when talents are identified or revealed.", "typical_stability": 60, "category": "TALENT_INNATE"}, {"code": "talent_development", "name": "Talent Development", "description": "Beliefs about the relationship between innate talent and deliberate practice.", "typical_stability": 65, "category": "TALENT_INNATE"}], "FEAR_PATTERN": [{"code": "fear_failure", "name": "Fear of Failure", "description": "Beliefs about the consequences and meaning of failure or defeat.", "typical_stability": 75, "category": "FEAR_PATTERN"}, {"code": "fear_rejection", "name": "Fear of Rejection", "description": "Beliefs about social exclusion, abandonment, or disapproval.", "typical_stability": 80, "category": "FEAR_PATTERN"}, {"code": "fear_uncertainty", "name": "Fear of Uncertainty", "description": "Beliefs about ambiguity, unpredictability, and the unknown.", "typical_stability": 70, "category": "FEAR_PATTERN"}, {"code": "fear_vulnerability", "name": "Fear of Vulnerability", "description": "Beliefs about the risks associated with emotional openness or authenticity.", "typical_stability": 75, "category": "FEAR_PATTERN"}], "MEANING": [{"code": "meaning_purpose", "name": "Life Purpose", "description": "Beliefs about one's unique contribution, role, or ultimate destination in life.", "typical_stability": 75, "category": "MEANING"}, {"code": "meaning_coherence", "name": "Life Coherence", "description": "Beliefs about the order, structure, and comprehensibility of life events.", "typical_stability": 70, "category": "MEANING"}, {"code": "meaning_significance", "name": "Personal Significance", "description": "Beliefs about the impact and importance of one's existence and actions.", "typical_stability": 80, "category": "MEANING"}, {"code": "meaning_transcendence", "name": "Transcendent Meaning", "description": "Beliefs about spiritual or existential dimensions of life's meaning.", "typical_stability": 85, "category": "MEANING"}]}}